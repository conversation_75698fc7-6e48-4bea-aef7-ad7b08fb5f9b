<template>
  <div class="user-center">
    <h1 class="title">账号信息</h1>
    <user-information class="user-information-container" ref="UserInformation"></user-information>
  </div>
</template>

<script>
import UserInformation from './UserInformation.vue';

export default {
  name: 'UserCenter',
  components: {
    UserInformation,
  },
  watch: {
    activeTab(val) {
      this.$router.replace({ query: { ...this.$route.query, activeTab: val } });
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.user-center {
  background: #fff;
  padding: 20px;
  .title {
    font-weight: 600;
    font-size: 16px;
    color: #00141a;
    line-height: 24px;
  }
}
.user-information-container {
  margin-left: 30px;
}
</style>
