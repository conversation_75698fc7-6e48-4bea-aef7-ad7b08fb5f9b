<template>
  <div class="user-edit">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '用户管理', query: $route.query }"> 用户管理 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item v-if="isEdit">
        <router-link :to="{ name: '用户详情', query: { ...$route.query, id: userId } }"> 用户详情：{{ userInfo.userName }} </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>{{ isEdit ? '编辑用户' : '新建用户' }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="main">
      <a-form :colon="false" class="form-content" ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <div class="form-title">{{ isEdit ? '编辑用户' : '新建用户' }}</div>
        <HeadTitle :title="infoTitle[0]" />
        <div class="base-info">
          <a-form-item class="sub-item" label="用户名" :wrapper-col="subWrapperCol" name="userName">
            <a-input placeholder="请输入" v-model:value="form.userName" :disabled="isEdit" />
          </a-form-item>
          <a-form-item v-if="!loginType.isOnlyPassword()" class="sub-item" label="手机号" :wrapper-col="subWrapperCol" name="phoneNum">
            <a-input placeholder="请输入" v-model:value="form.phoneNum" />
          </a-form-item>
          <a-form-item class="sub-item" label="姓名" :wrapper-col="subWrapperCol" name="fullName">
            <a-input placeholder="请输入" v-model:value="form.fullName" />
          </a-form-item>
          <a-form-item class="sub-item" label="邮箱" :wrapper-col="subWrapperCol" name="email">
            <a-input placeholder="请输入" v-model:value="form.email" />
          </a-form-item>
          <a-form-item v-if="!isEdit && !loginType.isOnlySmscode()" class="sub-item" label="密码" :wrapper-col="subWrapperCol" name="password">
            <a-input-password placeholder="请输入" autocomplete="new-password" v-model:value="form.password" />
          </a-form-item>
          <a-form-item v-if="!isEdit && !loginType.isOnlySmscode()" class="sub-item" label="确认密码" :wrapper-col="subWrapperCol" name="passwordConfirm">
            <a-input-password placeholder="请输入" autocomplete="new-password" v-model:value="form.passwordConfirm" />
          </a-form-item>
          <a-form-item v-if="!isEdit && isUseUserIdentity && !loginType.isOnlySmscode()" class="sub-item constraint-item" label="登录后强制修改密码" :wrapper-col="subWrapperCol">
            <a-switch v-model:checked="form.isPasswordTemporary" checked-children="开" un-checked-children="关" default-checked :disabled="form.password == ''" />
            <span>开启后，用户使用该密码登录后，将强制其再次设置新密码</span>
          </a-form-item>
          <a-form-item required class="sub-item school-item" :wrapper-col="subWrapperCol" label="所属组织">
            <a-space>
              <!-- 注：后端这边修改新建时传的是 select 的 名称 (label) 而不是 value 值，但是做联级查询时用到的 groupid 又需要 value   -->
              <a-form-item style="width: 231.5px" name="firstGroup">
                <a-select v-model:value="form.firstGroup" @change="firstGroupChange" placeholder="所属一级组织" show-search allowClear>
                  <a-select-option v-for="item in firstGroupOptions" :key="item.value" :value="item.label">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item style="width: 231.5px">
                <a-select v-model:value="form.secondGroup" @change="secondGroupChange" placeholder="所属二级组织" show-search allowClear>
                  <a-select-option v-for="item in secondGroupOptions" :key="item.value" :value="item.label">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item style="width: 231.5px">
                <a-select v-model:value="form.thirdGroup" @change="thirdGroupChange" placeholder="所属三级组织" show-search allowClear>
                  <a-select-option v-for="item in thirdGroupOptions" :key="item.value" :value="item.label">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item style="width: 231.5px">
                <a-input v-if="form.thirdGroup === '其他'" v-model:value="form.fourthGroup" placeholder="所属四级组织" />
                <a-select v-else v-model:value="form.fourthGroup" @change="fourGroupChange" placeholder="所属四级组织" show-search allowClear>
                  <a-select-option v-for="item in fourthGroupOptions" :key="item.value" :value="item.label">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-space>
          </a-form-item>
          <a-form-item v-if="isUseUserIdentity" class="sub-item" label="身份" :wrapper-col="subWrapperCol" name="identity">
            <a-radio-group v-model:value="form.identity" @change="changeIdentity">
              <a-radio value="教师"> 教师 </a-radio>
              <a-radio value="学生"> 学生 </a-radio>
              <a-radio value="开发者"> 开发者 </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item v-if="isUseUserIdentity" class="sub-item" style="position: relative" :wrapper-col="subWrapperCol">
            <img-uploader class="img-uploader" v-model:value="form.image"></img-uploader>
          </a-form-item>
          <a-form-item v-if="isUseUserIdentity && form.identity !== '开发者'" class="sub-item" label="学校" :wrapper-col="subWrapperCol">
            <a-space>
              <a-form-item style="width: 136px; margin: 0">
                <a-select v-model:value="form.area" show-search :options="areaOptions" placeholder="地区" @change="areaChange" />
              </a-form-item>
              <a-form-item style="width: 358px; margin: 0" name="school">
                <a-input v-if="form.area === '其他'" :placeholder="schoolePlaceholder" v-model:value="form.school" />
                <a-select v-else :placeholder="schoolePlaceholder" show-search v-model:value="form.school" :options="schoolOptions"></a-select>
              </a-form-item>
            </a-space>
          </a-form-item>
          <a-form-item v-if="form.identity == '开发者' && isUseUserIdentity" class="sub-item" label="工作单位" :wrapper-col="subWrapperCol">
            <a-space>
              <a-form-item style="width: 136px; margin: 0">
                <a-select v-model:value="form.area" show-search :options="areaOptions" placeholder="地区" @change="companyAreaChange" />
              </a-form-item>
              <a-form-item style="width: 358px; margin: 0" name="company">
                <a-input :disabled="form.area === undefined" placeholder="工作单位" v-model:value="form.company" />
              </a-form-item>
            </a-space>
          </a-form-item>
          <a-form-item v-if="isUseUserIdentity" class="sub-item" :wrapper-col="subWrapperCol"></a-form-item>
          <a-form-item v-if="form.identity == '学生' && isUseUserIdentity" class="sub-item" label="专业" :wrapper-col="subWrapperCol" name="major">
            <a-input class="public-width" placeholder="请输入" v-model:value="form.major" />
          </a-form-item>
          <a-form-item v-if="form.identity == '学生' && isUseUserIdentity" class="sub-item" :wrapper-col="subWrapperCol"></a-form-item>
          <a-form-item v-if="(form.identity == '教师' || form.identity == '学生') && isUseUserIdentity" class="sub-item" label="院系" :wrapper-col="subWrapperCol" name="faculty">
            <a-input class="public-width" placeholder="请输入" v-model:value="form.faculty" />
          </a-form-item>
          <a-form-item v-if="form.identity == '学生' && isUseUserIdentity" class="sub-item" :wrapper-col="subWrapperCol"></a-form-item>
          <a-form-item v-if="form.identity == '学生' && isUseUserIdentity" class="sub-item" label="学号" :wrapper-col="subWrapperCol" name="stuNum">
            <a-input class="public-width" placeholder="请输入" v-model:value="form.stuNum" />
          </a-form-item>
          <a-form-item v-if="(form.identity == '学生' || form.identity == '教师') && isUseUserIdentity" class="sub-item" :wrapper-col="subWrapperCol"></a-form-item>
          <a-form-item v-if="isUseUserIdentity" class="introduction-item" label="简介" name="introduction">
            <a-textarea v-model:value="form.introduction" placeholder="请填写学校、职称、擅长领域等；简介信息将显示在课程注册的教师团队信息中" :rows="4" />
            <span class="count-area">{{ `${(form.introduction || '').length}/${maxCount}` }}</span>
          </a-form-item>
        </div>
        <p class="tip-item" v-if="isUseUserIdentity">以上基本信息将同步至个人中心</p>
        <div class="tip-item1"></div>
        <div class="role-info">
          <HeadTitle :title="infoTitle[1]" />
          <div style="margin-top: 17px">
            <a-form-item class="sub-item" label=" " :wrapper-col="subWrapperCol" name="myrole">
              <a-transfer :show-select-all="false" @change="handleChange" v-model:target-keys="targetKeys" :titles="['可配置角色', '现有角色']" :data-source="dataSource">
                <template #children="{ direction, selectedKeys, onItemSelect }">
                  <!-- 搜索框 -->
                  <div v-if="direction === 'left'">
                    <a-input v-model:value="searchQuery" placeholder="快速检索" class="input_search" />
                    <a-tree
                      checkable
                      :expanded-keys="expandedKeys"
                      @expand="handleExpand"
                      :checked-keys="[...selectedKeys, ...targetKeys]"
                      :tree-data="treeData"
                      @check="
                        (_, props) => {
                          onChecked(props, [...selectedKeys, ...targetKeys], onItemSelect);
                        }
                      "
                    >
                    </a-tree>
                  </div>
                  <div v-else-if="direction === 'right'">
                    <a-input v-model:value="searchQueryRight" placeholder="快速检索" class="input_search" />
                    <a-tree
                      checkable
                      :expanded-keys="expandedKeysRight"
                      @expand="handleExpandRight"
                      :tree-data="rightTargetKeysData"
                      @check="
                        (_, props) => {
                          handleRightChecked(props, [...selectedKeys, ...targetKeys], onItemSelect);
                        }
                      "
                    />
                  </div>
                </template>
              </a-transfer>
            </a-form-item>
          </div>
        </div>
      </a-form>

      <a-button @click="formFinish" type="primary" class="confirm-btn">确定</a-button>
      <a-button @click="goBack" class="cancel-btn">取消</a-button>
    </div>
    <div v-if="editLoading" class="edit-loading">
      <a-spin />
    </div>
  </div>
</template>
<script lang="ts">
import { computed, defineComponent, ref } from 'vue';
import { message, Empty } from 'ant-design-vue';
import _ from 'lodash';
import HeadTitle from './components/headTitle/index.vue';
import ImgUploader from './components/imgUploader.vue';
import request from '@/request';
import { isLoginType } from './auth';
import { getEnvConfig } from '@/config';
import { getClientList, labelForValue } from './utils/baseTool';
import { setPassword } from './utils/encryptStr';
import cloneDeep from 'lodash/cloneDeep';
import { pwdRegex } from '@/constants/regex';

export default defineComponent({
  components: {
    HeadTitle,
    ImgUploader,
  },

  setup() {
    let tData = ref([]);
    let transferDataSource = ref([]);
    let getpresentname = ref([]);
    let expandedKeys = ref([]);
    let getTitle;
    let cloneData = ref([]);
    // 用户输入的搜索查询
    const searchQuery = ref('');
    const targetKeys = ref([]);
    const dataSource = ref(transferDataSource.value);
    const treeData = computed(() => {
      if (!searchQuery.value) {
        // 如果没有搜索查询，则返回完整的树形结构数据源
        return handleTreeData(tData.value, targetKeys.value);
      } else {
        // 否则，返回过滤后的树形结构数据
        // 这里需要实现一个递归函数来过滤树节点
        return filterTreeNodes(tData.value, searchQuery.value, targetKeys.value);
      }
    });
    // 递归过滤树节点的函数
    const filterTreeNodes = (nodes, query, targetKeys = []) => {
      let res = handleTreeData(nodes, targetKeys);
      return res
        .map((node) => {
          // 创建一个新节点对象，避免直接修改原始数据
          const newNode = { ...node };
          if (newNode.title.toLowerCase().includes(query.toLowerCase())) {
            // 如果节点标题包含查询字符串，则保留其子节点（如果有的话）
            if (newNode.children) {
              // newNode.children = filterTreeNodes(newNode.children, query);
              // 如果没有匹配的子节点，则移除children属性（可选）
            }
            return newNode; // 返回匹配的节点
          } else if (newNode.children) {
            // 如果节点标题不匹配但包含子节点，则递归过滤子节点
            const filteredChildren = filterTreeNodes(newNode.children, query, targetKeys);
            if (filteredChildren.length > 0) {
              // 如果找到匹配的子节点，则保留父节点并更新其子节点
              newNode.children = filteredChildren;
              return newNode; // 返回包含匹配子节点的父节点
            }
          }
          // 如果节点及其子节点都不匹配，则返回null（用于过滤）
          return null;
        })
        .filter((node) => node !== null); // 移除null值
    };
    const onChecked = (e, checkedKeys, onItemSelect) => {
      let { children, parent, key } = e.node;
      if (e.checked) {
        if (parent) {
          onItemSelect(key, true);
        } else if (children.length > 0) {
          children.forEach((element) => {
            if (!element.disabled) {
              onItemSelect(element.key, true);
            }
          });
        }
      } else {
        if (parent) {
          onItemSelect(key, false);
        } else if (children.length > 0) {
          children.forEach((element) => {
            onItemSelect(element.key, false);
          });
        }
      }
    };

    function flatten(list = []) {
      list.forEach((item) => {
        if (!item.children) {
          transferDataSource.value.push(item);
        }
        flatten(item.children);
      });
    }
    function handleTreeData(treeNodes, targetKeys = []) {
      let res = treeNodes.map(({ children, ...props }) => ({
        ...props,
        disabled: targetKeys.includes(props.key),
        children: handleTreeData(children ?? [], targetKeys),
      }));
      return res;
    }
    // 转换首字母key 为汉字
    async function filterTitleData(res) {
      const newObj = {};
      let roleListName = await getClientList();
      Object.keys(res).forEach((key) => {
        const match = roleListName.find((itemValue) => itemValue.code === key);
        if (match) {
          const newkey = match.name;
          getpresentname.value.push(match.name);
          newObj[newkey] = res[key];
        }
      });
      return newObj;
    }

    // 获取可选角色
    async function getRoleList() {
      await request('/web/admin/um/v1/user/role/available', { method: 'GET', data: {} }).then(async (res: any) => {
        if (res.msg == 'OK' && res.data != null) {
          let newObj = await filterTitleData(res.data);
          tData.value = await filterTreeData(newObj);
          cloneData.value = await filterTreeData(newObj);
          getTitle = Object.keys(newObj);
          expandedKeys.value = getAllKeys(tData.value);
          flatten(JSON.parse(JSON.stringify(tData.value)));
        }
      });
    }
    const getAllKeys = (nodes) => {
      return nodes.reduce((keys, node) => {
        keys.push(node.key);
        if (node.children) {
          keys.push(...getAllKeys(node.children));
        }
        return keys;
      }, []);
    };
    // 展开关闭
    function handleExpand(keys, { node }) {
      const isExpanded = expandedKeys.value.includes(node.key);
      if (isExpanded) {
        // 如果节点已展开，则收起它（从 expandedKeys 中移除 key）
        expandedKeys.value = expandedKeys.value.filter((k) => k !== node.key);
      } else {
        // 如果节点未展开，则展开它（将 key 添加到 expandedKeys 中）
        expandedKeys.value = [...expandedKeys.value, node.key];
      }
    }
    function removeTitle(arrtilte) {
      return arrtilte.filter((item) => !getTitle.includes(item));
    }
    // 转换数据为树形结构
    function filterTreeData(data) {
      const filterData = Object.keys(data).map((key) => ({
        key,
        title: key,
        children: data[key].map((item) => ({
          key: item.roleId,
          title: item.roleName,
          platformCode: item.platformCode,
        })),
      }));
      return filterData;
    }
    const searchTree = (tree, keyword, includeChildren = false) => {
      const newTree = [];
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.title.includes(keyword)) {
          // 如果当前节点符合条件，则将其复制到新的树形结构中，并根据 includeChildren 参数决定是否将其所有子节点也复制到新的树形结构中
          newTree.push({ ...node, children: includeChildren ? searchTree(node.children || [], '', true) : [] });
        } else if (node.children) {
          // 如果当前节点不符合条件且存在子节点，则递归遍历子节点，以继续搜索
          const result = searchTree(node.children, keyword, true);
          if (result.length > 0) {
            // 如果子节点中存在符合条件的节点，则将其复制到新的树形结构中
            newTree.push({ ...node, children: result });
          }
        }
      }
      return newTree;
    };
    const handleSearch = async (dir, value) => {
      let cloneData = cloneDeep(tData.value);
      if (value) {
        await searchTree(cloneData, value, true);
      }
    };
    const handleRightChecked = (e, checkedKeys, onItemSelect) => {
      let { children, parent, key } = e.node;
      if (e.checked) {
        if (parent) {
          onItemSelect(key, true);
        } else if (children.length > 0) {
          children.forEach((element) => {
            onItemSelect(element.key, true);
          });
        }
      } else {
        if (parent) {
          onItemSelect(key, false);
        } else if (children.length > 0) {
          children.forEach((element) => {
            onItemSelect(element.key, false);
          });
        }
      }
    };
    getRoleList();
    return {
      targetKeys,
      removeTitle,
      dataSource,
      treeData,
      onChecked,
      handleSearch,
      getpresentname,
      filterTitleData,
      filterTreeData,
      handleRightChecked,
      searchQuery,
      filterTreeNodes,
      tData,
      expandedKeys,
      handleExpand,
      cloneData,
      getAllKeys,
    };
  },

  data() {
    return {
      editLoading: false,
      userId: this.$route.query.id,
      userInfo: {} as any,
      isEdit: this.$route.query.flag === '1',
      infoTitle: ['基本信息', '角色信息'],
      labelCol: { span: 4 },
      wrapperCol: { span: 10 },
      subWrapperCol: { span: 14 },
      rightTargetKeys: [],
      searchQueryRight: '',
      form: {
        userName: '',
        phoneNum: '',
        fullName: '',
        email: '',
        password: '',
        passwordConfirm: '',
        isPasswordTemporary: true, //是否强制修改
        firstGroup: undefined,
        secondGroup: undefined,
        thirdGroup: undefined,
        fourthGroup: undefined,
        image: '',
        identity: '学生',
        school: undefined,
        company: '',
        faculty: '',
        major: '',
        stuNum: '',
        introduction: '',
        area: '',
      },
      maxCount: 30,
      firstGroupOptions: [] as any,
      secondGroupOptions: [] as any,
      thirdGroupOptions: [] as any,
      fourthGroupOptions: [] as any,
      areaOptions: [],
      schoolOptions: [],
      roleList: [],
      countNum: 0,
      searchValue: '',
      autoExpandParent: true,
      simpleImage: '' as any,
      setGroupId: [],
      saveTargetData: [],
      expandedKeysRight: [],
    };
  },
  created() {
    this.getUserInfo();
    this.getAreaOptions();
    this.getFirstGroup().then((data) => {
      this.firstGroupOptions = data;
    });
    if (this.isEdit) {
      this.getUserRoleList();
      this.getRightList();
    }
    this.simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  },
  computed: {
    schoolePlaceholder(this: any) {
      const placeholderMap = new Map([
        ['教师', '就职学校'],
        ['学生', '就读学校'],
        ['开发者', '毕业学校'],
      ]);
      return placeholderMap.get(this.form.identity);
    },
    rules(this: any) {
      return {
        userName: [
          { required: true, message: '请输入', trigger: ['blur', 'change'] },
          {
            validator: (rule, value, callback) => {
              if (this.isEdit || value == '') {
                return Promise.resolve();
              } else {
                let reg = /^(?![0-9]+$)[\w]{6,20}$/;
                if (reg.test(value)) {
                  return Promise.resolve();
                } else {
                  return Promise.reject();
                }
              }
            },
            message: '6-20个字符，只能包含字母、下划线和数字，不支持全数字，用户名不可修改，请谨慎设置',
            trigger: ['blur', 'change'],
          },
        ],
        firstGroup: [{ required: true, message: '请选择所属组织', trigger: ['blur', 'change'] }],
        identity: [{ required: true, message: '请选择身份', trigger: ['blur', 'change'] }],
        fullName: [
          { required: false, message: '请输入', trigger: ['blur', 'change'] },
          { max: 30, min: 0, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { pattern: /^[\u4e00-\u9fa5 a-zA-Z]+$/, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
        ],
        password: [
          {
            required: !this.isUseUserIdentity,
            trigger: ['blur', 'change'],
          },
          {
            validator: (rule, value, callback) => {
              if (value === '' && this.isUseUserIdentity) {
                return Promise.resolve();
              }
              if (pwdRegex.test(value)) {
                return window.zxcvbn(value).score > 1 ? Promise.resolve() : Promise.reject('密码过于简单或存在安全风险，请修改');
              } else {
                // 为空的时候防止与required校验重复
                return Promise.reject(value === '' ? '' : '8-20个字符，必须包含大、小写字母和数字');
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
        passwordConfirm: [
          {
            required: !this.isUseUserIdentity,
            validator: (rule, value, callback) => {
              if (value === this.form.password) {
                return Promise.resolve();
              } else {
                return Promise.reject();
              }
            },
            message: '两次密码输入不一致',
            trigger: ['blur', 'change'],
          },
        ],
        school: [
          { required: false, message: '请选择/输入学校', trigger: ['blur', 'change'] },
          { min: 0, max: 50, message: '50个字符以内的中英文，可包含空格、小括号', trigger: ['blur', 'change'] },
          { pattern: /^[\u4e00-\u9fa5a-zA-Z\s()（）]*$/, message: '50个字符以内的中英文，可包含空格、小括号', trigger: ['blur', 'change'] },
        ],
        faculty: [
          { min: 0, max: 30, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { pattern: /^[\u4e00-\u9fa5 a-zA-Z]+$/, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
        ],
        major: [
          { min: 0, max: 30, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { pattern: /^[\u4e00-\u9fa5 a-zA-Z]+$/, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
        ],
        stuNum: [
          { min: 0, max: 20, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
          { pattern: /^\w+$/, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
        ],
        phoneNum: [
          { required: getEnvConfig('USE_USER_IDENTITY') === '1', message: '请输入', trigger: ['blur', 'change'] },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确格式手机号', trigger: ['blur', 'change'] },
        ],
        email: [
          { required: false, message: '请输入', trigger: ['blur', 'change'] },
          { pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/, message: '请输入正确格式邮箱', trigger: ['blur', 'change'] },
          { min: 6, max: 50, message: '6-50个字符', trigger: ['blur', 'change'] },
        ],
        introduction: [
          { required: false, message: '请输入', trigger: ['blur', 'change'] },
          { min: 0, max: 30, message: '不超过30个字符', trigger: ['blur', 'change'] },
        ],
        image: [{ required: true, message: '请上传头像', trigger: 'change' }],
        company: [
          { required: false, message: '请输入工作单位', trigger: ['blur', 'change'] },
          { min: 0, max: 50, message: '50字以内的中英文、空格', trigger: ['blur', 'change'] },
          { pattern: /^[\u4e00-\u9fa5 a-zA-Z]+$/, message: '50字以内的中英文、空格', trigger: ['blur', 'change'] },
        ],
      };
    },
    isUseUserIdentity() {
      return getEnvConfig('USE_USER_IDENTITY') === '1';
    },
    loginType() {
      return isLoginType;
    },
    rightTargetKeysData() {
      if (!this.searchQueryRight) {
        // 如果没有搜索查询，则返回完整的树形结构数据源
        return this.rightTargetKeys;
      } else {
        // 否则，返回过滤后的树形结构数据
        // 这里需要实现一个递归函数来过滤树节点
        return this.filterTreeNodes(this.rightTargetKeys, this.searchQueryRight);
      }
    },
  },
  watch: {
    // 当message变化时，这个函数就会被调用rightTargetKeys
    rightTargetKeys: {
      handler(newValue, oldValue) {
        // 判断如果 right 有数据那么 要去除掉左侧的数据
        if (newValue) {
          // 右侧若子数据全为空那么将 parent 字段也去除
          newValue.forEach((element, i) => {
            element.disabled = false;
            element.children.forEach((child) => {
              child.disabled = false;
            });
            if (element.children.length === 0) {
              newValue.splice(i, 1);
            }
          });
        }
      },
      deep: true,
      immediate: true,
    },
    tData: {
      handler(newValue, oldValue) {
        if (newValue) {
          // 左侧若子数据全为空那么将 parent 字段也去除
          newValue.forEach((element, i) => {
            if (element.children.length === 0) {
              newValue.splice(i, 1);
            }
          });
        }
      },
      deep: true,
      immediate: true,
    },
    treeData: {
      // 子集全选后父集设置成 disabled
      handler(newValue, oldValue) {
        if (newValue.length > 0) {
          newValue.forEach((item) => {
            if (item.children && item.children.length > 0) {
              let allChildrenDisabled = item.children.every((child) => child.disabled === true);
              if (allChildrenDisabled) {
                item.disabled = true;
              }
            }
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 获取全国地区列表
    getAreaOptions(this: any) {
      request('/web/admin/um/v1/user/province/list', { method: 'GET', data: {} }).then((res: any) => {
        this.areaOptions = res.data.map((area) => {
          return {
            value: area,
            label: area,
          };
        });
        this.areaOptions.push({
          value: '其他',
          label: '其他',
        });
      });
    },
    // 获取地区内学校列表
    getSchoolOptions() {
      request('/web/um/v1/province/school', { method: 'GET', data: { province: this.form.area } }).then((res: any) => {
        this.schoolOptions = res.data.map((school) => {
          return {
            value: school,
            label: school,
          };
        });
      });
    },
    async getFirstGroup(groupId = '') {
      try {
        const res = await request('/web/admin/um/v1/group/list-tree', { method: 'GET', data: { groupId } });
        if (res.msg === 'OK') {
          return res.data.map((firstGroupItem) => {
            return {
              value: firstGroupItem.groupId,
              label: firstGroupItem.groupName,
            };
          });
        }
      } catch (e) {
        return e;
      }
    },
    firstGroupChange(_val, raw) {
      this.form.secondGroup = undefined;
      this.secondGroupOptions = [];
      this.form.thirdGroup = undefined;
      this.thirdGroupOptions = [];
      this.form.fourthGroup = undefined;
      this.fourthGroupOptions = [];
      if (!_val) {
        this.setGroupId.pop();
      } else {
        this.setGroupId.push(raw.key);
        this.getSecondGroup(raw.key);
      }
    },
    async getSecondGroup(val) {
      await this.getFirstGroup(val).then((data) => {
        this.secondGroupOptions = data;
      });
    },
    secondGroupChange(_val, raw) {
      this.form.thirdGroup = undefined;
      this.thirdGroupOptions = [];
      this.form.fourthGroup = undefined;
      this.fourthGroupOptions = [];
      if (!_val) {
        this.setGroupId.pop();
      } else {
        this.getThirdGroup(raw.key);
        this.setGroupId.push(raw.key);
      }
    },
    getThirdGroup(val) {
      this.getFirstGroup(val).then((data) => {
        this.thirdGroupOptions = data;
      });
    },
    thirdGroupChange(_val, raw) {
      this.form.fourthGroup = undefined;
      this.fourthGroupOptions = [];
      if (!_val) {
        this.setGroupId.pop();
      } else {
        this.getFourthGroup(raw.key);
        this.setGroupId.push(raw.key);
      }
    },
    fourGroupChange(_val, raw) {
      if (!_val) {
        this.setGroupId.pop();
      } else {
        this.setGroupId.push(raw.key);
      }
    },
    getFourthGroup(val) {
      this.getFirstGroup(val).then((data) => {
        this.fourthGroupOptions = data;
      });
    },
    changeIdentity(e) {
      switch (e.target.value) {
        case '开发者':
          this.form.faculty = '';
          this.form.major = '';
          this.form.school = '';
          this.form.stuNum = '';
          break;
        default:
          this.form.major = '';
          this.form.stuNum = '';
          break;
      }
    },
    areaChange(val) {
      this.form.area = val;
      this.form.school = undefined;
      if (val !== '其他') {
        this.getSchoolOptions();
      }
    },
    companyAreaChange(val) {
      this.form.companyArea = val;
    },
    // 获取角色信息表数据
    async getUserRoleList(code = 'all') {
      this.getTabCode = code;
      await request('/web/admin/um/v1/user/role/list', {
        method: 'GET',
        data: {
          platformCode: code,
          userId: this.userId,
        },
      }).then((res: any) => {
        if (res.msg == 'OK' && res.data != null) {
          let roleFilterData = res.data.filter((item) => item.roleType !== 'common-user' && item.roleType !== 'admin');
          roleFilterData.forEach((res) => {
            this.targetKeys.push(res.roleId);
          });
        }
      });
    },
    // 获取基本信息
    async getUserInfo() {
      if (!this.isEdit) return;
      await request('/web/admin/um/v1/user/detail', { method: 'GET', data: { userId: this.userId } }).then(async (res: any) => {
        if (res.msg === 'OK') {
          this.userInfo = res.data;
          for (const key in this.form) {
            if (this.userInfo[key]) {
              this.form[key] = this.userInfo[key];
            }
          }
          await this.editfiltertGroupOptions(res.data);
          this.form.isPasswordTemporary = true;
        }
      });
    },
    /**
     * 编辑状态下后端只返回所属组织的字段名称 这里需要
     * 1.先手动将字段名称查找到对应的 value 值
     * 2.在通过检索的value值传入给公共 getFirstGroup(value) 让他查下一层
     * @param {Obejct} res - 传入的基本信息
     */
    async editfiltertGroupOptions(res) {
      let FirstData: any = {};
      let SecoundData: any = {};
      let ThirdData: any = {};
      let FourthData: any = {};
      let filterGroup = res.formatGroupPath.split('>').filter(Boolean);
      await this.getFirstGroup().then((data) => {
        this.firstGroupOptions = data;
        if (filterGroup[0]) {
          FirstData = labelForValue(data, filterGroup[0]);
          this.setGroupId.push(FirstData.value);
          this.form.firstGroup = FirstData.label;
        }
      });
      await this.getFirstGroup(FirstData.value).then((data) => {
        this.secondGroupOptions = data;
        if (filterGroup[1]) {
          SecoundData = labelForValue(data, filterGroup[1]);
          this.setGroupId.push(SecoundData.value);
          this.form.secondGroup = SecoundData.label;
        }
      });
      await this.getFirstGroup(SecoundData.value).then((data) => {
        this.thirdGroupOptions = data;
        if (filterGroup[2]) {
          ThirdData = labelForValue(data, filterGroup[2]);
          this.setGroupId.push(ThirdData.value);
          this.form.thirdGroup = ThirdData.label;
        }
      });

      await this.getFirstGroup(ThirdData.value).then((data) => {
        this.fourthGroupOptions = data;
        if (filterGroup[3]) {
          FourthData = labelForValue(data, filterGroup[3]);
          this.setGroupId.push(FourthData.value);
          this.form.fourthGroup = FourthData.label;
        }
      });
    },
    // 获取右侧可选角色
    async getRightList() {
      return await request('/web/admin/um/v1/user/role/updatable', { method: 'GET', data: { userId: this.userId } }).then(async (res: any) => {
        if (res.msg == 'OK' && res.data != null) {
          let newObj = await this.filterTitleData(res.data);
          this.rightTargetKeys = await this.filterTreeData(newObj);
          this.expandedKeysRight = this.getAllKeys(this.rightTargetKeys);
        }
      });
    },
    handleExpandRight(keys, { node }) {
      const isExpanded = this.expandedKeysRight.includes(node.key);
      if (isExpanded) {
        // 如果节点已展开，则收起它（从 expandedKeys 中移除 key）
        this.expandedKeysRight = this.expandedKeysRight.filter((k) => k !== node.key);
      } else {
        // 如果节点未展开，则展开它（将 key 添加到 expandedKeys 中）
        this.expandedKeysRight = [...this.expandedKeysRight, node.key];
      }
    },

    // 提交表单
    formFinish(this: any) {
      this.editLoading = true;
      let groupId = this.setGroupId;
      this.$refs.ruleForm
        .validate()
        // 表单校验成功
        .then(async () => {
          let requestUrl = '/web/admin/um/v1/user/create';
          this.form.isPasswordTemporary = this.isUseUserIdentity ? this.form.isPasswordTemporary : false; //深度学习平台不许强制登陆
          let password = await setPassword(this.form.password);
          let requestData = {
            ...this.form,
            userId: this.userId,
            groupId: groupId[groupId.length - 1],
            password,
            roleIds: this.removeTitle(this.targetKeys),
            identity: this.isUseUserIdentity ? this.form.identity : '', //身份
          };
          if (this.isEdit) {
            requestUrl = '/web/admin/um/v1/user/update';
          }
          this.form.passwordConfirm = '';
          request(requestUrl, {
            data: requestData,
            method: 'POST',
          }).then((res: any) => {
            if (res.msg == 'OK') {
              message.success(`${this.isEdit ? '编辑' : '新建'}用户成功`);
              setTimeout(() => {
                // 后端数据可能同步的没那么快，间隔后在跳转
                this.goBack();
              }, 1000);
            } else {
              this.editLoading = false;
              message.error(res.msg);
            }
          });
        })
        // 表单校验失败
        .catch((err) => {
          this.editLoading = false;
          this.$refs.ruleForm.scrollToField();
        });
    },
    goBack(this: any) {
      let goPath = { path: '/user-management', query: this.$route.query };
      if (this.isEdit) {
        goPath = { path: '/user-management/userControl-detail', query: { ...this.$route.query, id: this.userId } };
      }
      this.$router.push(goPath);
    },
    handleChange(keys, other, moveKeys) {
      if (other === 'right') {
        let cloneData = cloneDeep(this.treeData);
        let filteredData = cloneData.reduce((acc, item) => {
          if (moveKeys.includes(item.key)) {
            acc.push(item);
          } else if (item.children) {
            const filteredChildren = item.children.filter((child) => moveKeys.includes(child.key));
            if (filteredChildren.length > 0) {
              acc.push({ ...item, children: filteredChildren });
            }
          }
          return acc;
        }, []);
        filteredData.forEach((item) => {
          const index = this.rightTargetKeys.findIndex((bItem) => bItem.key === item.key);
          if (index === -1) {
            // 如果 mockDataB 中没有对应的项，则添加
            this.rightTargetKeys.push({ ...item });
          } else {
            // 如果 mockDataB 中有对应的项，则合并 children
            let res = [...this.rightTargetKeys[index].children, ...item.children];
            let uniqueTreeData = res.reduce((acc, node) => {
              // 检查累加器数组中是否已存在具有相同 key 的节点
              const existingNode = acc.find((n) => n.key === node.key);

              // 如果不存在，则添加到累加器数组中
              if (!existingNode) {
                acc.push(node);
              }

              // 返回累加器数组以供下一次迭代使用
              return acc;
            }, []);
            this.rightTargetKeys[index].children = uniqueTreeData;
          }
        });
      } else {
        this.rightTargetKeys.filter((item) => {
          if (item.children) {
            item.children = item.children.filter((child) => keys.includes(child.key));
          }
        });
      }
    },
  },
});
</script>
<style lang="less" scoped>
.user-edit {
  .nav {
    height: 58px;
    line-height: 58px;
    padding-left: 20px;
    background: #ffffff;
    font-weight: 500;
    color: #121f2c;
  }

  .main {
    min-width: 1254px;
    padding: 20px 20px 80px;
    margin: 20px;
    background: #ffffff;
    position: relative;

    .confirm-btn {
      width: 120px;
      margin: 28px 10px 0 150px;
    }

    .cancel-btn {
      width: 88px;
    }

    .form-content {
      position: relative;

      .form-title {
        font-size: 16px;
        font-weight: 500;
        color: #121f2c;
        margin-bottom: 24px;
      }
    }

    /deep/.ant-col-4 {
      min-width: 136px;
      max-width: 148px;
    }

    /deep/.ant-space-align-center {
      align-items: unset;
      vertical-align: top;
    }
  }

  .base-info {
    max-width: 1300px;
    margin: 20px 0 0;
    display: flex;
    flex-wrap: wrap;
    padding-left: 14px;
    position: relative;

    .sub-item {
      width: 600px;

      .public-width {
        width: 502px;
      }
    }

    .constraint-item {
      /deep/.ant-switch {
        vertical-align: top;
      }

      span {
        width: 315px;
        font-size: 12px;
        color: #ff8b00;
        position: absolute;
        top: 7px;
        left: 63px;
      }
    }

    .school-item {
      margin-right: 480px;
      margin-bottom: 0;

      /deep/ .ant-form-item-children {
        display: flex;
      }

      /deep/ .ant-form-item-label {
        line-height: 32px;
      }
    }

    .introduction-item {
      width: 800px;
      position: relative;

      /deep/.ant-form-item-control-wrapper {
        width: 502px;
      }

      /deep/.ant-input {
        width: 502px;
        height: 80px;
        resize: none;
      }

      /deep/.ant-col-10 {
        min-width: 502px;
      }

      .count-area {
        position: absolute;
        bottom: -21px;
        right: 0px;
      }
    }

    .img-uploader {
      position: absolute;
      top: 10px;
      right: -140px;
    }
  }

  .tip-item {
    color: #606972;
    padding-left: 150px;
  }

  .tip-item1 {
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #efefef;
    margin: 0 0 40px;
  }

  .role-info {
    position: relative;
    margin-bottom: 20px;

    .transfer-loading {
      width: 247px;
      height: 232px;
      text-align: center;
      padding-top: 100px;
      position: absolute;
      top: 98px;
      left: 0px;
      z-index: 2;
    }

    .transfer-empty {
      width: 246px;
      position: absolute;
      top: 146px;
      left: 0px;
      z-index: 2;

      /deep/.ant-empty-img-simple {
        width: 64px;
        height: 35px;
      }

      /deep/.ant-empty-image {
        height: 35px;
      }
    }

    .transfer-title {
      position: absolute;
      background: #ffffff;
      top: 50px;
      font-size: 12px;
      color: #333333;
      z-index: 1;

      span:nth-child(2) {
        color: #bbbbbb;
        margin-left: 8px;
      }
    }

    .transfer-left-title {
      left: 161px;
      .transfer-title();
    }

    .transfer-right-title {
      left: 448px;
      .transfer-title();
    }
    .input_search {
      width: 220px;
      margin-left: 14px;
      margin-bottom: 20px;
    }
    .iconsousuo {
      background: #ffffff;
      position: absolute;
      top: 98px;
      left: 172px;
      z-index: 2;
    }

    .iconsousuo1 {
      background: #ffffff;
      position: absolute;
      top: 98px;
      left: 460px;
      z-index: 2;
    }

    /deep/.anticon svg {
      color: #bec2c5;
      width: 18px;
      height: 18px;
    }

    /deep/.ant-transfer-list {
      width: 248px;
      height: 331px;
      max-width: 248px;
      max-height: 331px;

      .ant-tree,
      .ant-transfer-list-content {
        font-size: 12px !important;
        color: #555555;
      }
    }
  }

  /deep/.ant-transfer-list-body {
    overflow-y: auto;
  }

  .edit-loading {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.45);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/deep/.ant-form-item-explain {
  font-size: 12px;
}

/deep/.ant-breadcrumb-link {
  font-weight: 400;
}

/deep/.ant-form-item-label > label::after {
  margin-right: 16px;
}
</style>
