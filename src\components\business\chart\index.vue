<template>
  <div class="resource" style="min-height: 400px">
    <chart-box-user />
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import chartBoxUser from './chartBoxUser/index.vue';
export default defineComponent({
  components: {
    chartBoxUser,
  },
  setup() {
    return {};
  },
});
</script>

<style lang="less" scoped>
.resource {
  background: #fff;
  // padding: 20px 20px 30px;
}
</style>
