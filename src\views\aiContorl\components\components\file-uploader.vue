<template>
  <div class="file-uploader-container">
    <div class="file-uploader-box">
      <div class="card-box" v-if="listOrCard === true">
        <a-upload :disabled="disabledControl" v-model:file-list="fileList" list-type="picture-card" :max-count="max" name="file" :multiple="true" :accept="acceptTypes" :action="actionUrl" :beforeUpload="beforeUpload" @change="handleChange" :data="handleData" :headers="tokenHeader" @preview="handlePreview" :preview-file="getPreviewFileExpectImg" @remove="handleRemove">
          <div v-if="fileList.length < max">
            <div><plus-outlined :style="{ fontSize: '24px' }" /></div>
            <div style="margin-top: 8px">上传文件</div>
          </div>
        </a-upload>
      </div>
      <div class="list-box" v-else>
        <a-upload :disabled="disabledControl" @preview="handlePreview" :multiple="true" v-model:file-list="fileList" :data="handleData" :headers="tokenHeader" :max-count="max" name="file" :accept="acceptTypes" :action="actionUrl" :beforeUpload="beforeUpload" @change="handleChange" @remove="handleRemove">
          <div v-if="fileList.length < max">
            <a-button :disabled="disabledControl" style="color: #606972"> <upload-outlined /> 上传文件 </a-button>
          </div>
          <template #itemRender="{ file, actions }">
            <a-space>
              <div class="audio-box">
                <div class="file-name-box" :style="file.status === 'error' ? 'color: red' : '#f4f9f9'">{{ file.name }}</div>
                <audio :id="'audioRef' + file.uid" :src="audioPrevierImage[`previewImage-${file.uid}`]" preload="auto"></audio>
                <div class="audio-control-btn-box">
                  <div class="audio-control-btn" :id="'pauseBtn' + file.uid" @click="pauseAudio(file)" :style="{ display: 'none' }"><pause-outlined :style="{ color: '#A0A6AB' }" /></div>
                  <div class="audio-control-btn" :id="'playBtn' + file.uid" @click="playAudio(file)"><caret-right-outlined :style="{ color: '#A0A6AB' }" /></div>
                  <div class="audio-control-btn" @click="actions.remove"><close-outlined :style="{ color: '#A0A6AB' }" /></div>
                </div>
              </div>
            </a-space>
          </template>
        </a-upload>
      </div>
    </div>
    <a-modal :visible="previewVisible" title="预览" :footer="null" @cancel="handleCancel">
      <img alt="example" style="width: 100%" :src="previewImage" v-if="mediaType === MEDIA_ENUM.IMG" />
      <video controls="controls" style="width: 100%" :src="previewImage" v-if="mediaType === MEDIA_ENUM.VIDEO" ref="videoRef">
        <track label="English" srclang="en-us" kind="captions" default />
      </video>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, ref, toRefs, computed } from 'vue';
import { Upload } from 'ant-design-vue';
import { UploadOutlined, PlusOutlined, CloseOutlined, CaretRightOutlined, PauseOutlined } from '@ant-design/icons-vue';
import { getBase64, MEDIA_ENUM } from './utils';
import { useStore } from 'vuex';

export default defineComponent({
  components: {
    UploadOutlined,
    PlusOutlined,
    CloseOutlined,
    CaretRightOutlined,
    PauseOutlined,
  },
  props: {
    // value: String,
    // required: Boolean,
    acceptTypes: {
      type: String,
      default: '.jpg,.jpeg,.png,.bmp,.JPG,.JPEG,.PNG,.BMP',
    },
    typeTips: {
      type: String,
      default: '请上传至少3张图片，支持PNG、JPG、JPEG、BMP格式，大小不得超过5M。图片比例要求16:9，且尺寸不小于宽800*高450px；必须上传3张图片。',
    },
    max: {
      type: Number,
      default: 6,
    },
    min: {
      type: Number,
      default: 3,
    },
    actionUrl: {
      type: String,
      default: './aiipweb/om-service/os/upload',
    },
    mediaTypeChoose: {
      type: Number,
      default: MEDIA_ENUM.IMG,
    },
    onChange: {
      type: Function,
    },
    defaultFileList: {
      type: Array,
    },
    disabledControl: {
      type: Boolean,
    },
    isEdit: {
      type: Boolean,
    },
  },
  emits: ['onChange'], // 更新事件
  setup(props) {
    const store = useStore();
    const refreshToken = computed(() => store.state.refreshToken); // keyclock刷新的token
    const previewVisible = ref(false); // 图片与视频预览的弹窗
    const previewImage = ref('');
    const videoRef = ref(null);
    // 关闭预览弹窗
    const mediaType = ref(props.mediaTypeChoose); // 选择的什么上传类型
    const { defaultFileList } = toRefs(props);
    const fileList = ref([...defaultFileList.value]); // 上传的文件

    const audioPrevierImage = reactive({});
    const tokenHeader = reactive({
      Authorization: 'Bearer ' + store.state.refreshToken,
    });
    return {
      mediaType: mediaType.value,
      listOrCard: props.mediaTypeChoose === MEDIA_ENUM.IMG || props.mediaTypeChoose === MEDIA_ENUM.VIDEO,
      previewVisible,
      previewImage,
      MEDIA_ENUM,
      fileList,
      videoRef,
      audioPrevierImage,
      tokenHeader,
      refreshToken,
    };
  },
  computed: {},
  watch: {
    mediaTypeChoose(value) {
      this.mediaType = value;
      this.listOrCard = value === MEDIA_ENUM.IMG || value === MEDIA_ENUM.VIDEO;
    },
    defaultFileList(newValue) {
      this.fileList = [...newValue];
      // 如果是编辑状态并且是音频，就需要audioPrevierImage这个变量
      if (this.isEdit) {
        this.fileList.forEach((item) => {
          this.generateMediaFilePreviewAddress(item);
        });
      }
    },
  },
  methods: {
    handleRemove(file) {
      const newFileList = this.fileList.filter((item) => item.uid !== file.uid);
      this.$emit('onChange', newFileList);
    },
    handleChange(info) {
      const file = info.file;
      if (file.status === 'done') {
        if (file.response && file.response.state === 'OK') {
          this.$message.success(`文件上传成功`);
          this.$emit('onChange', info.fileList);
          this.generateMediaFilePreviewAddress(file);
        } else {
          file.response = { body: '' };
          file.status = 'error';
          this.$emit('onChange', info.fileList);
          this.$message.error(`文件上传失败`);
        }
      } else if (file.status === 'error') {
        file.response = { body: '' };
        this.$emit('onChange', info.fileList);
        this.$message.error(`文件上传失败`);
      }
    },
    async beforeUpload(file) {
      this.tokenHeader.Authorization = 'Bearer ' + this.refreshToken;
      if (!this.checkFileType(this.acceptTypes, file.name)) {
        this.$notification.error({
          message: '错误',
          description: '上传文件格式错误',
        });
        return Upload.LIST_IGNORE;
      }
      return true;
      // 暂时注释，不能删除
      // if (this.mediaType === MEDIA_ENUM.IMG) {
      //   const flag = await this.checkImage(file);
      //   if (!flag) {
      //     return Upload.LIST_IGNORE;
      //   }
      // }
      // if (this.mediaType === MEDIA_ENUM.VIDEO) {
      //   const flag = await this.checkVideo(file);
      //   if (!flag) {
      //     return Upload.LIST_IGNORE;
      //   }
      // }
      // if (this.mediaType === MEDIA_ENUM.AUDIO) {
      //   const flag = this.checkAudio(file);
      //   if (!flag) {
      //     return Upload.LIST_IGNORE;
      //   }
      // }
    },
    // 检查文件类型
    checkFileType(types, fileName) {
      return types.split(',').includes('.' + fileName.split('.')[1]);
    },
    // 校验图片
    async checkImage(file) {
      const isLt5M = file.size / 1024 / 1024 < 5;
      if (!isLt5M) {
        this.$notification.error({
          message: '错误',
          description: `图片不能超过5M（${file.name}）`,
        });
        return false;
      }
      const imgFit = await this.getImgInfo(file);
      if (!imgFit) {
        this.$notification.error({
          message: '错误',
          description: `图片读取失败（${file.name}）`,
        });
        return false;
      }
      const { width, height } = imgFit;
      if (width < 800) {
        this.$notification.error({
          message: '错误',
          description: `图片宽度应该大于800px（${file.name}）`,
        });
        return false;
      }
      if (height < 450) {
        this.$notification.error({
          message: '错误',
          description: `图片高度应该大于450px（${file.name}）`,
        });
        return false;
      }
      if (width / height !== 16 / 9) {
        this.$notification.error({
          message: '错误',
          description: `图片宽高比应等于16:9（${file.name}）`,
        });
        return false;
      }
      return true;
    },
    // 获取图片信息
    getImgInfo(file) {
      return new Promise(function (resolve, reject) {
        try {
          const reader = new FileReader();
          reader.readAsDataURL(file);
          reader.onload = () => {
            let img = new Image();
            img.src = reader.result;
            if (img.complete) {
              resolve({
                height: img.height,
                width: img.width,
              });
            } else {
              img.onload = () => {
                resolve({
                  height: img.height,
                  width: img.width,
                });
              };
            }
          };
        } catch (error) {
          reject(null);
        }
      });
    },
    async checkVideo(file) {
      try {
        const isLt5M = file.size / 1024 / 1024 < 200;
        if (!isLt5M) {
          this.$notification.error({
            message: '错误',
            description: `视频不能超过200M（${file.name}）`,
          });
          return false;
        }
        const videoObj = await this.getVideoInfo(file);
        if (!videoObj) {
          this.$notification.error({
            message: '错误',
            description: `读取视频失败（${file.name}）`,
          });
          return false;
        }
        const { duration } = videoObj;
        if (duration > 5) {
          this.$notification.error({
            message: '错误',
            description: `视频长度不应该超过5s（${file.name}）`,
          });
          return false;
        }
        return true;
      } catch (e) {
        return false;
      }
    },
    // 获取视频信息
    async getVideoInfo(file) {
      return new Promise(function (resolve, reject) {
        try {
          const oVideo = document.createElement('video');
          oVideo.preload = 'auto';
          oVideo.onloadedmetadata = function (e) {
            // 视频总长度，秒为单位
            resolve({
              oVideo,
              duration: oVideo.duration,
              width: oVideo.videoWidth,
              height: oVideo.videoHeight,
            });
          };
          oVideo.src = URL.createObjectURL(file);
        } catch (error) {
          reject(null);
        }
      });
    },
    // 检查音频
    checkAudio(file) {
      const isLtM = file.size / 1024 / 1024 < 10;
      if (!isLtM) {
        this.$notification.error({
          message: '错误',
          description: `音频大小不能超过10M（${file.name}）`,
        });
        return false;
      }
      return true;
    },
    // 生成传递给后端的参数
    handleData(file) {
      const params = {
        name: file.uid,
      };
      if (this.mediaType === MEDIA_ENUM.AUDIO) {
        params.materialType = '2';
      }
      return params;
    },
    // 获取视频封面
    getVideoPosterInfo(videoInfo) {
      return new Promise((resolve) => {
        const { oVideo, width, height } = videoInfo;
        oVideo.addEventListener('canplay', () => {
          const canvas = document.createElement('canvas');
          canvas.width = width;
          canvas.height = height;
          const ctx = canvas.getContext('2d');
          // 将视频对象直接绘入canvas
          ctx.drawImage(oVideo, 0, 0, width, height);
          // 获取图像的整体平均饱和度
          // const saturation = getImageSaturation(canvas)
          const posterUrl = canvas.toDataURL('image/jpg');
          resolve({ posterUrl });
        });
      });
    },
    // 获取视频文件自定义回显状态
    async getPreviewFileExpectImg(file) {
      try {
        if (this.mediaType === MEDIA_ENUM.IMG) {
          return await getBase64(file);
        }
        if (this.mediaType === MEDIA_ENUM.VIDEO) {
          const videoInfo = await this.getVideoInfo(file);
          const obj = await this.getVideoPosterInfo(videoInfo);
          return obj.posterUrl;
        }
        return await getBase64(file);
      } catch (e) {
        console.log('获取自定义回显效果失败：', e);
        return await getBase64(file);
      }
    },
    // 生成媒体文件预览地址
    async generateMediaFilePreviewAddress(file) {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      if (this.mediaType === MEDIA_ENUM.AUDIO) {
        this.audioPrevierImage[`previewImage-${file.uid}`] = file.url || file.preview;
        return;
      }
      this.previewImage = file.url || file.preview;
    },
    // 点击预览媒体文件
    async handlePreview(file) {
      // 如果预览的是图片和视频
      if (this.mediaType === MEDIA_ENUM.IMG || this.mediaType === MEDIA_ENUM.VIDEO) {
        this.previewImage = file.url || file.preview;
        this.previewVisible = true;
      }
    },
    // 关闭预览弹窗
    async handleCancel() {
      if (this.videoRef) {
        this.videoRef?.load();
      }
      this.previewVisible = false;
    },

    // 播放音频
    async playAudio(file) {
      this.previewVisible = false;
      const dom = document.getElementById('audioRef' + file.uid);
      const doms = document.getElementsByTagName('audio');
      if (doms) {
        for (let domi of doms) {
          if (domi) {
            // 获取dom[i]上面的fileId
            let idAttribute = domi.getAttribute('id');
            if (idAttribute) {
              idAttribute = idAttribute.replace('audioRef', '');
            }
            const pbDom = document.getElementById('playBtn' + idAttribute);
            const abDom = document.getElementById('pauseBtn' + idAttribute);
            pbDom.style.display = 'block'; // 播放按钮显示
            abDom.style.display = 'none'; // 暂停按钮隐藏
            domi.pause();
          }
        }
      }
      if (dom) {
        dom.play(); // 播放当前音频
        const pbDom = document.getElementById('playBtn' + file.uid);
        const abDom = document.getElementById('pauseBtn' + file.uid);
        pbDom.style.display = 'none';
        abDom.style.display = 'block';
      }
    },
    // 播放音频
    async pauseAudio(file) {
      const dom = document.getElementById('audioRef' + file.uid);
      const pbDom = document.getElementById('playBtn' + file.uid);
      const abDom = document.getElementById('pauseBtn' + file.uid);
      pbDom.style.display = 'block';
      abDom.style.display = 'none';
      dom.pause();
    },
  },
});
</script>

<style lang="less" scoped>
.card-box {
  /deep/ .ant-upload {
    flex-direction: column;
  }
}
.tips-box {
  font-size: 12px;
  color: #a0a6ab;
}
.audio-box {
  box-sizing: border-box;
  display: flex;
  margin-bottom: 10px;
  margin-top: 8px;
  width: 300px;
  align-items: center;
  justify-content: space-between;
  .file-name-box {
    width: 150px;
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #a0a6ab;
  }
  .audio-control-btn-box {
    box-sizing: border-box;
    display: flex;
    margin-left: 70px;
    .audio-control-btn {
      margin-right: 10px;
      cursor: pointer;
    }
  }
}
</style>
