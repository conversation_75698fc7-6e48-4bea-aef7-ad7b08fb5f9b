<template>
  <div>
    <a-menu class="menu" @click="handleClickItem" mode="inline" theme="dark" v-model:openKeys="openKeys" v-model:selectedKeys="activeKey">
      <div class="menu-wrap" v-for="item in menu" :key="menuKey(item)">
        <a-menu-item :key="menuKey(item)" v-if="item.link">
          <template #icon>
            <jt-icon :type="item.icon" />
          </template>
          <span>{{ item.title }}</span>
        </a-menu-item>
        <a-sub-menu :key="item.title" v-else>
          <template #icon>
            <jt-icon :type="item.icon" />
          </template>
          <template #title>
            <span>{{ item.title }}</span>
          </template>
          <a-menu-item v-for="x in item.subs" :key="x.link">{{ x.title }}</a-menu-item>
        </a-sub-menu>
      </div>
    </a-menu>
  </div>
</template>

<script lang="ts">
import { Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { defineComponent, computed, createVNode } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { LINK_FLAG } from '@/constants';
import { openInNewTab } from '@/utils';

export default defineComponent({
  setup() {
    const route = useRoute();
    const router = useRouter();
    const store = useStore();
    const menu = computed(() => store.state.sideMenu);
    const openKeys = computed(() => store.state.sideMenu.filter((x) => !x.link).map((x) => x.title));
    // 筛选选中的菜单
    const filterChoosedMenu = (key) => {
      const menuList = JSON.parse(JSON.stringify(store.state.sideMenu));
      const menuDta = menuList.find((item) => {
        const subs = item.subs;
        if (item.link === key) return item;
        if (subs && subs.length) {
          return subs.find((sub) => sub.link === key);
        }
      });
      if (menuDta.subs && menuDta.subs.length) {
        return menuDta.subs.find((sub) => sub.link === key);
      }
      return menuDta;
    };
    const handleClickItem = ({ key }) => {
      const { linkFlag } = filterChoosedMenu(key) || {};
      // 需要判断当前路径是否为 开放能力新建或编辑页面，有二次弹窗提醒
      if (location.hash.includes('#/capacity-management/capacity-')) {
        store.commit('openModal', key);
      } else {
        linkFlag === LINK_FLAG.RELATIVE ? router.push(key) : openInNewTab(key);
        if (route.path === key) {
          setTimeout(() => {
            store.commit('UPDATE_ROUTEVIEWREFRESHKEY');
          });
        }
      }
    };
    const menuKey = (item) => {
      if (item.link) {
        return item.link;
      }
      return item.title;
    };
    return {
      menu,
      activeKey: computed(() => {
        return ['/' + route.path.split('/')[1]];
      }),
      openKeys,
      handleClickItem,
      menuKey,
    };
  },
});
</script>

<style lang="less" scoped>
.menu,
:deep(.ant-menu-sub) {
  background: #153654 !important;
}
:deep(.ant-menu.ant-menu-dark) {
  background: #153654 !important;
}
:deep(.ant-menu-dark .ant-menu-submenu-open) {
  color: rgba(255, 255, 255, 0.65);
}
:deep(.menu-icon) {
  font-size: 22px !important;
}
:deep(.ant-menu-item .ant-menu-item-icon),
:deep(.ant-menu-submenu-title .ant-menu-item-icon) {
  font-size: 22px;
}
:deep(.ant-menu-item-only-child > .ant-menu-title-content) {
  padding-left: 8px;
}
</style>
