<template>
  <icon v-if="iconType === 'publish'" class="icons">
    <template #component>
      <svg t="1726716664347" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16799" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M944.360773 107.425093a31.96878 31.96878 0 0 1 3.007063 20.362115L814.159922 854.991048c-3.180894 17.36704-19.838626 28.86681-37.205666 25.684917a31.96878 31.96878 0 0 1-7.355817-2.290763l-196.358244-88.343727-93.728468 153.739864a36.963902 36.963902 0 0 1-22.542986 16.605783c-19.59986 4.930185-39.483442-6.793366-44.711336-26.237378l-0.15385-0.591422-61.280156-243.582127L85.758252 570.720656c-16.101276-7.243926-23.282263-26.169444-16.038338-42.27072a31.96878 31.96878 0 0 1 14.551789-15.322037L901.318806 93.588605c15.706661-8.065124 34.976843-1.871173 43.041967 13.836488zM434.460722 727.60345l27.050583 107.52 45.618451-74.825928-72.669034-32.694072z m415.68206-468.26571L451.314263 656.31207l301.282779 135.548629 97.54574-532.521959z m-88.971114-12.931371L190.98649 539.190447l190.107348 85.529475 380.078829-378.312554z" p-id="16800"></path></svg>
    </template>
  </icon>
</template>
<script>
import { defineComponent } from 'vue';
import Icon from '@ant-design/icons-vue';

export default defineComponent({
  props: {
    iconType: [String],
  },
  components: {
    Icon,
  },
});
</script>
<style></style>
