<template>
  <a-card :bordered="false" :bodyStyle="{ padding: 0 }">
    <div class="card-overview" v-if="type === 'OVERVIEW'">
      <img :src="data?.imgUrl" alt="" class="overview-image" />
      <ul class="overview-content">
        <li class="mt5">
          <span class="font-bold">{{ data?.content?.count ? formattedNumber(data?.content?.count) : '-' }}</span>
          <!-- <span class="font-bold">{{ data?.content?.count ? formattedNumberCN(data?.content?.count, { getUnits: 1 }) : '-' }}</span>
          <span v-if="data?.content?.count">{{ data?.content?.count && formattedNumberCN(data?.content?.count, { getUnits: 2 }) }}</span> -->
        </li>
        <li class="font-thin">
          <span class="jt-word">{{ data?.content?.word }}</span>
          <a-tooltip placement="top" :overlayStyle="{ 'font-size': '12px' }" v-if="data?.content?.description" @visibleChange="visibleChange">
            <template #title>
              <span>{{ data?.content?.description }}</span>
            </template>
            <span :class="['jt-icon', !isShow ? 'icon_help' : 'icon_help_bold']" />
          </a-tooltip>
        </li>
      </ul>
      <!-- 组织数特殊处理-->
      <template v-if="data?.content?.word === '组织数'">
        <div class="divide-line"></div>
        <div class="org-level">
          <div class="level-list">
            <span class="level-info" v-for="(ele, i) in data?.content?.orgInfo" :key="i">
              <span :class="['dot', ['三级', '四级'].includes(ele?.title) && 'light']"></span>
              <span class="name">{{ ele?.title }}</span>
              <span class="num-bold">{{ ele?.count ? formattedNumber(ele?.count) : '-' }}</span>
              <!-- <span class="num-bold">{{ ele?.count ? formattedNumberCN(ele?.count, { getUnits: 1 }) : '-' }}</span>
              <span v-if="ele?.count">{{ ele?.count && formattedNumberCN(ele?.count, { getUnits: 2 }) }}</span> -->
            </span>
          </div>
        </div>
      </template>
    </div>
    <div class="card-overview" v-if="type === 'RESOURCE'">
      <ul class="overview-content">
        <li v-if="data?.unit === SUMMARY_CLASS_UNIT.memory">
          <span class="font-bold">{{ data?.total ? getUsageResult(data?.used, 1, { getUnits: 1 }) : '-' }} </span>
          <span v-if="data?.total" class="font-18 ml5">{{ getUsageResult(data?.used, 1, { getUnits: 2 }) }}</span> / <span class="font-18">{{ data?.total ? convertToTB(data?.total) : '-' }} {{ data?.unit }}</span>
        </li>
        <li v-else>
          <span class="font-bold">{{ data?.total ? formattedNumber(toFixed(data?.used, 1)) : '-' }}</span> / <span class="font-18">{{ data?.total ? formattedNumber(toFixed(data?.total, 1)) : '-' }}</span> <span class="font-16">{{ data?.unit }}</span>
        </li>
        <li class="font-thin">{{ data?.title }}<span class="gray">（已用 / 总共）</span></li>
      </ul>
    </div>
    <div class="card-overview" v-if="type === 'LINKS'">
      <span class="link-href" @click="openInNewTab(data?.platformLink)">
        <img :src="data?.iconUrl" alt="" class="overview-image" />
        <ul class="overview-content">
          <li class="font-big center">{{ data?.platformName }}</li>
        </ul>
      </span>
    </div>
    <slot></slot>
  </a-card>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { formattedNumber, formattedNumberCN, getUsageResult, convertToTB, toFixed } from '@/utils/format';
import { SUMMARY_CLASS_UNIT } from '@/constants';
import { openInNewTab } from '@/utils';

export default defineComponent({
  props: {
    data: Object,
    type: String, // 区分卡片类型
  },
  setup(props) {
    // 是否显示帮助
    const isShow = ref(false);
    const visibleChange = (visible) => {
      isShow.value = visible;
    };
    return {
      formattedNumber,
      getUsageResult,
      formattedNumberCN,
      convertToTB,
      SUMMARY_CLASS_UNIT,
      visibleChange,
      isShow,
      toFixed,
      openInNewTab,
    };
  },
});
</script>

<style lang="less" scoped>
.card-overview {
  display: flex;
  .overview-image {
    width: 56px;
    height: 56px;
    margin-right: 12px;
    background: #edf7ff;
    border-radius: 3px;
  }
  .overview-content {
    margin: 0;
    .mt5 {
      margin-top: -5px;
    }
    .ml5 {
      margin-left: 5px;
    }
    .font-bold {
      font-size: 28px;
      font-weight: 500;
    }
    .font-18 {
      font-size: 18px;
    }
    .font-16 {
      font-size: 16px;
    }
    .font-thin {
      font-size: 12px;
      color: #a0a6ab;
      .jt-word,
      .jt-icon {
        display: inline-block;
        vertical-align: middle;
      }
      .jt-word {
        margin-right: 4px;
      }
      .jt-icon {
        width: 14px;
        height: 14px;
        background-size: 14px 14px;
        &.icon_help {
          background-image: url('~@/assets/images/home/<USER>');
        }
        &.icon_help_bold {
          background-image: url('~@/assets/images/home/<USER>');
        }
      }
      .gray {
        color: #cbcfd2;
      }
    }
    .font-big {
      font-size: 16px;
      &.center {
        line-height: 56px;
      }
    }
  }
  .divide-line {
    height: 40px;
    width: 1px;
    margin: 13px 16px 0 16px;
    background: #efefef;
  }
  .org-level {
    flex: 1;
    .level-list {
      .level-info {
        display: inline-block;
        width: 50%;
      }
    }
    .dot {
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 5px;
      background: #2986ff;
      border-radius: 50%;
      &.light {
        background: #a3d4ff;
      }
    }
    .name {
      vertical-align: middle;
    }
    .num-bold {
      margin-left: 16px;
      font-size: 20px;
      vertical-align: middle;
      font-weight: 500;
    }
  }
  .link-href {
    display: flex;
    color: #121f2c;
    cursor: pointer;
  }
}
</style>
