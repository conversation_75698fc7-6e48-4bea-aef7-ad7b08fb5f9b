<template>
  <a-modal :visible="props.shelfVal" :closable="false" :width="388" :footer="false" class="modal">
    <div class="shelf">
      <div class="quitbox">
        <div class="quitbox1">
          <jt-icon type="iconerror-fill" />
        </div>
        <div class="quitbox2" v-if="isIntact">
          <p>
            确认<span>{{ props.shelfOrDraft }}</span
            >该能力吗？
          </p>
          <div class="draftBut">
            <p>请先查看<span @click="preview">预览</span>效果，确保无误后再{{ props.shelfOrDraft }}。</p>
            <p>确定{{ props.shelfOrDraft }}后将实时{{ props.shelfOrDraft }}到开放能力，能力状态{{ props.shelfOrDraft == '同步' ? '仍' : '变' }}为“已上架”。</p>
          </div>
        </div>
        <div class="quitbox2" v-else>
          <p>请补全能力信息</p>
          <div class="draftBut">
            <p style="height: 72px">该能力信息不完整，请填写完善后再{{ props.shelfOrDraft }}。</p>
          </div>
        </div>
        <div class="quitbox3">
          <a-button key="submit" @click="cancel"> 取消 </a-button>
          <a-button key="back" @click="handleOk" v-if="isIntact"> 确定{{ props.shelfOrDraft }} </a-button>
          <a-button key="backTo" @click="handleTo(props.draftObj)" v-else> 补全信息 </a-button>
        </div>
      </div>
    </div>
    <!-- 误删 -->
    <div class="shelf2"></div>
  </a-modal>
</template>
<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue';
import request from '@/request';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
const props = defineProps({
  shelfVal: {
    //模态框
    type: Boolean,
    default: false,
  },
  shelfOrDraft: {
    //上架还是同步
    type: String,
    default: '上架',
  },
  shelfObj: {
    //能力所有数据
    type: Object,
    default: () => ({}),
  },
  draftButton: {
    //判断是否需要补充数据
    type: Boolean,
    default: true,
  },
  keyDraft: {
    //能力还是草稿请求的上架
    type: Boolean,
    default: false,
  },
  draftObj: {
    //草稿里的数据
    type: Object,
    default: () => ({}),
  },
});
const emits = defineEmits(['cancelModal']);
const isIntact = ref(true);
//监听
watch(
  () => props.shelfOrDraft,
  (newValue) => {
    console.log(newValue);
  }
);
//关闭
const cancel = () => {
  emits('cancelModal');
};
//确认请求
const handleOk = () => {
  if (!props.keyDraft) {
    const capabilityId = props?.shelfObj?.id;
    request('/aiipweb/om-service/capability/on-shelf', {
      method: 'GET',
      data: {
        capabilityId: capabilityId,
      },
    }).then((res: any) => {
      // if (res.state) {
      if (res.body == 'success') {
        message.success(`【${props?.shelfObj?.name}】上架操作成功，上架结果将通过站内信发送，请耐心等待`);
        emits('cancelModal', true);
      } else {
        message.error(`【${props?.shelfObj?.name}】未能上架到开放能力，请稍后再试`);
        emits('cancelModal');
      }
      // }
    });
  } else {
    const draftId = props?.draftObj?.id;
    request('/aiipweb/om-service/draft/on-sync', {
      method: 'GET',
      data: {
        draftId: draftId,
      },
    }).then((res: any) => {
      if (res.state == 'OK') {
        // if (props.shelfOrDraft == '同步') {
        //   message.success(`【${props?.draftObj?.name}】已同步到开放能力`);
        // } else if (props.shelfOrDraft == '上架') {
        //   message.success(`【${props?.draftObj?.name}】上架操作成功，上架结果将通过站内信发送，请耐心等待`);
        // }
        props.shelfOrDraft == '同步' ? message.success(`【${props?.draftObj?.name}】已同步到开放能力`) : message.success(`【${props?.draftObj?.name}】上架操作成功，上架结果将通过站内信发送，请耐心等待`);
        emits('cancelModal', true);
      } else {
        if (res.errorCode == '130100') {
          isIntact.value = false;
        } else if (res.errorCode == '130108') {
          message.error(res.errorMessage);
          emits('cancelModal');
        } else {
          message.error(`【${props?.draftObj?.name}】未能${props.shelfOrDraft}到开放能力，请稍后再试`);
          // if (props.shelfOrDraft == '同步') {
          //   message.error(`【${props?.draftObj?.name}】未能同步到开放能力，请稍后再试`);
          // } else {
          //   message.error(`【${props?.draftObj?.name}】未能上架到开放能力，请稍后再试`);
          // }
          emits('cancelModal');
        }
      }
      // }
    });
  }
};
//补全信息
const router = useRouter();
const handleTo = (val) => {
  emits('cancelModal');
  router.push({
    path: `/capacity-management/capacity-edit/${val.name}`,
    query: { key: 'draft', button: val.button },
  });
};
//去预览
const preview = () => {
  message.warning('下期开放');
};
</script>
<style lang="less" scoped>
.modal {
  .shelf {
    width: 372px;
    height: 318px;
    border-radius: 2px;
    border: 1px solid #ffb766;
    position: absolute;
    left: 8px;
    top: 8px;
  }
  .shelf2 {
    width: 388px;
    height: 288px;
  }
}
/deep/.ant-modal-header {
  border: none;
}
/deep/.ant-modal-footer {
  border: none;
  padding: 0 32px 24px 0;
}
/deep/.ant-btn {
  margin-bottom: 0px;
}
.quitbox {
  .quitbox1 {
    display: flex;
    justify-content: center;
    margin-bottom: 37px;
    margin-top: 24px;
    /deep/ svg {
      width: 68px;
      height: 64px;
      color: #ff7e00;
    }
  }
  .quitbox2 {
    margin-left: 32px;
    margin-bottom: 32px;
    p:nth-child(1) {
      font-size: 16px;
      font-weight: bold;
      font-weight: 500;
      color: #121f2c;
      line-height: 22px;
      margin-bottom: 16px;
      span {
        color: #ff7e00;
        margin: 0 5px;
      }
    }
    p:nth-child(2),
    .draftBut p:nth-child(1),
    .draftBut p:nth-child(2) {
      font-size: 14px;
      font-weight: 400;
      color: #606972;
      line-height: 24px;
      margin-bottom: 0;
      cursor: pointer;
      span {
        color: #0082ff;
        margin: 0 5px;
      }
    }
  }
  .quitbox3 {
    float: right;
    margin-right: 32px;
    .ant-btn:nth-child(2) {
      margin-left: 8px;
      background-color: #ff7e00;
      color: white;
    }
  }
}
</style>
