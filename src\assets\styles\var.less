/* less全局变量 */

// 颜色类
@jt-primary-color: #0082FF;
@jt-color-white: #fff;
@jt-text-color-primary: #121F2C;
@jt-text-color: #606972; // 灰色文字颜色
@jt-text-color-secondary: #A0A6AB; // 二级灰色文字颜色
@jt-title-color: @jt-text-color-primary; // 标题文字颜色：tab标题、弹框标题

@jt-disable-color: #CBCFD2; // 按钮disable颜色
@jt-line-color:#E0E1E1; // 分割线颜色
@jt-table-header-color: #333333; // table表头文字颜色、alert提示的文字颜色

@jt-error-color: #FF454D;
@jt-warn-color: #F79032;

// 字体大小
@jt-font-size-sm: 12px;
@jt-font-size-base: 14px;
@jt-font-size-lg: @jt-font-size-base + 2px;
@jt-font-size-lger: @jt-font-size-lg + 2px;
@jt-font-weight: 400;
@jt-font-weight-medium: 600;

// 圆角
@jt-border-radius: 2px;

// 按钮高度
@jt-btn-height-base: 32px;
@jt-btn-height-lg: 40px;

// 间距
@jt-gap-base: 8px;

// 盒子阴影
@jt-box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);


