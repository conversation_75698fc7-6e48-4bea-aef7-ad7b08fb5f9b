<template>
  <div class="user-control-container">
    <sub-header title="操作日志"></sub-header>
    <Container>
      <container-item>
        <div class="top-bar">
          <div class="top-bar-left">
            <h3>日志列表</h3>
            <div class="group-tip">
              <ExclamationCircleFilled style="color: #f9881a" />
              <div>仅保留近3个月内日志记录</div>
            </div>
          </div>
          <a-space>
            <div class="flex_center">
              <a-input @change="(e) => handleChange('searchKey', e.target.value)" class="group-search" v-model:value="postData.searchKey" allow-clear placeholder="操作人/操作/操作对象" style="margin-right: 12px">
                <template #prefix>
                  <jt-icon type="iconsousuo" style="font-size: 18px" />
                </template>
              </a-input>
              <a-select class="group-filter" placeholder="操作结果" ref="eventResult" v-model:value="postData.eventResult" allow-clear @focus="focus" @change="(value) => handleChange('eventResult', value)">
                <a-select-option value="1">成功</a-select-option>
                <a-select-option value="0">失败</a-select-option>
              </a-select>
              <a-select :max-tag-count="1" mode="multiple" class="group-filter2" placeholder="操作模块" ref="eventModule" allow-clear v-model:value="postData.eventModule" @focus="focus" @change="(value) => handleChange('eventModule', value)">
                <a-select-option v-for="(item, index) in moduleTypeList" :key="index" :value="item">{{ item }}</a-select-option>
              </a-select>
            </div>
            <a-range-picker
              @change="(value) => handleChange('time', value)"
              :locale="locale"
              v-model:value="postData.time"
              show-time
              :allowClear="false"
              format="YYYY-MM-DD HH:mm"
              :disabledDate="(current) => current && (current > dayjs().endOf('day') || current < dayjs().subtract(3, 'month'))"
              :ranges="{
                近一天: [dayjs().subtract(1, 'day'), dayjs()],
                近一周: [dayjs().subtract(7, 'day'), dayjs()],
                近一月: [dayjs().subtract(1, 'month'), dayjs()],
                近三月: [dayjs().subtract(3, 'month'), dayjs()],
              }"
            />
            <a-button @click="exportData" :disabled="!showEdit">
              <template #icon><upload-outlined /></template>
              导出
            </a-button>
          </a-space>
        </div>
        <a-table :loading="loading" :customRow="customRow" :columns="columns" :data-source="dataSource" :rowClassName="() => 'cus-row'" :pagination="false" @change="handleTableChange">
          <template #emptyText>
            <a-empty :image="AEmpty.PRESENTED_IMAGE_SIMPLE" :image-style="{ height: '32px' }" description="目前暂无操作日志信息" />
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'eventResult'">
              <span>
                <a-tag style="border-radius: 8px" :color="record.eventResult == 1 ? 'success' : 'error'">{{ statusFilter(record.eventResult) }}</a-tag>
              </span>
            </template>
          </template>
        </a-table>
        <Pagination :total="total" :pageNum="postData.pageNum" :pageSize="postData.pageSize" :pageSizeOptions="[10, 20, 50, 100]" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
      </container-item>
    </Container>
  </div>
</template>

<script setup>
import { Empty as AEmpty } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { onMounted } from 'vue';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import Pagination from '@/components/pagination.vue';
import ContainerItem from '@/components/containerItem.vue';
import { ExclamationCircleFilled, UploadOutlined, SearchOutlined } from '@ant-design/icons-vue';
import { reactive, ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import { STATUSENUM } from './utils/enum';
import ApiOperationLog from '../../apis/operationLog';
import { downloadFile } from '@/utils/file';
import { checkKeycloakArray } from '@/utils/auth';
dayjs.locale('zh-cn');
const router = useRouter();
const route = useRoute();
const dataSource = ref([]);
const moduleTypeList = ref([]);
const loading = ref(false);
const total = ref(0);
const postData = ref({
  eventModule: [], // 默认值为 'all'
  eventResult: null,
  searchKey: '',
  pageNum: 1,
  pageSize: 10,
  sortFiled: 'eventTime',
  isAsc: false,
  isExport: false,
  startTime: dayjs().subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss'), // 默认开始时间为最近一天
  endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 默认结束时间为当前时间
  time: [dayjs().subtract(1, 'day'), dayjs()],
});

const showEdit = computed(() => {
  return checkKeycloakArray('jtc-operation-log').includes('edit');
});
const columns = computed(() => {
  let columns = [
    {
      title: '操作时间',
      dataIndex: 'eventTime',
      key: 'eventTime',
      sorter: true, // 添加排序功能
      sortDirections: ['descend', 'ascend'],
      width: 150, // 设置列宽
      customRender: ({ text }) => {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-';
      },
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: 150, // 设置列宽
    },
    {
      title: '操作模块',
      dataIndex: 'eventModule',
      key: 'eventModule',
      width: 150, // 设置列宽
    },
    {
      title: '操作',
      dataIndex: 'eventAction',
      key: 'eventAction',
      width: 150, // 设置列宽
    },
    {
      title: '操作结果',
      dataIndex: 'eventResult',
      key: 'eventResult',
      width: 150, // 设置列宽
    },
    {
      title: '操作对象',
      dataIndex: 'eventObject',
      key: 'eventObject',
      width: 150, // 设置列宽
      ellipsis: true, // 启用省略号
    },
  ];
  return columns;
});
const debounce = (func, delay) => {
  let timer;
  return (...args) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

// 使用自定义防抖函数
const debouncedLogData = debounce(() => {
  LogData();
}, 500);
const handleChange = (key, value) => {
  postData.value.pageNum = 1;
  switch (key) {
    case 'searchKey':
      postData.value.searchKey = value;
      debouncedLogData(); // 仅操作人调用防抖后的查询方法
      break;
    case 'eventResult':
      postData.value.eventResult = value === undefined ? null : value; // 更新操作结果
      break;
    case 'eventModule':
      postData.value.eventModule = value === undefined ? [] : value;
      break;
    case 'time':
      if (value && value.length === 2) {
        postData.value.startTime = dayjs(value[0]).format('YYYY-MM-DD HH:mm:ss'); // 格式化开始时间
        postData.value.endTime = dayjs(value[1]).format('YYYY-MM-DD HH:mm:ss'); // 格式化结束时间
      } else {
        postData.value.startTime = '';
        postData.value.endTime = '';
      }
      break;
    default:
      console.warn(`Unknown key: ${key}`);
  }

  // 除了 searchKey 外，其他情况直接调用 LogData，
  if (key !== 'searchKey') {
    LogData();
  }
};
const LogData = () => {
  loading.value = true;
  // 处理 eventModule 的值
  const requestData = {
    ...postData.value,
    eventResult: postData.value.eventResult === null ? '' : postData.value.eventResult,
  };
  ApiOperationLog.ApiLogList(requestData)
    .then((res) => {
      if (res.data && res.data.data && res.data.data.length > 0) {
        total.value = res.data.total;
        dataSource.value = res.data.data;
      } else {
        // 如果查询结果为空，清空表格数据
        total.value = 0;
        dataSource.value = [];
      }
    })
    .catch((error) => {
      throw new Error('查询失败');
    })
    .finally(() => {
      // 无论成功还是失败，都结束 loading
      loading.value = false;
    });
};
// 获取操作模块
const getModule = () => {
  ApiOperationLog.APILogModelType().then((res) => {
    if (res.data && res.data.length > 0) {
      moduleTypeList.value = res.data;
    }
  });
};
//导出
const exportData = async () => {
  const requestData = {
    ...postData.value,
    eventResult: postData.value.eventResult === null ? '' : postData.value.eventResult,
  };
  try {
    message.loading('正在导出，请稍后...', 0); // 显示加载提示
    await downloadFile({ url: `/web/admin/eventrack/v1/event-log/export-event-log-detail`, method: 'POST', data: requestData });
    message.destroy(); // 清除加载提示
    message.success('导出完成');
  } catch (error) {
    message.destroy(); // 清除加载提示
    console.error('导出失败:', error);
    message.error('导出失败，请稍后重试');
  }
};
const handleTableChange = (pagination, filters, sorter) => {
  if (sorter.field === 'eventTime') {
    postData.value.sortFiled = 'eventTime'; // 设置排序字段
    postData.value.isAsc = sorter.order === 'ascend'; // 设置排序方向
    LogData(); // 重新加载数据
  }
};
const changePageNum = (pageNum) => {
  postData.value.pageNum = pageNum;
  LogData();
};
const changePageSize = (pageSize) => {
  postData.value.pageSize = pageSize;
  postData.value.pageNum = 1;
  LogData();
};
// 点击跳转详情
const customRow = (record) => {
  return {
    onClick: () => {
      goToDetail(record);
    },
  };
};
const goToDetail = (record) => {
  router.push({
    path: '/operation-log/operationLog-detail',
    query: {
      ...route.id,
      id: record.id,
    },
  });
};
const statusFilter = (status) => {
  const item = STATUSENUM.find((item) => item.value == status);
  return item ? item.text : '';
};

onMounted(() => {
  // LogData(); // 调用日志数据加载方法
  getModule(); // 调用获取操作模块方法
  handleChange('time', postData.value.time); // 手动触发 handleChange，初始化默认值
});
</script>

<style lang="less" scoped>
.top-bar-left {
  display: flex;
  align-items: center;
  width: 400px;
  h3 {
    margin-right: 16px; // 标题与提示信息之间的间距
  }
}

.user-control-container {
  #container-wrap {
    height: calc(100vh - 108px);
  }
}
.flex_center {
  display: flex;
  align-items: center;
}

.top-bar {
  padding: 16px 0;
  padding-top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.groupPath {
  display: block;
  max-width: 200px;
}

.userName {
  display: block;
  max-width: 200px;
}

.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}

.group-icon {
  width: 186px;
  margin-right: 20px;
  font-size: 14px;
  display: flex;
  justify-content: space-around;
}

.group-tip {
  width: 200px;
  font-size: 14px;
  display: flex;
  justify-content: space-evenly;
}

.group-search {
  min-width: 180px; // 改为最小宽度
  width: 12vw; // 使用视口单位
  max-width: 280px; // 设置最大宽度
  font-size: 12px;
}

.group-filter {
  min-width: 80px;
  width: 6vw;
  max-width: 120px;
  font-size: 12px;
  :deep(.ant-select-selection-placeholder) {
    color: #000000 !important; /* 确保颜色可见 */
  }
}

.group-filter2 {
  min-width: 180px;
  width: 10vw;
  max-width: 180px;
  font-size: 12px;
  :deep(.ant-select-selection-placeholder) {
    color: #000000 !important;
  }
}

.group-time {
  min-width: 260px;
  width: 22vw;
  max-width: 360px;
  font-size: 12px;
}

// 新增媒体查询适配
@media screen and (max-width: 1440px) {
  .group-search {
    width: 180px;
  }
  .group-filter {
    width: 100px;
  }
  .group-filter2 {
    width: 140px;
  }
  .group-time {
    width: 280px;
  }
}

@media screen and (max-width: 1280px) {
  .group-search {
    width: 160px;
  }
  .group-time {
    width: 240px;
  }
}

/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}

/deep/.ant-table-filter-column {
  justify-content: left;

  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    width: 48px;
    flex: 0;
  }
}

/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}

:deep(.cus-row) {
  cursor: pointer;
  font-size: 12px;

  .user-name-text {
    &:hover {
      color: @primary-color;
    }
  }
}

:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;

  th {
    background-color: #f6f9fc;
  }
}
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
  padding: 0 16px;
}
</style>
