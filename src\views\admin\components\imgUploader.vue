<template>
  <div class="container">
    <div class="viewer">
      <img :src="value || defaultUrl" alt="" />
    </div>
    <a-upload
      :showUploadList="false"
      name="file"
      :multiple="false"
      :accept="acceptTypes"
      action="./object/web/storage/image/upload"
      :headers="{
        Authorization: 'Bearer ' + refreshToken,
      }"
      :beforeUpload="beforeUpload"
      @remove="handleRemove"
      @change="onChange"
    >
      <!-- <span class="required-icon">*</span> -->
      <a-button type="primary" ghost style="margin: 4px 0px"> 更换头像</a-button></a-upload
    >
    <p class="ant-upload-hint">支持jpg、jpeg、gif、png、bmp格式，大小不超过10MB，建议使用正方形图片</p>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { checkFileType } from '../utils';

export default {
  props: {
    value: String,
    required: <PERSON>olean,
    acceptTypes: {
      required: false,
      type: String,
      default: '.jpg,.jpeg,.gif,.png,.bmp,.JPG,.JPEG,.GIF,.PNG,.BMP',
    },
  },
  emits: ['update:value'],
  data() {
    return {
      defaultUrl: require('@/assets/images/avatar_admin.png'),
    };
  },
  computed: {
    ...mapState(['refreshToken']),
  },
  methods: {
    handleRemove() {
      this.$emit('update:value', '');
    },
    onChange(info) {
      const file = info.file;
      if (file.status === 'done') {
        if (file.response && file.response.state === 'OK') {
          this.$message.success(`文件上传成功`);
          this.$emit('update:value', file.response.body.url);
        } else {
          this.$message.error(file.response.errorMessage || `文件上传失败:未知错误`);
        }
      } else if (file.status === 'error') {
        this.$message.error(`文件上传失败`);
      }
    },
    beforeUpload(file) {
      if (!checkFileType(this.acceptTypes, file.name)) {
        this.$notification.error({
          message: '错误',
          description: '上传文件格式错误',
        });
        return false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.ant-upload-hint {
  font-size: 12px;
  font-weight: 400;
  color: #a0a6ab;
  line-height: initial;
  width: 180px;
  text-align: left;
}
.viewer {
  margin-bottom: 24px;
  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
  }
}
.required-icon {
  color: #ff454d;
  margin-right: 12px;
}
</style>
