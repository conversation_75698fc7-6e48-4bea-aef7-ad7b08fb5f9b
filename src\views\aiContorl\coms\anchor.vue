<template>
  <!-- <section> -->
  <div class="anchor" v-if="isShowFun">
    <a-anchor class="anchorList" @click="handleClick">
      <a-anchor-link href="#sample" v-if="showSample">
        <template #title>模型编排</template>
      </a-anchor-link>
      <a-anchor-link href="#demo" v-if="isDemo">
        <template #title>功能演示</template>
      </a-anchor-link>
      <a-anchor-link href="#functionTab">
        <template #title>功能介绍</template>
      </a-anchor-link>
      <a-anchor-link href="#scrollTab">
        <template #title>使用场景</template>
      </a-anchor-link>
      <a-anchor-link href="#technology">
        <template #title>技术特色</template>
      </a-anchor-link>
      <a-anchor-link href="#questions">
        <template #title>常见问题</template>
      </a-anchor-link>
      <a-anchor-link href="#recommend">
        <template #title>相关推荐</template>
      </a-anchor-link>
    </a-anchor>
  </div>
  <div class="anchor" v-else>
    <a-anchor class="anchorList" @click="handleClick">
      <a-anchor-link href="#functionTab">
        <template #title>功能介绍</template>
      </a-anchor-link>
      <a-anchor-link href="#scrollTab">
        <template #title>使用场景</template>
      </a-anchor-link>
      <a-anchor-link href="#technology">
        <template #title>技术特色</template>
      </a-anchor-link>
      <a-anchor-link href="#questions">
        <template #title>常见问题</template>
      </a-anchor-link>
    </a-anchor>
  </div>
  <!-- </section> -->
</template>

<script>
export default {
  props: {
    isShowFun: {
      type: Boolean,
      default: true,
    },
    isShowReal: {
      type: Boolean,
      default: true,
    },
    showSample: {
      type: Boolean,
      default: false,
    },
    isDemo: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {};
  },
  components: {},
  created() {},
  methods: {
    handleClick(e, link) {
      e.preventDefault();
    },
  },
};
</script>

<style scoped lang="less">
/deep/.ant-affix {
  box-shadow: 0px 10px 10px -10px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  background: #ffffff;
}
/deep/.ant-anchor-link-active {
  background: linear-gradient(90deg, #00cfdf 0%, #0093dc 100%);
}
/deep/.ant-anchor-ink-ball {
  display: none;
}
/deep/.ant-anchor-link-active > .ant-anchor-link-title {
  color: #ffffff;
  &:hover {
    color: #ffffff !important;
  }
}
/deep/.ant-affix .ant-anchor-wrapper .ant-anchor {
  min-width: 1320px;
  .ant-anchor-link {
    flex: 1;
    transition: 2s ease;
    padding: 0;
    a {
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
  }
}
/deep/.ant-anchor {
  display: flex;
  justify-content: center;
  align-content: center;
  height: 48px;
  .ant-anchor-link {
    display: flex;
    justify-content: center;
    align-content: center;
    flex-direction: column;
    padding: 0 50px;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    a {
      &:hover {
        color: #00b3cc;
        font-weight: 600;
      }
    }
  }
}
.anchor {
  width: 100%;
  min-width: 1320px;
  height: 48px;
  background: #ffffff;
  box-shadow: 0px 10px 10px -10px rgba(0, 0, 0, 0.1);
}
</style>
