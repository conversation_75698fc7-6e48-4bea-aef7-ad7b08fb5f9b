import request from '@/request';

const MEG_API = {
  list: '/messagecenter/web/manage/getMailList',
  userUpload: '/messagecenter/web/receiver/import',
  download: '/messagecenter/web/receiver/downloadTemplate',
  userExport: '/messagecenter/web/receiver/downloadResult',
  successUserExport: '/messagecenter/web/receiver/downloadRecord',
  create: '/messagecenter/web/manage/createMail',
  update: '/messagecenter/web/manage/updateMail',
  send: '/messagecenter/web/manage/sendMail',
  detail: '/messagecenter/web/manage/getMailDetail',
  cancel: '/messagecenter/web/manage/cancelMail',
  delete: '/messagecenter/web/manage/deleteMail',
  mailType: '/messagecenter/web/getAllMailType',
};

const MessageCenter = {
  getList: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(MEG_API.list, { method: type || 'GET', data });
  },
  // 新建站内信-上传用户列表
  uploadSenderUser: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(MEG_API.userUpload, { method: type || 'GET', data, headers: { 'Content-Type': 'multipart/form-data' } });
  },
  download: MEG_API.download,
  exportUser: MEG_API.userExport,
  exportSuccessUser: MEG_API.successUserExport,
  createMail: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(MEG_API.create, { method: type || 'GET', data });
  },
  updateMail: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(MEG_API.update, { method: type || 'GET', data });
  },
  sendMail: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(MEG_API.send, { method: type || 'GET', data });
  },
  getDetail: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(MEG_API.detail, { method: type || 'GET', data });
  },
  cancelMail: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(MEG_API.cancel, { method: type || 'GET', data });
  },
  deleteMail: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(MEG_API.delete, { method: type || 'GET', data });
  },
  getMailType: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(MEG_API.mailType, { method: type || 'GET', data });
  },
};
export default MessageCenter;
