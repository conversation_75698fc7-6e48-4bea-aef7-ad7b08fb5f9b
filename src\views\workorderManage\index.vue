<template>
  <div>
    <sub-header title="工单管理"> </sub-header>
    <Container>
      <container-item style="position: relative">
        <div class="workorder-manage-header">
          <a-space>
            <a-button @click="toReceiverManage" type="primary" v-if="FEATURE_SMS">接收人管理</a-button>
            <a-button @click="exportModalVisible = true" type="primary">工单导出</a-button>
          </a-space>
        </div>
        <div>
          <a-tabs :activeKey="activeKey" forceRender @change="tabChange">
            <a-tab-pane tab="需求反馈工单" key="1" v-if="showDemand">
              <demand-manage v-if="!isQuestionManage" @updateLmCatogery="updateLmCatogery" />
            </a-tab-pane>
            <a-tab-pane tab="问题反馈工单" key="2" v-if="showQuestion">
              <question-manage v-if="isQuestionManage" @updatePlatform="updatePlatform" @updateStatusCatogery="updateStatusCatogery" />
            </a-tab-pane>
          </a-tabs>
        </div>
      </container-item>
    </Container>
    <!-- 导出弹窗 -->
    <export-modal v-model:visible="exportModalVisible" :isQuestionManage="isQuestionManage" :statusCatogery="statusCatogery" :lmCatogery="lmCatogery" title="工单导出" :confirmLoading="exportModalConfirmLoading" @ok="exportModalHandleOk" @cancel="exportModalHandleCancel" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, toRaw, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import ExportModal from './exportModal.vue';
import { checkKeycloakAuth } from '@/utils/auth';
import { downloadFile } from '@/utils/file';
import DemandManage from './demandManage/index.vue';
import QuestionManage from './questionManage/index.vue';
import { getEnvConfig } from '@/config';

const hasQuesitionTicket = checkKeycloakAuth('COMMON_MGM_TICKET');
const hasDemandTicket = checkKeycloakAuth('COMMON_MGM_DEMAND_TICKET');
// const showDemandEnv = getEnvConfig('SHOW_DEMAND_ICON') === '1';

export default defineComponent({
  components: {
    QuestionManage,
    DemandManage,
    subHeader,
    Container,
    ContainerItem,
    ExportModal,
  },
  setup() {
    // 2.5版本，需求反馈废弃，接入其他平台的需求反馈
    // const showDemand = ref(hasDemandTicket && showDemandEnv);
    const showDemand = ref(false);
    const showQuestion = ref(hasQuesitionTicket);
    const router = useRouter();
    const route = useRoute();
    const exportModalVisible = ref(false);
    const breadCrumbs = [{ name: '工单管理' }];
    const FEATURE_SMS = getEnvConfig('FEATURE_SMS') === '1';

    const platform = ref<string>('');
    const statusCatogery = ref([]);
    const lmCatogery = ref([]);
    const toReceiverManage = () => {
      router.push({ path: `/workorder-manage/receiver`, query: route.query });
    };

    // 初始化tab权限处理，根据tab
    const initRolesCheck = () => {
      let activeKey = route.query.activeKey;
      if (showDemand.value && !hasQuesitionTicket) {
        if (activeKey !== '1' || activeKey !== undefined) {
          router.replace({ query: { ...route.query, activeKey: '1' } });
          activeKey = '1';
        }
      }
      if (!showDemand.value && hasQuesitionTicket) {
        if (activeKey !== '2') {
          router.replace({ query: { ...route.query, activeKey: '2' } });
          activeKey = '2';
        }
      }
      return activeKey;
    };
    const initActiveKey = initRolesCheck();
    const activeKey = ref(initActiveKey);

    const isQuestionManage = computed(() => {
      return activeKey.value === '2';
    });

    const exportModalConfirmLoading = ref(false);
    const exportModalHandleOk = (values) => {
      const data: any = {
        beginTime: values.time[0].format('YYYY-MM-DD'),
        endTime: values.time[1].format('YYYY-MM-DD'),
      };
      let url = '';
      if (isQuestionManage.value) {
        url = '/ticket/web/exportTicket';
        data.category = values.category;
        data.status = values.status;
        data.platform = toRaw(platform.value);
      } else {
        url = '/ticket/web/demand/export';
        data.largeModelTypes = values.largeModelTypes;
        data.statuses = values.status;
      }
      exportModalConfirmLoading.value = true;
      downloadFile({ url, method: 'POST', data })
        ?.then(() => {
          exportModalHandleCancel();
        })
        .finally(() => {
          exportModalConfirmLoading.value = false;
        });
    };
    const exportModalHandleCancel = () => {
      exportModalVisible.value = false;
    };

    const tabChange = (newActiveValue) => {
      router.push({ query: { activeKey: newActiveValue } }).then(() => {
        activeKey.value = newActiveValue;
      });
    };

    // 同步子组件的筛选状态
    const updateStatusCatogery = (catogery) => {
      statusCatogery.value = catogery;
    };
    const updatePlatform = (value) => {
      platform.value = value;
    };
    const updateLmCatogery = (value) => {
      lmCatogery.value = value;
    };

    return {
      activeKey,
      breadCrumbs,
      FEATURE_SMS,
      toReceiverManage,
      exportModalVisible,
      exportModalHandleCancel,
      exportModalHandleOk,
      exportModalConfirmLoading,
      checkKeycloakAuth,
      tabChange,
      updatePlatform,
      updateStatusCatogery,
      statusCatogery,
      showDemand,
      showQuestion,
      lmCatogery,
      updateLmCatogery,
      isQuestionManage,
    };
  },
});
</script>

<style lang="less" scoped>
@import '@/assets/styles/index.less';
.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}
.workorder-manage-header {
  position: absolute;
  right: 20px;
  top: 20px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  margin-bottom: 20px;
  z-index: 1;
}
</style>
