import { getEnvConfig } from './config';
import Keycloak from 'keycloak-js';
import { axios } from './request';
import request from './request';
import store from './store';

export function getKeycloakUrl(): string {
  const keycloakUrl = getEnvConfig('KEYCLOAK_URL');
  return keycloakUrl;
}

export function getKeycloakClientId() {
  return getEnvConfig('KEYCLOAK_CLIENT_ID');
}

function getKeycloakLogoutPrefix(): string {
  const keycloakUrl = getEnvConfig('KEYCLOAK_LOGOUT_PREFIX');
  return keycloakUrl;
}

const initOptions = {
  onLoad: 'login-required',
  realm: 'TechnicalMiddlePlatform',
  url: getKeycloakUrl(),
  clientId: getKeycloakClientId(),
  'ssl-required': 'external',
  resource: getKeycloakClientId(),
  'public-client': true,
  'confidential-port': 0,
};

export const keycloak = new Keycloak(initOptions);
// export const keycloak = Keycloak(initOptions);
(window as any).$keycloak = keycloak;

export function checkLogin(): Promise<any> {
  return keycloak
    .init({
      onLoad: 'login-required',
      checkLoginIframe: false, // 检测登陆状态
    })
    .then((authenticated) => {
      if (!authenticated) {
        console.error('未登录');
        window.location.reload();
      } else {
        store.commit('UPDATE_REFRESH_TOKEN', keycloak.token);
        axios.interceptors.request.use((config) => {
          if (config.method !== 'put') {
            config.headers.Authorization = 'Bearer ' + keycloak.token;
          }
          return config;
        });
      }
      // 检测token是否刷新
      setInterval(() => {
        keycloak.updateToken(5).then((response) => {
          // keycloak更新成功
          store.commit('UPDATE_REFRESH_TOKEN', keycloak.token);
        });
      }, 30000);
    })
    .catch((error) => {
      console.error('keycloak错误');
      throw error;
    });
}

export async function logout(redirectUrl?: string): Promise<any> {
  const path = redirectUrl || location.href;
  document.cookie = ''; // 退出登录清除cookie
  const keycloakLogoutPrefix = getKeycloakLogoutPrefix();
  const replaceUrl = keycloakLogoutPrefix ? `${keycloakLogoutPrefix}${changeLogoutRedirectUri(path)}` : changeLogoutRedirectUri(path);
  window.location.replace(replaceUrl);
}
function changeLogoutRedirectUri(redirectUri) {
  const url = new URL(keycloak.createLogoutUrl());
  const params = new URLSearchParams(url.search);
  params.set('post_logout_redirect_uri', redirectUri);
  const newUrl = url;
  newUrl.search = params.toString();
  return newUrl.href;
}

// enum AuthType {
//   'home' = 'MGM_FRONT_PAGE',
//   'compute' = 'MGM_ACCOUNTING',
//   'exam' = 'MGM_EXAM',
//   'announcement' = 'MGM_NOTICE',
//   'instance' = 'MGM_INSTANCE',
//   'monitoring' = 'MGM_ALERT',
//   'workorderManage' = 'MGM_TICKET',
//   'helpManage' = 'MGM_HELP',
// }

// export function checkAuth(authType: string): boolean {
//   if (!keycloak.authenticated) {
//     return true;
//   }
//   const authCollections = keycloak.resourceAccess['dlp-train-front']?.roles || [];
//   if (AuthType[authType]) {
//     return authCollections.includes(AuthType[authType]);
//   }
//   return true;
// }

export default {
  getKeycloakUrl,
  checkLogin,
  logout,
  keycloak,
};
