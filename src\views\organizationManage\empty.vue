<template>
  <div class="empty-container">
    <!-- <img src="../../assets/images/<EMAIL>" alt="" /> -->
    <div class="content" v-if="showOperation">
      <p class="title">暂无组织</p>
      <p class="description">
        请立即
        <a href="javascript:;" @click="$emit('create')">新建组织</a>
        或
        <a href="javascript:;" @click="$emit('batchImport')">导入组织</a>
      </p>
    </div>
    <div class="content" v-else>
      <p class="title">抱歉，没有找到相关组织</p>
      <p class="description">您可以换一个关键词试试哦～</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';
const props = defineProps({
  showOperation: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="less" scoped>
.empty-container {
  display: flex;
  justify-content: center;
  .content {
    width: 416px;
    height: 416px;
    padding-top: 300px;
    background: url(../../assets/images/<EMAIL>);
    .title {
      font-size: 18px;
      font-weight: 500;
      color: #121f2c;
    }
    .description {
      font-size: 14px;
      color: #606972;
    }
  }
}
</style>
