<template>
  <div class="form-container">
    <a-form :colon="false" class="form-content" ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-item label="用户名" name="userName">
        <a-input class="max-input-width" v-model:value="form.userName" disabled />
      </a-form-item>
      <a-form-item label="手机号" v-if="phoneLoginValid">
        <p>{{ originPhoneNum || '--' }}</p>
      </a-form-item>
      <a-row type="flex" v-if="phoneNumEditable && phoneLoginValid">
        <a-col flex="394px" style="margin-right: 20px">
          <a-form-item :labelCol="{ span: 10 }" :wrapperCol="{ span: 14 }" class="phoneNum-item" label="修改手机号" name="phoneNum" ref="phoneNumRef" layout="inline">
            <a-input style="width: 228px" placeholder="请输入" v-model:value="form.phoneNum" />
          </a-form-item>
        </a-col>
      </a-row>
      <a-row type="flex" v-if="phoneNumEditable && phoneLoginValid">
        <a-col flex="394px" style="margin-right: 20px">
          <a-form-item name="captcha" :labelCol="{ span: 10 }" :wrapperCol="{ span: 14 }" ref="captchaRef" label="验证码">
            <a-popover overlayClassName="catcha-popover" placement="topRight" trigger="click">
              <template #content>
                <img alt="" style="height: 40px" :src="captchaImgPrefix + captchaData" class="captcha-img" @click="refreshCaptcha" />
              </template>
              <a-input
                class="captcha-input"
                placeholder="请输入图形验证码"
                v-model:value="form.captcha"
                @blur="
                  () => {
                    $refs.captchaRef.onFieldBlur();
                  }
                "
                @change="
                  () => {
                    $refs.captchaRef.onFieldChange();
                  }
                "
              >
                <template #suffix>
                  <img alt="" style="margin-right: -8px" v-if="captchaData" :src="captchaImgPrefix + captchaData" class="captcha-img" @click="refreshCaptcha" />
                </template>
              </a-input>
            </a-popover>
          </a-form-item>
        </a-col>
        <a-col flex="240px">
          <a-form-item name="code" ref="codeRef" label="" :wrapperCol="{ span: 24 }">
            <a-input
              @blur="
                () => {
                  $refs.codeRef.onFieldBlur();
                }
              "
              @change="
                () => {
                  $refs.codeRef.onFieldChange();
                }
              "
              class="code-input"
              placeholder="请输入验证码"
              v-model:value="form.code"
            >
              <template #suffix>
                <a-button style="padding: 0" :disabled="!checkPhoneNum || timmer > 0 || !form.captcha?.trim()" @click="handleSendCode" type="link">
                  {{ timmer > 0 ? `重新获取 ${timmer}` : `${sended ? '重新获取' : '获取验证码'}` }}
                </a-button>
              </template>
            </a-input>
          </a-form-item>
        </a-col>
      </a-row>
      <a-form-item label="姓名" name="fullName">
        <a-input class="max-input-width" placeholder="请输入" v-model:value="form.fullName" />
      </a-form-item>
      <!-- <a-form-item class="section-title" label="身份信息"> </a-form-item> -->
      <a-form-item label="身份" name="identity">
        <a-radio-group v-model:value="form.identity" @change="changeIdentity">
          <a-radio value="开发者"> 开发者 </a-radio>
          <a-radio value="教师"> 教师 </a-radio>
          <a-radio value="学生"> 学生 </a-radio>
        </a-radio-group>
      </a-form-item>

      <a-row type="flex" v-if="form.identity === '教师'" key="教师">
        <a-col :span="4" class="label-col">学校</a-col>
        <a-col :span="12" style="display: flex">
          <a-space align="start">
            <a-form-item>
              <a-select class="small-width" v-model:value="form.area" show-search :options="areaOptions" placeholder="地区" @change="areaChange" />
            </a-form-item>
            <a-form-item name="schoolInput" style="width: 124px" :wrapper-col="{ span: 24 }" v-if="form.area === '其他'">
              <a-input class="middle-width" :placeholder="schoolePlaceholder" v-model:value="form.schoolInput" />
            </a-form-item>
            <a-form-item name="school" v-else style="width: 124px" :wrapper-col="{ span: 24 }">
              <a-select class="middle-width" :placeholder="schoolePlaceholder" show-search v-model:value="form.school" :options="schoolOptions"></a-select>
            </a-form-item>

            <a-form-item class="sub-item" :label-col="{ span: 4 }" :wrapper-col="subWrapperCol" label="院系" name="faculty">
              <a-input class="larger-width" placeholder="请输入" v-model:value="form.faculty" />
            </a-form-item>
          </a-space>
        </a-col>
      </a-row>
      <a-row type="flex" v-if="form.identity === '学生'" key="学生-学校">
        <a-col :span="4" class="label-col">学校</a-col>
        <a-col :span="12" style="display: flex">
          <a-space align="start">
            <a-form-item name="schoolSelect">
              <a-select class="small-width" v-model:value="form.area" show-search :options="areaOptions" placeholder="地区" @change="areaChange" />
            </a-form-item>
            <a-form-item name="schoolInput" style="width: 124px" :wrapper-col="{ span: 24 }" v-if="form.area === '其他'">
              <a-input class="middle-width" :placeholder="schoolePlaceholder" v-model:value="form.schoolInput" />
            </a-form-item>
            <a-form-item name="school" v-else style="width: 124px" :wrapper-col="{ span: 24 }">
              <a-select class="middle-width" :placeholder="schoolePlaceholder" show-search v-model:value="form.school" :options="schoolOptions"></a-select>
            </a-form-item>
            <a-form-item :label-col="{ span: 4 }" :wrapper-col="subWrapperCol" label="院系" name="faculty">
              <a-input class="larger-width" placeholder="请输入" v-model:value="form.faculty" />
            </a-form-item>
          </a-space>
        </a-col>
      </a-row>
      <a-row type="flex" v-if="form.identity === '学生'" key="学生-专业">
        <a-col :span="4" class="label-col">专业</a-col>
        <a-col :span="12" style="display: flex">
          <a-space align="start">
            <a-form-item class="sub-item" :wrapper-col="{ span: 24 }" name="major">
              <a-input class="large-width" placeholder="请输入" v-model:value="form.major" />
            </a-form-item>
            <a-form-item class="sub-item" :label-col="{ span: 4 }" :wrapper-col="subWrapperCol" label="学号" name="stuNum">
              <a-input class="larger-width" placeholder="请输入" v-model:value="form.stuNum" />
            </a-form-item>
          </a-space>
        </a-col>
      </a-row>
      <a-row type="flex" v-if="form.identity === '开发者'" key="开发者">
        <a-col :span="4" class="label-col">工作单位</a-col>
        <a-col style="display: flex">
          <a-space align="start">
            <a-form-item>
              <a-select class="two-input-width" v-model:value="form.companyArea" show-search :options="areaOptions" placeholder="地区" @change="companyAreaChange" />
            </a-form-item>
            <a-form-item name="company" :wrapper-col="{ span: 24, offset: 0 }">
              <a-input class="two-input-width" :disabled="form.companyArea === undefined" placeholder="工作单位" v-model:value="form.company" />
            </a-form-item>
          </a-space>
        </a-col>
      </a-row>

      <a-form-item label="邮箱" name="email">
        <a-input class="max-input-width" placeholder="请输入" v-model:value="form.email" />
      </a-form-item>
      <!-- <a-form-item class="section-title" label="其他信息"> </a-form-item> -->
      <a-form-item class="introduction-item" label="简介" name="introduction">
        <a-textarea class="max-input-width" placeholder="请输入" v-model:value="form.introduction" />
        <span class="count-area">{{ `${(form.introduction || '').length}/${maxCount}` }}</span>
      </a-form-item>

      <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
        <a-space>
          <a-button type="primary" style="width: 96px" @click="onSubmit"> 确定 </a-button>
          <a-button style="width: 72px" @click="$emit('close')"> 取消 </a-button>
        </a-space>
      </a-form-item>
      <a-form-item class="avatar-container" name="image">
        <img-uploader :required="teachAuth" class="uploader" v-model:value="form.image"></img-uploader>
      </a-form-item>
    </a-form>
  </div>
</template>
<script>
import imgUploader from '../components/imgUploader.vue';
import { sendCode } from '../api/sms';
import { chineseOrLetterOrBlankRegex, numberOrLetterOrLineRegex, telephoneNumberRegex, emailRegex, schoolInputRegex } from '../utils';
import { GET } from '@/request';
import { encryptStr } from '../utils/encrypt';
import { getEnvConfig } from '@/config';
import { SMSCODE_TYPE } from '../const';
import { keycloak } from '@/keycloak';

const phoneNumEditable = getEnvConfig('USER_INFO_PHONE_EDITABLE') === '1';
const loginMethod = getEnvConfig('USER_LOGIN_OPTION');

export default {
  components: { imgUploader },
  props: { value: Object, jtPK: String },
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 12 },
      subWrapperCol: { span: 20 },
      other: '',
      form: {
        userName: '',
        fullName: '',
        identity: '',
        phoneNum: '',
        code: '',
        email: '',
        emailChecked: false,
        school: undefined,
        schoolInput: '',
        faculty: '',
        major: '',
        stuNum: '',
        introduction: '',
        image: '',
        area: undefined,
        companyArea: undefined,
        company: '',
        captcha: '',
      },
      timmer: 0,
      maxCount: 30,
      sended: false,
      originPhoneNum: '',
      areaOptions: [],
      schoolOptions: [],
      phoneNumEditable,
      captchaData: '',
      captchaId: '',
      captchaImgPrefix: 'data:image/png;base64,',
    };
  },
  created() {
    this.init();
    this.refreshCaptcha();
  },
  watch: {
    value() {
      this.init();
    },
    // checkPhoneNum(val) {
    //   if (!val) {
    //     this.$refs.codeRef.resetField();
    //     this.$refs.captchaRef.resetField();
    //   }
    // },
  },
  computed: {
    phoneLoginValid() {
      return loginMethod.includes('smscode');
    },
    schoolePlaceholder() {
      const placeholderMap = new Map([
        ['教师', '就职学校'],
        ['学生', '就读学校'],
        ['开发者', '毕业学校'],
      ]);
      return placeholderMap.get(this.form.identity);
    },
    teachAuth() {
      // 开课权限
      return keycloak.idTokenParsed.DLP_USER_ALLOW_PUBLISH_OPEN_COURSE === '1';
    },
    checkPhoneNum() {
      return this.form.phoneNum && this.$refs.phoneNumRef && this.$refs.phoneNumRef.validateState !== 'error';
    },
    rules() {
      return {
        userName: [{ required: true, message: '', trigger: ['blur', 'change'] }],
        identity: [{ required: true, message: '请选择身份', trigger: ['blur', 'change'] }],
        fullName: [
          { required: this.teachAuth, message: '请输入', trigger: ['blur', 'change'] },
          { max: 30, min: 0, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { validator: this.blankValidator, trigger: ['blur', 'change'], message: '请输入' },
        ],
        school: [{ required: true, message: '请选择/输入学校', trigger: ['blur', 'change'] }],
        schoolInput: [
          { required: true, message: '请选择/输入学校', trigger: ['blur', 'change'] },
          { min: 0, max: 50, message: '50个字符以内的中英文，可包含空格、小括号', trigger: ['blur', 'change'] },
          { pattern: schoolInputRegex, message: '50个字符以内的中英文，可包含空格、小括号', trigger: ['blur', 'change'] },
          { validator: this.blankValidator, trigger: ['blur', 'change'], message: '请选择/输入学校' },
        ],
        faculty: [
          { min: 0, max: 30, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { validator: this.blankValidator, trigger: ['blur', 'change'], message: '请输入' },
        ],
        major: [
          { min: 0, max: 30, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { pattern: chineseOrLetterOrBlankRegex, message: '30个字符以内的中英文，可包含空格', trigger: ['blur', 'change'] },
          { validator: this.blankValidator, trigger: ['blur', 'change'], message: '请输入' },
        ],
        stuNum: [
          { min: 0, max: 20, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
          { pattern: numberOrLetterOrLineRegex, message: '20个字符以内的数字、字母和下划线', trigger: ['blur', 'change'] },
        ],
        phoneNum: [{ validator: this.phoneNumValidator, trigger: ['blur', 'change'] }],
        captcha: [{ validator: this.captchaValidator, trigger: ['blur', 'change'] }],
        code: [{ validator: this.codeValidator, trigger: ['blur', 'change'] }],
        email: [
          { required: false, message: '请输入', trigger: ['blur', 'change'] },
          { pattern: emailRegex, message: '请输入正确格式邮箱', trigger: ['blur', 'change'] },
          { min: 6, max: 50, message: '6-50个字符以内', trigger: ['blur', 'change'] },
        ],
        introduction: [
          { required: this.teachAuth, message: '请输入', trigger: ['blur', 'change'] },
          { min: 0, max: 30, message: '不超过30个字符', trigger: ['blur', 'change'] },
        ],
        image: [{ required: this.teachAuth, message: '请上传头像', trigger: 'change' }],
        company: [
          { required: false, message: '请输入工作单位', trigger: ['blur', 'change'] },
          { min: 0, max: 30, message: '30字以内的中英文、空格', trigger: ['blur', 'change'] },
          { pattern: chineseOrLetterOrBlankRegex, message: '30字以内的中英文、空格', trigger: ['blur', 'change'] },
          { validator: this.blankValidator, trigger: ['blur', 'change'], message: '请输入' },
        ],
      };
    },
  },
  methods: {
    blankValidator(rule, value) {
      if (value && !value.trim()) {
        return Promise.reject(new Error());
      } else {
        return Promise.resolve();
      }
    },
    phoneNumValidator(rule, value) {
      if (!value) {
        if (this.form.code || this.form.captcha) {
          return Promise.reject(new Error('请输入手机号'));
        } else {
          return Promise.resolve();
        }
      } else if (!telephoneNumberRegex.test(value)) {
        return Promise.reject('请输入正确格式手机号');
      } else {
        return Promise.resolve();
      }
    },
    captchaValidator(rule, value) {
      if (!value?.trim()) {
        if (this.form.phoneNum) {
          return Promise.reject(new Error('请输入图形验证码'));
        } else {
          return Promise.resolve();
        }
      } else {
        return Promise.resolve();
      }
    },
    codeValidator(rule, value) {
      if (!value?.trim()) {
        if (this.form.phoneNum) {
          return Promise.reject(new Error('请输入验证码'));
        } else {
          return Promise.resolve();
        }
      } else {
        return Promise.resolve();
      }
    },
    changeIdentity(e) {
      // this.$nextTick(() => {
      this.$refs.ruleForm.clearValidate();
      switch (e.target.value) {
        case '教师':
          this.form.faculty = '';
          break;
        case '学生':
          this.form.faculty = '';
          this.form.major = '';
          this.form.stuNum = '';
          break;
        case '开发者':
          this.form.school = '';
          this.form.faculty = '';
          this.form.major = '';
          this.form.stuNum = '';
          break;
        default:
          break;
      }
      // });
    },
    init() {
      this.form = { ...this.form, ...this.value, companyArea: this.value.companyArea || this.value.area };
      this.originPhoneNum = this.form.phoneNum;
      this.form.phoneNum = '';
      if (this.value.area == undefined) {
        this.form.schoolInput = this.value.school;
      }
      this.getAreaOptions();
      this.getArea();
      this.formatDefaultValue();
    },
    formatDefaultValue() {
      this.form.school = this.form.school || undefined;
      this.form.area = this.form.area || undefined;
      this.form.companyArea = this.form.companyArea || undefined;
    },
    getArea() {
      if (!this.form.school) {
        return;
      }
      GET('/web/um/v1/school/province', { school: this.form.school }).then((res) => {
        if (res.code === 0) {
          this.form = { ...this.form, area: res.data && res.data.length > 0 ? res.data : '其他' };
          if (this.form.area !== '其他') {
            this.getSchoolOptions();
          }
        }
      });
    },
    async refreshCaptcha() {
      try {
        const res = await GET('/web/um/v1/public/captcha');
        if (res.code === 0) {
          this.captchaData = res.data.captchaData;
          this.captchaId = res.data.captchaId;
        }
      } catch (error) {
        console.error('获取验证码失败:', error);
      }
    },
    handleSendCode() {
      const params = {
        captchaId: this.captchaId,
        captchaCode: this.form.captcha?.trim(),
        phoneNum: encryptStr(this.form.phoneNum, this.jtPK),
        type: SMSCODE_TYPE.changePhoneNum,
      };
      sendCode(params).then((res) => {
        if (res.code === 0) {
          this.sended = true;
          this.timmer = 60;
          this.$message.success('发送成功');
          this.timmerDecrease();
        } else {
          this.$message.error(res.errorMessage || res.msg || '发送失败');
          this.refreshCaptcha();
        }
      });
    },
    timmerDecrease() {
      if (this.timmer > 0) {
        this.timmer--;
        setTimeout(() => {
          this.timmerDecrease();
        }, 1000);
      }
    },
    onSubmit() {
      // console.log('this.$refs.ruleForm:', this.$refs.ruleForm);
      this.$refs.ruleForm.validate().then((valid) => {
        try {
          if (valid) {
            const form = { ...this.form };
            if (form.identity === '开发者') {
              // form.school = '';
              form.faculty = '';
              form.major = '';
              form.stuNum = '';
            } else if (form.identity === '教师') {
              form.major = '';
              form.stuNum = '';
            }
            const encryptForm = { ...form, area: form.companyArea || form.area, code: form.code?.trim(), phoneNum: encryptStr(form.phoneNum?.trim(), this.jtPK) };
            this.$emit('ok', encryptForm);
          } else {
            console.log('error submit!!');
            return false;
          }
        } catch (error) {
          console.log('error:', error);
        }
      });
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
    areaChange(val) {
      this.form.area = val;
      this.form.school = undefined;
      if (val !== '其他') {
        this.getSchoolOptions();
      }
    },
    companyAreaChange(val) {
      this.form.companyArea = val;
    },
    getAreaOptions() {
      GET('/web/um/v1/province/list', {}).then((res) => {
        if (res.code === 0) {
          this.areaOptions = res.data.map((item) => {
            return {
              value: item,
              label: item,
            };
          });
          this.areaOptions.push({
            value: '其他',
            label: '其他',
          });
        }
      });
    },
    getSchoolOptions() {
      GET('/web/um/v1/province/school', { province: this.form.area }).then((res) => {
        if (res.code === 0) {
          this.schoolOptions = res.data.map((item) => {
            return {
              value: item,
              label: item,
            };
          });
        }
      });
      this.form.schoolInput = '';
    },
  },
};
</script>

<style lang="less" scoped>
.form-container {
  width: 980px;
  .form-content {
    position: relative;
  }
}
.uploader {
  width: 240px;
}
.section-title {
  margin-bottom: 0;
  margin-top: 28px;
  /deep/ label {
    padding-left: 8px;
    border-left: 4px solid #0082ff;
  }
}
.avatar-container {
  position: absolute;
  top: 0;
  right: -30px;
  /deep/ .ant-form-item-control-wrapper {
    width: 100%;
    text-align: center;
  }
}
.code-input {
  padding: 0px 11px;
}

.sms-code-input {
  position: relative;
  display: flex;
  align-items: center;
  .send-code-btn {
    position: absolute;
    right: 0;
  }
}
.extra-info {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
  width: 624px;
  height: 148px;
  padding-left: 76px;
  align-items: flex-start;
  &.one-line {
    max-height: 120px;
  }
}
.introduction-item {
  position: relative;
  .count-area {
    position: absolute;
    top: 30px;
    right: 20px;
  }
}
.school-item {
  margin-bottom: 0;
  padding-top: 4px;
  padding-bottom: 4px;
  /deep/ .ant-form-item-children {
    display: flex;
  }
  /deep/ .ant-form-item-label {
    line-height: 32px;
  }
}
.small-width {
  width: 94px !important;
}
.middle-width {
  width: 124px;
}
.large-width {
  width: 226px;
}
.larger-width {
  width: 206px;
}
.two-input-width {
  width: 235px !important;
}
.max-input-width {
  width: 480px;
}
//.captcha-input {
//  width: 120px;
//}
.captcha-img {
  height: 24px;
  cursor: pointer;
}

.label-col {
  text-align: right;
  padding-right: 8px;
  line-height: 36px;
}
</style>

<style>
.catcha-popover .ant-popover-inner-content {
  padding: 4px;
}
</style>
