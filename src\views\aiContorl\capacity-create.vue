<template>
  <div class="container">
    <loading v-if="store.state.showGlobalLoading"></loading>
    <!-- 底部版权信息 -->
    <div class="bar-copyright">Copyright © 2021 中国移动 版权所有</div>
    <!-- 面包屑 -->
    <div class="bread-crumb">
      <breadCrumb :need-confirm="true" :value="pathLink" @handle-confirm="handleConfirm"></breadCrumb>
    </div>
    <div class="content" ref="contentRef">
      <!-- 页面头部 -->
      <div class="content-header">
        <h2 class="content-header-title">{{ title }}</h2>
        <div class="steps-box">
          <a-steps :current="current">
            <a-step title="能力基本信息" />
            <a-step title="功能演示相关素材" />
            <a-step title="功能介绍及使用场景" />
            <a-step title="技术特色及常见问题" />
            <a-step title="能力推荐卡片" />
            <a-step title="对应技术文档" />
          </a-steps>
        </div>
      </div>

      <!-- 组件 -->
      <keep-alive>
        <baseCapacityInfo v-if="current === 0" ref="baseInfoRef" :is-edit="isEdit" :app-info="appInfo" @to-next="handleToNext" @save-draft="handleSaveDraft" @handle-confirm="handleConfirm"></baseCapacityInfo>
        <functionDemonstration v-else-if="current === 1" ref="materialRef" :is-edit="isEdit" :app-info="appInfo" @to-pre="handleToPre" @save-draft="handleSaveDraft" @to-next="handleToNext"></functionDemonstration>
        <functionIntro v-else-if="current === 2" :is-edit="isEdit" :app-info="appInfo" :icon-list="functionIconList" :capacity-type-map="capacityTypeMap" @to-pre="handleToPre" @to-next="handleToNext" @save-draft="handleSaveDraft" ref="functionIntroRef"></functionIntro>
        <technologyFeature v-else-if="current === 3" :is-edit="isEdit" :app-info="appInfo" :origin-options="originOptions" :icon-list="featureIconList" @to-pre="handleToPre" @to-next="handleToNext" @save-draft="handleSaveDraft" ref="technologyFeatureRef"></technologyFeature>
        <abilityRecommend v-else-if="current === 4" :is-edit="isEdit" :app-info="appInfo" @to-pre="handleToPre" @to-next="handleToNext" @save-draft="handleSaveDraft" ref="abilityRecommendRef"></abilityRecommend>
        <technicalDocuments v-else-if="current === 5" :is-edit="isEdit" :app-info="appInfo" @to-pre="handleToPre" @to-next="handleToNext" @save-draft="handleSaveDraft"></technicalDocuments>
      </keep-alive>
    </div>
    <template v-if="visible">
      <confirmModal :visible="visible" @handle-ok="handleModalOk" @handle-cancel="handleModalCancel" @handle-close="handleModalClose"></confirmModal>
    </template>
  </div>
</template>

<script setup lang="ts">
import request, { requestBlob } from '@/request';
import type { SelectProps } from 'ant-design-vue';
import { message } from 'ant-design-vue';
import { ref, reactive, onBeforeUnmount, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { isAbsolutePath, openInNewTab } from '@/utils';

import breadCrumb from '@/components/breadCrumb.vue';
import baseCapacityInfo from './components/base-capacity-info.vue';
import functionDemonstration from './components/function-demonstration.vue';
import functionIntro from './components/functionIntro.vue';
import technologyFeature from './components/technologyFeature.vue';
import abilityRecommend from './components/abilityRecommend.vue';
import technicalDocuments from './components/technicalDocuments.vue';
import { useDraftSaveTask } from './components/components/draftSave';
import loading from './components/Loading.vue';
import confirmModal from './components/components/confirmModal.vue';

const contentRef = ref();
// 子组件实例
const baseInfoRef = ref();
const materialRef = ref();
const functionIntroRef = ref();
const technologyFeatureRef = ref();
const abilityRecommendRef = ref();

const title = ref('');
const current = ref(0);

const pathLink = reactive([
  {
    name: '开放能力',
  },
  {
    name: '能力管理',
    path: '/capacity-management',
  },
  {
    name: '新建能力',
  },
]);
const isEdit = ref(false);
const appInfo = ref({});
const store = useStore();
// 图标库
const functionIconList: any = ref([]);
const featureIconList: any = ref([]);

const capacityTypeMap = ref([]);
const optionsUse = ref(true);
const { draftSaveTask } = useDraftSaveTask(store, optionsUse);
// 保存草稿标志位
const isSaveDraft = computed(() => {
  return store.state.isSaveDraft;
});
// 模态框显隐
const visible = computed(() => {
  return store.state.modalVisible;
});

const route = useRoute();
const router = useRouter();
const capacityName = route.params.name;
const source = route.query.key;
// 请求功能介绍图标库
request('/aiipweb/om-service/os/getPictureList', {
  method: 'GET',
  data: {
    category: 1,
  },
}).then((res: any) => {
  const tempList = res.body.map((item) => {
    return {
      key: item,
      value: `/aiipweb/om-service/os/getObject?category=1&object=${item}`,
    };
  });
  const requests = tempList.map((item) => {
    return requestBlob(item.value, { method: 'GET' });
  });

  Promise.all(requests).then((result: any) => {
    for (let i = 0; i < result.length; i++) {
      const tempData = {
        key: tempList[i].key,
        value: result[i],
      };
      functionIconList.value.push(tempData);
    }
  });
});
// 请求技术特色图标库
request('/aiipweb/om-service/os/getPictureList', {
  method: 'GET',
  data: {
    category: 0,
  },
}).then((res: any) => {
  const tempList = res.body.map((item) => {
    return {
      key: item,
      value: `/aiipweb/om-service/os/getObject?category=0&object=${item}`,
    };
  });
  const requests = tempList.map((item) => {
    return requestBlob(item.value, { method: 'GET' });
  });

  Promise.all(requests).then((result: any) => {
    for (let i = 0; i < result.length; i++) {
      const tempData = {
        key: tempList[i].key,
        value: result[i],
      };
      featureIconList.value.push(tempData);
    }
  });
});
// 请求已上架能力列表，并过滤掉本能力。
const originOptions = ref<SelectProps['options']>([]);
request('/aiipweb/om-service/capability/queryList', {
  method: 'POST',
  data: {
    name: '',
    orderByModifyTimeDesc: true,
    pageNum: 1,
    pageSize: 1000,
    statusList: [1],
    type: '',
  },
}).then((res: any) => {
  const temp = res.body.data.map((item) => {
    return {
      value: item.id,
      label: item.name,
      disabled: false,
    };
  });
  originOptions.value = temp.filter((item) => item.label !== capacityName);
});
// 请求能力类别映射表
request('/aiipweb/om-service/dict/getDict', {
  method: 'GET',
  data: { dictName: 'category' },
})
  .then((res: any) => {
    capacityTypeMap.value = res.body;
  })
  .catch(() => {
    capacityTypeMap.value = [];
  });
// 保存草稿
watch(
  isSaveDraft,
  (newValue) => {
    if (newValue) {
      if (current.value === 0) {
        baseInfoRef.value?.updateToStore();
      } else if (current.value === 1) {
        materialRef.value?.updateToStore();
      } else if (current.value === 2) {
        functionIntroRef.value.updateToStore();
      } else if (current.value === 3) {
        technologyFeatureRef.value.updateToStore();
      } else if (current.value === 4) {
        abilityRecommendRef.value.abilityRecommendStore();
      }
      const path = store.state.toPath;
      draftSaveTask().then(() => {
        isAbsolutePath(path) ? openInNewTab(path) : router.push({ path });
      });
      store.commit('closeSaveDraft');
    }
  },
  {
    immediate: true,
  }
);
// 编辑页面回显
if (capacityName !== undefined) {
  title.value = '编辑能力';
  pathLink[2].name = '编辑能力';
  store.commit('openGlobalLoading');
  request('/aiipweb/om-service/capability/edit-show', {
    method: 'GET',
    data: {
      name: capacityName,
    },
  }).then((res: any) => {
    store.commit('closeGlobalLoading');
    isEdit.value = true;
    appInfo.value = res.body;
    store.commit('updateAppInfo', res.body);
    if (res.body.id !== null) {
      if (source === 'capacity') {
        if (res.body.abilityId !== null) {
          message.info('该能力存在草稿，已为您打开此草稿内容');
        }
      }
    }
  });
} else {
  title.value = '新建能力';
  pathLink[2].name = '新建能力';
  isEdit.value = false;
}
// 处理模态框相关
const handleConfirm = (path) => {
  store.commit('openModal', path);
};
// 保存并离开
const handleModalOk = () => {
  store.commit('openSaveDraft');
};
// 直接离开
const handleModalCancel = () => {
  const path = store.state.toPath;
  store.commit('closeSaveDraft');
  isAbsolutePath(path) ? openInNewTab(path) : router.push({ path });
};
// 仅关闭模态框
const handleModalClose = () => {
  store.commit('closeSaveDraft');
};

// 上一步
const handleToPre = () => {
  current.value -= 1;
  contentRef.value.scrollTop = 0;
};

const handleToNext = () => {
  current.value += 1;
  contentRef.value.scrollTop = 0;
};

const handleSaveDraft = () => {
  draftSaveTask();
};

onBeforeUnmount(() => {
  store.commit('resetAppInfo');
});
</script>

<style lang="less" scoped>
.container {
  position: absolute;
  left: 0;
  right: 0;
  top: 50px;
  bottom: 0;
  background: #eff1f4;
  overflow-y: scroll;
  .bar-copyright {
    position: absolute;
    bottom: 24px;
    left: 50%;
    width: 240px;
    text-align: center;
    height: 18px;
    margin-left: -103px;
    font-size: 12px;
    font-weight: 400;
    color: #aaacb4;
    line-height: 18px;
  }
  .content {
    position: absolute;
    top: 58px;
    right: 0;
    bottom: 0;
    left: 0;
    overflow-y: auto;
    padding: 20px 20px 90px 20px;
    &-header {
      padding: 20px;
      height: 160px;
      border-radius: 2px;
      background-color: #fff;
      /deep/ .ant-steps .ant-steps-item:not(.ant-steps-item-active) > .ant-steps-item-container[role='button'] {
        cursor: auto;
      }
      &-title {
        margin-bottom: 0;
        height: 24px;
        font-size: 16px;
        font-weight: 500;
        color: #121f2c;
        line-height: 24px;
      }
      .steps-box {
        padding: 40px 44px 0 44px;
        /deep/ .ant-steps-item-icon {
          width: 28px;
          height: 28px;
          line-height: 28px;
          font-size: 14px;
          border-radius: 28px;
        }
        /deep/ .ant-steps-item-title {
          padding-right: 8px;
          font-size: 14px;
          font-weight: 500;
        }
        /deep/ .ant-steps-item-container {
          display: flex;
          align-items: center;
        }
      }
    }
  }
}
</style>
