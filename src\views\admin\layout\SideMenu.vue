<template>
  <div class="message-left-menu">
    <a-menu class="menu" @click="handleClickItem" mode="inline" theme="light" :selectedKeys="selectedKeys">
      <template v-for="subItem in menu">
        <template v-if="subItem.children">
          <a-menu-item-group :key="subItem.name">
            <template v-slot:title>
              <jt-icon :type="subItem.icon" style="padding-right: 9px" /><span>{{ subItem.name }}</span>
            </template>
            <a-menu-item v-for="child in subItem.children" :key="child.link"> {{ child.name }}<a-badge style="padding-left: 8px" v-if="child.link === '/unreadMessage' && unreadCount > 0" :count="unreadCount" /> </a-menu-item>
          </a-menu-item-group>
        </template>
        <template v-else>
          <a-menu-item :key="subItem.link"><jt-icon :type="subItem.icon" style="padding-right: 9px"></jt-icon>{{ subItem.name }} </a-menu-item>
        </template>
      </template>
    </a-menu>
  </div>
</template>
<script>
import menuConfig from './menuConfig';

export default {
  name: 'Menu',
  data() {
    return {
      menu: menuConfig,
      selectedKeys: [],
    };
  },
  watch: {
    $route: {
      handler(route) {
        this.selectedKeys = [route.path || route?.meta.link];
      },
      immediate: true,
    },
  },
  methods: {
    handleClickItem({ key }) {
      this.$router.push(key).catch((err) => err);
    },
  },
};
</script>

<style lang="less" scoped>
.message-left-menu {
  /deep/.ant-menu-item-group {
    .ant-menu-item-group-title {
      padding: 10px 16px 10px 25px;
      color: #16161c;
    }
    .ant-menu-item-group-list {
      .ant-menu-item {
        padding-left: 48px !important;
        display: flex;
        align-items: center;
        .ant-badge {
          .ant-badge-count {
            padding: 0 6px;
            height: 16px;
            line-height: 15px;
            background-color: #f24444;
          }
        }
      }
      .ant-menu-selected::after,
      .ant-menu-item-selected::after {
        display: none;
      }
      li.ant-menu-item {
        margin-top: 0px;
        margin-bottom: 0px;
      }
    }
  }
  .ant-menu-inline,
  .ant-menu-vertical,
  .ant-menu-vertical-left {
    border-right: none;
  }

  /deep/ .ant-menu-inline .ant-menu-item:after,
  .ant-menu-vertical .ant-menu-item:after,
  .ant-menu-vertical-left .ant-menu-item:after,
  .ant-menu-vertical-right .ant-menu-item:after {
    display: none;
  }
}
/deep/ .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background: #fff;
}
</style>
