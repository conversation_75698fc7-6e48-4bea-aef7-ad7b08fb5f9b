<template>
  <div class="title jt-head-title">
    <div class="tip"></div>
    <div>{{ title }}</div>
  </div>

  <!-- <h3 class="jt-head-title">{{ title }}</h3> -->
</template>
<!--
  标题效果组件（前有一个label横杠）
  <head-title :title="这是一个标题"/>
 -->
<script>
import { defineComponent } from 'vue';
export default defineComponent({
  name: 'HeadTitle',
  props: {
    title: {
      type: String,
      required: true,
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/assets/styles/index.less';

.title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  font-size: 14px;
}

.tip {
  content: '';
  display: inline-block;
  width: 4px;
  height: 17px;
  background-color: @jt-primary-color;
  margin-right: 8px;
  margin-top: 1px;
}

.jt-head-title {
  color: @jt-title-color;
  // font-size: @jt-font-size-lger;
  position: relative;
  margin-left: 12px;
  font-size: 14px;
  font-weight: 500;
}

</style>
