import request from '@/request';

const APIs = {
  logList: '/web/admin/eventrack/v1/event-logs', // 获取运营端操作日志列表
  logExport: '/web/admin/eventrack/v1/event-log/export-event-log-detail', // 导出运管操作日志详情表
  logDetail: '/web/admin/eventrack/v1/event-log/detail', // 获取运营端操作日志详情
  logModelType: '/web/admin/eventrack/v1/event-modules', // 获取运营端操作日志模块类型
};

const ApiOperationLog = {
  ApiLogList: (data: any = {}) => {
    return request(APIs.logList, {
      method: 'POST',
      data,
    });
  },
  ApiLogExport: (data: any = {}) => {
    return request(APIs.logExport, {
      method: 'POST',
      data,
    });
  },
  APILogDetail: (data: any = {}) => {
    return request(APIs.logDetail, {
      method: 'GET',
      data,
    });
  },
  APILogModelType: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.logModelType, {
      method: type || 'GET',
      data,
    });
  },
};
export default ApiOperationLog;
