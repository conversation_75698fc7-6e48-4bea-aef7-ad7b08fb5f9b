import { getProxyPrefix } from '@/config';

import Axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

import { notification } from 'ant-design-vue';
const proxyPrefix = getProxyPrefix();

const isDev = process.env.NODE_ENV === 'development';
const baseURL = isDev ? `/api/${proxyPrefix}` : './';

export const axios = Axios.create({
  baseURL: baseURL,
  withCredentials: true, // set cross-origin
  headers: {
    'Content-Type': 'application/json',
  },
});

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (err) => {
    if (err.response.status === 401) {
      throw new Error('未登录');
    }
    if (err.response.status === 403) {
      notification.error({
        message: `无权限`,
        description: '抱歉，无操作权限',
      });
    } else {
      notification.error({
        message: `系统错误`,
        description: err.response.config.url,
      });
    }
    throw err;
  }
);

export interface Options {
  method?: AxiosRequestConfig['method'];
  data?: AxiosRequestConfig['data'];
  useError?: boolean;
  returnType?: 'url' | 'blob';
  headers?: AxiosRequestConfig['headers'];
}

function request(url: string, options: Options = {}): Promise<any> {
  const { method = 'GET', data = {}, useError = true, headers = {} } = options;
  return axios({
    url,
    method,
    params: method === 'GET' ? data : undefined,
    data: ['PUT', 'POST', 'DELETE'].includes(method) ? data : undefined,
    headers,
  } as AxiosRequestConfig)
    .then((res) => {
      if (isDev && useError && (res.data.errorCode === '-500' || res.data.errorCode === '-503')) {
        notification.error({
          message: '系统错误',
          description: `${res.config.url}: ${res.data.errorMessage}`,
        });
        throw new Error(res.data);
      }
      return res.data;
    })
    .catch((err) => {
      throw err;
    });
}

export function POST(url, data, options = {}) {
  return request(url, { method: 'POST', data, useError: true, ...options });
}
export function GET(url, data) {
  return request(url, { method: 'GET', data, useError: true });
}

export function requestBlob(url: string, options: Options = {}) {
  const { method = 'GET', data = {}, useError = true, returnType = 'url', headers = {} } = options;
  return axios({
    url,
    method,
    params: method === 'GET' ? data : undefined,
    data: ['PUT', 'POST', 'DELETE'].includes(method) ? data : undefined,
    responseType: 'blob',
    headers,
  } as AxiosRequestConfig)
    .then((res) => {
      if (isDev && useError && (res.data.errorCode === '-500' || res.data.errorCode === '-503')) {
        notification.error({
          message: '系统错误',
          description: `${res.config.url}: ${res.data.errorMessage}`,
        });
        throw new Error(res.data);
      }

      return returnType === 'url' ? URL.createObjectURL(new Blob([res.data])) : (res.data as Blob);
    })
    .catch((err) => {
      throw err;
    });
}

// 文件上传和下载相关接口，不需要登录态
export const axiosWithNoToken = Axios.create({
  baseURL: baseURL, // 本地
  withCredentials: true, // set cross-origin
});

export default request;
