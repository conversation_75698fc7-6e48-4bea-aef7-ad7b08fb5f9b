<template>
  <div class="question-manage">
    <a-space :class="['operation-group', !FEATURE_SMS && 'input-gap']">
      <a-select style="width: 200px" @change="changeProject" v-model:value="platform">
        <a-select-option v-for="(x, i) in platformList" :value="x" :key="i">{{ x }}</a-select-option>
      </a-select>
      <a-input v-model:value="keyword" @change="handleChange" placeholder="工单号/问题描述/用户名/最后处理人" style="min-width: 280px; height: 32px">
        <template #prefix>
          <jt-icon type="iconsousuo" style="color: #bec2c5"></jt-icon>
        </template>
      </a-input>
    </a-space>
    <a-table :loading="tableAttr(loading).loading" :columns="columns" :data-source="dataSource" @change="handleTableChange" :customRow="customRow" :pagination="false">
      <template #emptyText>
        <empty
          v-if="!loading"
          title="工单"
          :showNoDataText="
            showNoDataText({
              currentCatageoryId,
              status,
              keyword,
            })
          "
        >
        </empty>
      </template>
      <template #statusTitleSlot>
        <span>状态</span>
        <span class="filter-group">
          <jt-icon class="filter-icon" ref="statusFilterVisibleIconref" :class="((status !== undefined && status !== null && status !== '') || statusFilterVisible) && 'filter-icon-select'" type="iconshaixuan" @click="handleFilterIcon('statusFilterVisible')" />
          <teleport to="body">
            <div class="filter-item-list" v-show="statusFilterVisible" :style="filterPosition">
              <div v-for="item in tabsData" :key="item.id" class="filter-item" :class="item.id === status && 'catageory-select'">
                <div @click="() => handleFilter(item.id, 'status')">{{ item.name }}</div>
              </div>
            </div>
          </teleport>
        </span>
      </template>
      <template #questionSlot>
        <span>问题分类</span>
        <span class="filter-group">
          <jt-icon class="filter-icon" ref="statusCatogeryFilterVisibleIconref" :class="(currentCatageoryId || statusCatogeryFilterVisible) && 'filter-icon-select'" type="iconshaixuan" @click="handleFilterIcon('statusCatogeryFilterVisible')" />
          <teleport to="body">
            <div class="filter-item-list" v-show="statusCatogeryFilterVisible" :style="filterPosition">
              <div v-for="item in statusCatogery" :key="item.categoryId" class="filter-item" :class="item.categoryId === currentCatageoryId && 'catageory-select'">
                <div @click="() => handleFilter(item.categoryId, 'currentCatageoryId')">{{ item.category }}</div>
              </div>
            </div>
          </teleport>
        </span>
      </template>
      <template #numberSlot="{ record }">
        <span class="order-number">{{ record.ticketNum }}</span>
      </template>
      <template #statusSlot="{ record }">
        <a-tag size="small" :color="record.status === 0 ? 'red' : record.status === 1 ? 'blue' : 'green'">{{ record.status === 0 ? '待处理' : record.status === 1 ? '处理中' : '已关闭' }}</a-tag>
      </template>
    </a-table>
    <Pagination :total="pagination.total" :pageNum="pagination.pageNum" :pageSize="pagination.pageSize" :pageSizeOptions="[10, 20, 50, 100]" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref, getCurrentInstance, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import request from '@/request';
import { onClickOutside } from '@vueuse/core';
import Pagination from '@/components/pagination.vue';
import queryParamsMixin from '@/mixins/queryParamsMixin.js';
import { tableAttr, showNoDataText } from '@/utils';
import { checkKeycloakAuth } from '@/utils/auth';
import Empty from '@/components/empty.vue';
import { getEnvConfig } from '@/config';
import { debounce } from 'lodash';

interface catageryItem {
  categoryId: string;
  category: string;
}

interface tabItem {
  id: number | null;
  name: string;
}
export default defineComponent({
  components: {
    Pagination,
    Empty,
  },
  mixins: [queryParamsMixin],
  setup(props, context) {
    const router = useRouter();
    const route = useRoute();
    const query = route.query;
    let status = ref<string | null>((query.status as string) || null);
    let loading = ref<boolean>(true);
    const keyword = ref<string>((query.keyword as string) || '');
    const tabsData = ref<tabItem[]>([
      {
        id: null,
        name: '全部',
      },
      {
        id: 0,
        name: '待处理',
      },
      {
        id: 1,
        name: '处理中',
      },
      {
        id: 2,
        name: '已关闭',
      },
    ]);
    const dataSource = ref<any[]>([]);
    const currentCatageoryId = ref(query.currentCatageoryId || '');
    const columns = ref([
      {
        dataIndex: 'ticketNum',
        key: 'ticketNum',
        title: '工单号',
        slots: {
          customRender: 'numberSlot',
        },
        width: '180px',
      },
      {
        dataIndex: 'description',
        key: 'description',
        title: '问题描述',
        width: '20%',
        ellipsis: true,
      },
      {
        dataIndex: 'userName',
        key: 'userName',
        title: '用户名',
      },
      {
        dataIndex: 'category',
        key: 'category',
        slots: {
          title: 'questionSlot',
        },
        width: '140px',
      },
      {
        dataIndex: 'status',
        key: 'status',
        slots: {
          title: 'statusTitleSlot',
          customRender: 'statusSlot',
        },
        width: '100px',
      },
      {
        dataIndex: 'createTime',
        key: 'createTime',
        title: '创建时间',
        sorter: true,
        sortOrder: false,
        width: '180px',
        customRender({ text }) {
          return text || '--';
        },
      },
      {
        dataIndex: 'updateTime',
        key: 'updateTime',
        title: '最近处理时间',
        sorter: true,
        sortOrder: false,
        width: '180px',
        customRender({ text }) {
          return text || '--';
        },
      },
      {
        dataIndex: 'handler',
        key: 'handler',
        title: '最后处理人',
        ellipsis: true,
        customRender({ text }) {
          return text || '--';
        },
      },
    ]);
    const FEATURE_SMS = getEnvConfig('FEATURE_SMS') === '1';

    const instance: any = getCurrentInstance();
    // 问题分类
    const statusCatogery = ref<catageryItem[]>([]);
    const orderColumn = ref(query.orderColumn || '');
    const isAsc = ref(query.isAsc === 'true' ? true : false);
    const pagination = reactive({
      pageNum: query.pageNum ? Number(query.pageNum) : 1,
      pageSize: query.pageSize ? Number(query.pageSize) : 10,
      total: 0,
    });
    const statusCatogeryFilterVisibleIconref: any = ref(null);
    const statusCatogeryFilterVisible = ref(false);
    const statusFilterVisibleIconref: any = ref(null);
    const statusFilterVisible = ref(false);
    const filterPosition = reactive({
      left: '0px',
      top: '0px',
    });
    onClickOutside(statusCatogeryFilterVisibleIconref, (event) => {
      statusCatogeryFilterVisible.value = false;
    });
    onClickOutside(statusFilterVisibleIconref, (event) => {
      statusFilterVisible.value = false;
    });
    // 筛选逻辑更新
    const handleFilter = (id, key) => {
      eval(key).value = id;
      pagination.pageNum = 1;
      instance.proxy.rewriteUrlParamsMixin({ pageNum: 1, [key]: id });
      getTableData();
    };
    const calcutateFilterPosition = (ref) => {
      filterPosition.left = (ref.value as Element).getBoundingClientRect().x + 5 + 'px';
      filterPosition.top = (ref.value as Element).getBoundingClientRect().y + 20 + 'px';
    };

    const handleFilterIcon = (key) => {
      !eval(key).value && calcutateFilterPosition(eval(key + 'Iconref'));
      eval(key).value = !eval(key).value;
    };

    // 获取工单列表信息
    const getTableData = async () => {
      loading.value = true;
      const reqData = {
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
        keyword: keyword.value,
        status: status.value === null ? undefined : Number(status.value),
        categoryId: currentCatageoryId.value === '' ? undefined : currentCatageoryId.value,
        platform: platform.value,
      };
      if (orderColumn.value) {
        reqData[orderColumn.value + 'Sort'] = isAsc.value ? 'asc' : 'desc';
      }
      const res = await request('/ticket/web/list', { method: 'POST', data: reqData });
      if (res.state === 'OK') {
        dataSource.value = res.body.list ?? [];
        pagination.total = res.body.total;
      }
      loading.value = false;
    };

    // 获取工单分类
    const getWorkorderCatagery = async () => {
      const data = {
        platform: platform.value,
      };
      const res = await request('/ticket/web/getCategorys', { method: 'GET', data });
      if (res.state === 'OK') {
        statusCatogery.value = [{ categoryId: '', category: '全部' }, ...res.body];
        context.emit('updateStatusCatogery', statusCatogery.value);
      }
    };
    const changePageNum = (pageNum) => {
      pagination.pageNum = pageNum;
      instance.proxy.rewriteUrlParamsMixin({ pageNum });
      getTableData();
    };

    const changePageSize = (pageSize) => {
      pagination.pageSize = pageSize;
      pagination.pageNum = 1;
      instance.proxy.rewriteUrlParamsMixin({ pageNum: 1, pageSize });
      getTableData();
    };

    // 点击排序操作
    const handleTableChange = (page, filter, sort) => {
      instance.proxy.changeOrderColumn(sort.field, sort.order);
      orderColumn.value = sort.field;
      isAsc.value = sort.order == 'ascend';
      if (!sort.order) {
        orderColumn.value = '';
        isAsc.value = false;
        instance.proxy.rewriteUrlParamsMixin({ orderColumn: undefined, isAsc: undefined });
      } else {
        instance.proxy.rewriteUrlParamsMixin({ orderColumn: orderColumn.value, isAsc: isAsc.value });
      }
      getTableData();
    };

    const changeProject = () => {
      currentCatageoryId.value = '';
      getWorkorderCatagery();
      pagination.pageNum = 1;
      pagination.pageSize = 10;
      instance.proxy.rewriteUrlParamsMixin({ pageNum: 1, pageSize: 10, platform: platform.value, currentCatageoryId: undefined });
      getTableData();
    };

    const toWorkorderDetail = (record) => {
      router.push({ path: `/workorder-manage/detail/${record.ticketNum}`, query: route.query });
    };

    const platform = ref(query.platform || undefined);
    context.emit('updatePlatform', platform.value);
    const platformList = ref<[]>();
    /**
     * 获取产品列表
     * @description 如果没有默认项，则设置列表中的第一项为默认项
     */
    const getPlatformByUser = async () => {
      return request('/ticket/web/getPlatformByUser', { method: 'GET' }).then((res) => {
        if (res.state === 'OK') {
          platformList.value = res.body;
          if (!platform.value) {
            platform.value = res.body[0] ?? '';
          }
        }
      });
    };

    const handleChange = debounce(() => {
      instance.proxy.rewriteUrlParamsMixin({ keyword: keyword.value, pageNum: 1 });
      pagination.pageNum = 1;
      getTableData();
    }, 500);

    // 将平台的参数带到url上
    watch(platform, (state) => {
      context.emit('updatePlatform', state);
    });

    const customRow = (record) => {
      return {
        style: { cursor: 'pointer' },
        onClick: () => {
          toWorkorderDetail(record);
        },
      };
    };

    onMounted(async () => {
      await getPlatformByUser();
      getWorkorderCatagery();
      getTableData();
    });
    return {
      keyword,
      status,
      dataSource,
      columns,
      FEATURE_SMS,
      tabsData,
      pagination,
      statusCatogery,
      loading,
      currentCatageoryId,
      statusFilterVisibleIconref,
      statusFilterVisible,
      statusCatogeryFilterVisibleIconref,
      statusCatogeryFilterVisible,
      filterPosition,
      getTableData,
      toWorkorderDetail,
      handleTableChange,
      handleFilter,
      getWorkorderCatagery,
      handleFilterIcon,
      changePageNum,
      changePageSize,
      changeProject,
      platformList,
      platform,
      checkKeycloakAuth,
      tableAttr,
      showNoDataText,
      handleChange,
      customRow,
    };
  },
});
</script>

<style lang="less" scoped>
@import '@/assets/styles/index.less';
.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}
.operation-group {
  position: absolute;
  right: 226px;
  top: 20px;
  margin-bottom: 20px;
  z-index: 1;
  .order-tabs {
    display: flex;
    align-items: center;
    width: 48%;
    min-width: 360px;
    .tab-item {
      width: 25%;
      cursor: pointer;
      text-align: center;
      border-bottom: 1px solid #e0e1e1;
      height: 32px;
      &:hover {
        color: @jt-primary-color;
      }
    }
    .tab-item-select {
      color: @jt-primary-color;
      border-bottom: 2px solid @jt-primary-color;
    }
  }
  &.input-gap {
    right: 116px;
  }
}
.question-manage {
  .order-number {
    cursor: pointer;
    &:hover {
      color: @jt-primary-color;
    }
  }
  .filter-group {
    position: relative;
    margin-left: 5px;
    .filter-icon {
      cursor: pointer;
      color: #bfbfbf;
    }
    .filter-icon-select {
      color: @jt-primary-color;
    }
  }
}
.filter-item-list {
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  position: absolute;
  left: 0px;
  top: 16px;
  display: flex;
  flex-direction: column;
  width: 90px;
  z-index: 1;
  .filter-item {
    cursor: pointer;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 12px;
    color: #606972;
    &:hover {
      background-color: #f0f8ff;
      color: @jt-primary-color;
    }
  }
  .filter-item.catageory-select {
    background-color: #f0f8ff;
    color: @jt-primary-color;
  }
}
</style>
