<template>
  <div>
    <sub-header :breadCrumb="breadCrumbs">
      <template #subHeader>
        <div class="button-group">
          <a-button type="primary" style="margin-right: 8px" :disabled="orderDetail.lastTicketNum === '-1'" @click="getOtherOrderDetail(orderDetail.lastTicketNum)">上一条</a-button>
          <a-button type="primary" :disabled="orderDetail.nextTicketNum === '-1'" @click="getOtherOrderDetail(orderDetail.nextTicketNum)">下一条</a-button>
        </div>
      </template>
    </sub-header>
    <Container>
      <container-item>
        <div class="workorder-items">
          <div class="workorder-items-header">
            <h3>工单详情</h3>
            <a-space>
              <a-button type="outline" v-if="orderDetail.status === 1 || orderDetail.status === 2" @click="addHandleResult">新增处理结果</a-button>
              <a-button type="primary" :disabled="handleLoading" v-if="startBtnDisplay" @click="() => throttleHandleOrder(orderDetail.status)">{{ startBtnText }}</a-button>
            </a-space>
          </div>
          <div v-if="isDemandOrder" class="workorder-items-content">
            <a-row class="content-item">
              <a-col :span="6">
                <span class="item-label">工单号：</span>
                <span>{{ orderDetail.ticketNum }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">创建时间：</span>
                <span>{{ orderDetail.createTime }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">大模型类型：</span>
                <span>{{ showEmptyText(orderDetail.largeModelType) }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">状态：</span>
                <a-tag v-if="orderDetail.status !== undefined" size="small" :color="typeObject.color">{{ typeObject.txt }}</a-tag>
              </a-col>
            </a-row>
            <a-row class="content-item">
              <a-col :span="6">
                <span class="item-label">期望交付时间：</span>
                <span>{{ showEmptyText(orderDetail.expectedTime) }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">部署方式：</span>
                <span>{{ showEmptyText(orderDetail.deploymentModel) }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">项目名称：</span>
                <span>{{ showEmptyText(orderDetail.projectName) }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">项目阶段：</span>
                <span>{{ showEmptyText(orderDetail.projectPhase) }}</span>
              </a-col>
            </a-row>
            <a-row class="content-item">
              <a-col :span="6">
                <span class="item-label">联系人：</span>
                <span>{{ showEmptyText(orderDetail.contactName) }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">联系方式：</span>
                <span>{{ showEmptyText(orderDetail.contactTel) }}</span>
              </a-col>

              <a-col :span="6">
                <span class="item-label">邮箱信息：</span>
                <span>{{ showEmptyText(orderDetail.contactEmail) }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">用户名：</span>
                <span>{{ orderDetail.userName }}</span>
              </a-col>
            </a-row>
            <a-row class="content-item">
              <a-col :span="6">
                <span class="item-label">用户ID：</span>
                <span>{{ showEmptyText(orderDetail.userId) }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">用户手机号：</span>
                <span>{{ orderDetail.phoneNum || '--' }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">用户邮箱：</span>
                <span>{{ orderDetail.email || '--' }}</span>
              </a-col>
              <a-col :span="6">
                <span class="item-label">所属组织：</span>
                <span>{{ showEmptyText(orderDetail.organization) }}</span>
              </a-col>
            </a-row>
            <div class="content-item question-des">
              <span class="item-label">需求描述：</span>
              <span class="des-content">{{ showEmptyText(orderDetail.description) }}</span>
            </div>
            <div class="content-item appendix-des">
              <span class="item-label">附件：</span>
              <a-image-preview-group v-if="orderDetail.files && orderDetail.files.length > 0">
                <a-space :size="30">
                  <a-image class="item-appendix-img" v-for="url in orderDetail.files" :key="url" :width="160" :height="120" :src="url"> </a-image>
                </a-space>
              </a-image-preview-group>
              <span v-else>--</span>
            </div>
          </div>
          <div v-else class="workorder-items-content">
            <a-row class="content-item">
              <a-col :span="8">
                <span class="item-label">工单号：</span>
                <span>{{ orderDetail.ticketNum }}</span>
              </a-col>
              <a-col :span="8">
                <span class="item-label">创建时间：</span>
                <span>{{ orderDetail.createTime }}</span>
              </a-col>
              <a-col :span="8">
                <span class="item-label">状态：</span>
                <a-tag v-if="orderDetail.status !== undefined" size="small" :color="typeObject.color">{{ typeObject.txt }}</a-tag>
              </a-col>
            </a-row>
            <a-row class="content-item">
              <a-col :span="8">
                <span class="item-label">问题分类：</span>
                <span>{{ orderDetail.category }}</span>
              </a-col>
              <a-col :span="8">
                <span class="item-label">用户名：</span>
                <span>{{ orderDetail.userName }}</span>
              </a-col>
              <a-col :span="8">
                <span class="item-label">用户ID：</span>
                <span>{{ orderDetail.userId || '--' }}</span>
              </a-col>
            </a-row>
            <a-row class="content-item">
              <a-col :span="8">
                <span class="item-label">用户手机号：</span>
                <span>{{ orderDetail.phoneNum || '--' }}</span>
              </a-col>
              <a-col :span="8">
                <span class="item-label">邮箱：</span>
                <span>{{ orderDetail.email || '--' }}</span>
              </a-col>
            </a-row>
            <div class="content-item question-des">
              <span class="item-label">问题描述：</span>
              <span class="des-content">{{ orderDetail.description || '--' }}</span>
            </div>
            <div class="content-item appendix-des">
              <span class="item-label">附件：</span>
              <a-image-preview-group v-if="orderDetail.files && orderDetail.files.length > 0">
                <a-space :size="30">
                  <a-image class="item-appendix-img" v-for="url in orderDetail.files" :key="url" :width="160" :height="120" :src="url"> </a-image>
                </a-space>
              </a-image-preview-group>
              <span v-else>--</span>
            </div>
          </div>
        </div>
      </container-item>
      <container-item>
        <div class="handle-records-title">
          <h3>处理记录</h3>
        </div>
        <div class="handle-records-table">
          <a-table :columns="columns" :data-source="orderDetail.record">
            <template #resultSlot="{ record }">
              <a v-if="record.handleResult" @click="() => handleRecordDetail(record)" :title="record.handleResult" class="handle-records-content">{{ record.handleResult }}</a>
              <span v-else>--</span>
            </template>
          </a-table>
        </div>
      </container-item>
    </Container>
    <!-- 新增处理结果modal -->
    <a-modal :visible="addModalVisible" destroyOnClose title="新增处理结果" :confirmLoading="confirmLoading" @cancel="handleAddCancel" @ok="handleAddConfirm">
      <a-form :model="formState" ref="formRef" :colon="false" v-bind="layout">
        <a-form-item
          label="处理结果"
          name="handleResult"
          :rules="[
            {
              required: true,
              message: '请输入处理结果',
              whitespace: true,
            },
          ]"
        >
          <a-textarea v-model:value="formState.handleResult" placeholder="请输入" :maxlength="400" showCount :autoSize="{ minRows: 6, maxRows: 8 }"></a-textarea>
        </a-form-item>
        <a-form-item label=" ">
          <div style="display: flex">
            <a-upload :file-list="formState.uploadList" :disabled="formState.uploadList.length >= 5" :headers="{ Authorization: 'Bearer ' + refreshToken }" accept=".png,.jpg,.jpeg" action="./object/web/storage/image/upload" @change="uploadChange">
              <a-button type="outline" :disabled="formState.uploadList.length >= 5">
                <upload-outlined></upload-outlined>
                上传附件
              </a-button>
            </a-upload>
            <a-tooltip class="upload-tooltip">
              <template #title>最多上传5个文件，每个文件不超过10M,支持png,jpg,jpeg,PNG,JPG,JPEG格式</template>
              <jt-icon type="iconinfo-circle"></jt-icon>
            </a-tooltip>
          </div>
        </a-form-item>
        <a-form-item label="通知用户">
          <a-checkbox-group v-model:value="formState.notification">
            <a-checkbox :disabled="!orderDetail.email || !FEATURE_SMS" value="emailNotice" name="emailNotice">发送邮件</a-checkbox>
            <a-checkbox :disabled="!orderDetail.phoneNum || !FEATURE_SMS" value="phoneNotice" name="phoneNotice">发送短信</a-checkbox>
            <a-checkbox :disabled="!showMessageCenter" value="messageNotice" name="messageNotice">发送站内信</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </a-form>
    </a-modal>
    <!-- 查看处理结果详情modal -->
    <a-modal :visible="detailModalVisible" title="查看处理结果" @cancel="handleCloseDetailModal" :footer="null">
      <a-row style="margin-bottom: 20px">
        <a-col class="result-detail-label" :span="6">处理结果：</a-col>
        <a-col :span="18">{{ currentRecord.handleResult }}</a-col>
      </a-row>
      <a-row style="margin-bottom: 20px">
        <a-col class="result-detail-label" :span="6">附件：</a-col>
        <a-col class="result-image-content" v-if="currentRecord.files && currentRecord.files.length > 0" :span="18">
          <a-image-preview-group>
            <a-space :size="10">
              <a-image class="modal-preview-img" v-for="url in currentRecord.files" :key="url" :width="60" :src="url"> </a-image>
            </a-space>
          </a-image-preview-group>
        </a-col>
        <a-col v-else :span="18">--</a-col>
      </a-row>
      <a-row style="margin-bottom: 20px">
        <a-col class="result-detail-label" :span="6">短信通知：</a-col>
        <a-col :span="18" v-if="orderDetail.phoneNum"
          ><a-space>
            <span>{{ currentRecord.phoneNotice || '--' }}</span>
            <a-button :disabled="!FEATURE_SMS" class="send-button" type="link" ghost @click="resendNotice(currentRecord, 0)">{{ currentRecord.phoneNotice === '--' || !currentRecord.phoneNotice ? '立即发送' : '再次发送' }}</a-button>
          </a-space></a-col
        >
        <a-col v-else>--</a-col>
      </a-row>
      <a-row style="margin-bottom: 20px">
        <a-col class="result-detail-label" :span="6">邮件通知：</a-col>
        <a-col :span="18" v-if="orderDetail.email">
          <a-space>
            <span>{{ currentRecord.emailNotice || '--' }}</span>
            <a-button :disabled="!FEATURE_SMS" class="send-button" type="link" ghost @click="resendNotice(currentRecord, 1)">{{ currentRecord.emailNotice === '--' || !currentRecord.emailNotice ? '立即发送' : '再次发送' }}</a-button>
          </a-space>
        </a-col>
        <a-col v-else>--</a-col>
      </a-row>
      <a-row style="margin-bottom: 20px">
        <a-col class="result-detail-label" :span="6">站内信通知：</a-col>
        <a-col :span="18">
          <a-space>
            <span>{{ currentRecord.messageNotice || '--' }}</span>
            <a-button v-if="showMessageCenter" class="send-button" type="link" ghost @click="resendNotice(currentRecord, 2)">{{ currentRecord.messageNotice === '--' || !currentRecord.messageNotice ? '立即发送' : '再次发送' }}</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, reactive, ref, UnwrapRef, createVNode, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import { debounce } from 'lodash';
import { getEnvConfig } from '@/config';
import { keycloak } from '@/keycloak';
import request from '@/request';

interface FormState {
  handleResult: string | undefined;
  uploadList: string[];
  notification: string[];
}

interface order {
  lastTicketNum: string;
  nextTicketNum: string;
  ticketNum: string;
  category: string;
  createTime: string;
  status: number | undefined;
  userName: string;
  phoneNum: string | undefined;
  email: string | undefined;
  description: string;
  files: string[];
  userId: string;
  record: Record<string, string | number>[];
}

// 工单状态
const enum ORDER_STATUS {
  PENDING,
  PROCESSING,
  FINISHED,
}

const enum BTN_TYPES {
  add = '新增处理结果',
  resolve = '解决',
}

export default defineComponent({
  components: {
    UploadOutlined,
    subHeader,
    Container,
    ContainerItem,
  },
  setup() {
    const imageType = ['image/png', 'image/jpg', 'image/jpeg'];
    const route = useRoute();
    const router = useRouter();
    const store = useStore();
    const activeKey = ref<string>((route.query.activeKey as string) || '1');
    const breadCrumbs = [{ name: '工单管理', path: '/workorder-manage', query: route.query }, { name: '工单详情' }];
    const handleLoading = ref<boolean>(false);
    const refreshToken = computed(() => store.state.refreshToken); // keyclock刷新的token
    // 详情数据
    const orderDetail = ref<order>({
      lastTicketNum: '-1',
      nextTicketNum: '-1',
      ticketNum: '',
      category: '',
      createTime: '',
      status: undefined,
      userName: '',
      phoneNum: undefined,
      email: undefined,
      description: '',
      userId: '',
      files: [],
      record: [],
    });

    const confirmLoading = ref<boolean>(false);

    // 处理结果列表
    const columns = ref([
      {
        dataIndex: 'handleTime',
        key: 'handleTime',
        title: '处理时间',
      },
      {
        dataIndex: 'adminName',
        key: 'adminName',
        title: '处理人',
      },
      {
        dataIndex: 'handleContent',
        key: 'handleContent',
        title: '处理内容',
      },
      {
        dataIndex: 'handleResult',
        key: 'handleResult',
        title: '处理结果',
        slots: {
          customRender: 'resultSlot',
        },
        width: '30%',
        ellipsis: true,
      },
      {
        dataIndex: 'phoneNotice',
        key: 'phoneNotice',
        title: '短信通知',
        customRender({ text }) {
          return text || '--';
        },
      },
      {
        dataIndex: 'emailNotice',
        key: 'emailNotice',
        title: '邮件通知',
        customRender({ text }) {
          return text || '--';
        },
      },
      {
        dataIndex: 'messageNotice',
        key: 'messageNotice',
        title: '站内信通知',
        customRender({ text }) {
          return text || '--';
        },
      },
    ]);
    let currentRecord = ref({});
    let formRef = ref();
    // 显示单条处理记录详情
    const detailModalVisible = ref(false);

    const isDemandOrder = computed(() => {
      return activeKey.value !== '2';
    });

    const typeObject = computed(() => {
      switch (orderDetail.value.status) {
        case 0:
          return {
            color: 'red',
            txt: '待处理',
          };
        case 1:
          return {
            color: 'blue',
            txt: '处理中',
          };
        default:
          return {
            color: 'green',
            txt: '已关闭',
          };
      }
    });
    const getDetailById = async (ticketNum?) => {
      const requestData = {
        ticketNum: ticketNum ?? route.params.orderNumber,
        categoryId: route.query.currentCatageoryId,
        status: route.query.status,
        createTimeSort: route.query.orderColumn === 'createTime' ? (eval(route.query?.isAsc as string) ? 'asc' : 'desc') : '',
        updateTimeSort: route.query.orderColumn === 'updateTime' ? (eval(route.query?.isAsc as string) ? 'asc' : 'desc') : '',
        keyword: route.query.keyword,
      };
      const url = isDemandOrder.value ? '/ticket/web/demand/get' : '/ticket/web/get';
      const res = await request(url, { method: 'GET', data: requestData });
      if (res.state === 'OK') {
        orderDetail.value = res.body as any;
        if (currentRecord.value) {
          const records = orderDetail.value.record;
          for (let i = 0; i < records.length; i++) {
            if (records[i].recordId === (currentRecord.value as any).recordId) {
              currentRecord.value = records[i];
              return;
            }
          }
        }
      }
    };

    const handleRecordDetail = (record) => {
      detailModalVisible.value = true;
      currentRecord.value = record;
    };
    const handleCloseDetailModal = () => {
      detailModalVisible.value = false;
    };

    /* 新增处理结果 */
    const addHandleResult = () => {
      addModalVisible.value = true;
    };

    const addModalVisible = ref(false);
    const formState: UnwrapRef<FormState> = reactive({
      handleResult: undefined,
      uploadList: [],
      notification: [],
    });
    const layout = {
      labelCol: { span: 5 },
      wrapperCol: { span: 18 },
    };

    // 处理按钮在三个状态下的逻辑处理
    const handleOrder = async (status) => {
      if (status === ORDER_STATUS.PENDING || status === ORDER_STATUS.FINISHED) {
        handleLoading.value = true;
        const url = isDemandOrder.value ? '/ticket/web/demand/beginHandle' : '/ticket/web/beginHandle';
        const res = await request(url, { method: 'POST', data: { ticketNum: orderDetail.value.ticketNum } });
        if (res.state === 'OK') {
          message.success('处理状态已更新');
          orderDetail.value.status = 1; // 直接更新下状态，防止按钮出现中间状态重复点击
          getDetailById();
        }
        nextTick(() => {
          handleLoading.value = false;
        });
      } else if (status === ORDER_STATUS.PROCESSING) {
        confirmLoading.value = true;
        const reqData = {
          result: formState.handleResult,
          ticketNum: orderDetail.value.ticketNum,
          emailNotice: '',
          phoneNotice: '',
          messageNotice: '',
          files: formState.uploadList.map((file) => {
            return (file as any).response.body.url;
          }),
        };
        const handleUrl = isDemandOrder.value ? '/ticket/web/demand/handle' : '/ticket/web/handle';
        request(handleUrl, { method: 'POST', data: reqData }).then((res) => {
          if (res.state === 'OK') {
            message.success('处理状态已更新');
            getDetailById();
            handleAddCancel();
          } else {
            message.error(res.errorMessage || '新增处理状态失败');
          }
          confirmLoading.value = false;
        });
      }
    };

    const throttleHandleOrder = debounce(handleOrder, 500, { leading: true, trailing: false });
    // 关闭处置弹框
    const handleAddCancel = () => {
      addModalVisible.value = false;
      formState.handleResult = undefined;
      formState.uploadList = [];
      formState.notification = [];
    };

    // 提交处置内容
    const handleAddConfirm = () => {
      formRef.value.validate().then(() => {
        const emailNotice = formState.notification.includes('emailNotice');
        const phoneNotice = formState.notification.includes('phoneNotice');
        const messageNotice = formState.notification.includes('messageNotice');
        const reqData = {
          result: formState.handleResult,
          ticketNum: orderDetail.value.ticketNum,
          emailNotice,
          phoneNotice,
          messageNotice,
          files: formState.uploadList.map((file) => {
            return (file as any).response.body.url;
          }),
        };
        const requrl = isDemandOrder.value ? '/ticket/web/demand/record/add' : '/ticket/web/addRecord';
        // 如果有通知用户的发送则需要二次确认
        if (emailNotice || phoneNotice || messageNotice) {
          addModalVisible.value = false; // 将弹框暂时隐藏掉，两个弹框重叠样式不美观
          let types = (() => {
            const typesArr: string[] = [];
            if (phoneNotice) {
              typesArr.push('短信');
            }
            if (emailNotice) {
              typesArr.push('邮件');
            }
            if (messageNotice) {
              typesArr.push('站内信');
            }
            return typesArr.join('、');
          })();
          const title = `确定发送${types}吗？`;
          Modal.confirm({
            title: () => title,
            icon: () => createVNode(ExclamationCircleOutlined),
            content: () => createVNode('span', {}, `处理结果（不含附件） 将发送至用户${orderDetail.value.userName}`),
            cancelText: () => '取消',
            okText: () => '确定',
            okType: 'danger',
            onCancel() {
              addModalVisible.value = true;
            },
            onOk() {
              sendRecord(true, types);
            },
          });
        } else {
          sendRecord();
        }
        function sendRecord(SecondConfirm = false, types?) {
          confirmLoading.value = true;
          request(requrl, { method: 'POST', data: reqData })
            .then((res) => {
              if (res.state === 'OK') {
                message.success('处理结果已更新');
                if (SecondConfirm) {
                  setTimeout(() => {
                    const body: any = res.body;
                    if (body.status === 'fail') {
                      message.error(body.message);
                    } else {
                      message.success(body.message || `${types}发送成功`);
                    }
                  }, 500);
                }
                getDetailById();
                handleAddCancel();
              } else {
                if (SecondConfirm) {
                  addModalVisible.value = true;
                }
                message.error(res.errorMessage || '新增处理结果失败');
              }
            })
            .catch(() => {
              message.error('新增处理结果失败');
            })
            .finally(() => {
              confirmLoading.value = false;
            });
        }
      });
    };

    const resendNotice = async (record, types) => {
      const params = {
        title: '',
        url: '',
      };
      switch (types) {
        case 0:
          params.title = '短信';
          params.url = 'smsNotice';
          break;
        case 1:
          params.title = '邮件';
          params.url = 'emailNotice';
          break;
        case 2:
          params.title = '站内信';
          params.url = 'messageNotice';
          break;
      }
      Modal.confirm({
        title: () => `确定发送${params.title}吗？`,
        icon: () => createVNode(ExclamationCircleOutlined),
        content: () => createVNode('span', {}, `处理结果（不含附件） 将发送至用户${orderDetail.value.userName}`),
        cancelText: () => '取消',
        okText: () => '确定',
        okType: 'danger',
        onOk() {
          const url = isDemandOrder.value ? `/ticket/web/demand/${params.url}` : `/ticket/web/${params.url}`;
          request(url, {
            method: 'POST',
            data: {
              recordId: record.recordId,
              ticketNum: record.ticketNum,
            },
          }).then((res) => {
            if (res.state === 'OK') {
              getDetailById();
              message.success(`${params.title}发送成功`);
            } else {
              message.error(`${params.title}发送失败`);
            }
          });
        },
      });
    };

    const uploadChange = (info) => {
      let resFileList = [...info.fileList];
      resFileList = resFileList.slice(-5);

      const file = info.file;
      if (file.size / 1024 > 1024 * 10) {
        resFileList.splice(-1);
        message.error('上传文件大于10M');
        return false;
      }

      if (!imageType.includes(file.type)) {
        message.error('格式不支持，请上传png,jpg,jpeg格式文件');
        return false;
      }

      if (file.status === 'done' && file.response?.state === 'OK') {
        message.success('上传成功');
      } else if (file.status === 'error') {
        resFileList.splice(-1);
        message.error('上传失败');
      }
      formState.uploadList = resFileList;
    };

    onMounted(() => {
      getDetailById();
    });

    const FEATURE_SMS = getEnvConfig('FEATURE_SMS') === '1';

    const startBtnDisplay = computed(() => {
      if (orderDetail.value.status !== 0) {
        return orderDetail.value.record.find((x) => x.handleContent === '新增处理结果') && orderDetail.value.record.length > 0;
      }
      return orderDetail.value.status !== undefined;
    });
    const startBtnText = computed(() => {
      switch (orderDetail.value.status) {
        case 0:
          return '开始处理';
        case 1:
          return '解决';
        default:
          return '重新打开';
      }
    });

    const getOtherOrderDetail = (ticketNum) => {
      router.replace({ name: '工单详情', params: { orderNumber: ticketNum }, query: route.query });
      getDetailById(ticketNum);
    };

    const showEmptyText = (text) => {
      return text || '--';
    };

    return {
      refreshToken,
      formRef,
      orderDetail,
      breadCrumbs,
      columns,
      formState,
      addModalVisible,
      layout,
      currentRecord,
      detailModalVisible,
      confirmLoading,
      typeObject,
      handleLoading,
      addHandleResult,
      handleAddCancel,
      handleAddConfirm,
      handleOrder,
      throttleHandleOrder,
      handleRecordDetail,
      handleCloseDetailModal,
      uploadChange,
      resendNotice,
      FEATURE_SMS,
      showMessageCenter: getEnvConfig('FEATURE_MESSAGECENTER') == '1',
      keycloak,
      getDetailById,
      startBtnDisplay,
      startBtnText,
      getOtherOrderDetail,
      isDemandOrder,
      showEmptyText,
    };
  },
});
</script>

<style lang="less" scoped>
:deep(.ant-col) {
  display: flex;
  .item-label {
    flex-shrink: 0;
  }
  > span {
    max-width: calc(100% - 100px);
    word-wrap: break-word;
  }
}
.handle-records-title {
  h3 {
    margin-bottom: 0.5em;
  }
}
.workorder-items {
  line-height: 1.75;
  .workorder-items-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  .workorder-items-content {
    padding: 0px 40px 0px 20px;
    .content-item {
      margin-bottom: 20px;
      .item-label {
        color: #666;
        display: inline-block;
        width: 100px;
        text-align: right;
        margin-right: 8px;
        flex-shrink: 0;
      }
    }
    .question-des {
      display: flex;
      .des-content {
        width: calc(100% - 108px);
        word-break: normal;
        word-wrap: break-word;
        overflow: hidden;
      }
    }
    .appendix-des {
      display: flex;
      /deep/.ant-image-img.item-appendix-img {
        height: 100%;
        width: 100%;
      }
    }
  }
  margin-bottom: 20px;
}
.send-button {
  font-size: 12px;
  height: auto;
  line-height: 1;
  padding: 0;
}
.result-detail-label {
  text-align: right;
  padding-right: 8px;
}
.result-image-content {
  .modal-preview-img {
    width: 100%;
    height: 100%;
  }
}
.handle-records-content {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  word-break: break-all;
}
.upload-tooltip {
  margin-left: 15px;
  font-size: 18px;
  color: #c2c5cf;
  margin-top: 7px;
}
</style>
