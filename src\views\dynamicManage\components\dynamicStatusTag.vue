<template>
  <span :class="dynamicStatus == 1 ? 'succeed-status' : 'draft-status'">{{ DYNAMIC_STATUS_MSG[dynamicStatus] }}</span>
</template>

<script>
import { defineComponent } from 'vue';
import { DYNAMIC_STATUS_MSG } from '../constants';
export default defineComponent({
  props: {
    dynamicStatus: [String],
  },
  data() {
    return {
      DYNAMIC_STATUS_MSG,
    };
  },
});
</script>

<style lang="less" scoped>
.mail-status {
  display: inline-block;
  height: 20px;
  line-height: 17px;
  border-radius: 2px;
  font-size: 12px;
  padding: 0 12px;
  border: 1px solid;
  white-space: nowrap;
}
.succeed-status {
  background: #f0fff8;
  border-color: #68e3b4;
  color: #1dca94;
  .mail-status();
}
.draft-status {
  background: #fef6e7;
  border-color: #ffd666;
  color: #faad14;
  .mail-status();
}
</style>
