<template>
  <div class="message-control-container">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '站内信发布', query: $route.query }"> 站内信发布 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>站内信预览</a-breadcrumb-item>
    </a-breadcrumb>
    <Container>
      <container-item>
        <div class="preview-top">
          <p>{{ mailInfo.mailTitle }}</p>
          <p>{{ mailInfo.sendTime || '--' }}</p>
        </div>
        <a-divider />
        <div class="preview-bottom">
          <div v-html="mailInfo.mailContent" class="document-content markdown-body"></div>
        </div>
      </container-item>
    </Container>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import { MessageCenter } from '@/apis';
const { getDetail } = MessageCenter;

export default defineComponent({
  components: { Container, ContainerItem },
  data() {
    return {
      mailInfo: {} as any,
    };
  },
  created() {
    this.getMailInfo();
  },
  methods: {
    async getMailInfo(this: any) {
      const res: any = await getDetail({ data: { mailId: this.$route.params.mailId } });
      if (res.state === 'OK') {
        this.mailInfo = res.body;
      }
    },
  },
});
</script>
<style lang="less" scoped>
.nav {
  height: 58px;
  line-height: 58px;
  padding-left: 20px;
  background: #ffffff;
  font-weight: 500;
  color: #121f2c;
}
.preview-top {
  text-align: center;
  & > p:nth-of-type(1) {
    font-size: 18px;
    font-weight: 500;
    color: #121f2c;
    margin-bottom: 8px;
  }
  & > p:nth-of-type(2) {
    font-size: 12px;
    color: #606972;
    margin: 0;
  }
}
.preview-bottom {
  padding: 0 36px;
  word-wrap: break-word;
  .document-content {
    line-height: 1.5715;
  }
}
</style>
