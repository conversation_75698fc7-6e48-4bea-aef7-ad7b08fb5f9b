<template>
  <div class="user-control-container">
    <sub-header title="站内信发布"></sub-header>
    <Container>
      <container-item>
        <div class="top-bar">
          <div>
            <h1>站内信列表</h1>
          </div>
          <div style="display: flex">
            <a-input v-model:value="postData.keyword" @change="handlerSearch" allow-clear placeholder="站内信标题" style="margin-right: 12px">
              <template #prefix>
                <jt-icon type="iconsousuo" style="font-size: 18px" />
              </template>
            </a-input>
            <a-button @click="goCreate" type="primary">
              <template #icon><PlusOutlined /></template>
              新建站内信
            </a-button>
          </div>
        </div>
        <a-table :loading="tableAttr(isLoading).loading" :getPopupContainer="getPopupContainer" :columns="columns" :data-source="mailList" @change="tableChange" :customRow="goDetail" :rowClassName="() => 'cus-row'" :pagination="false" rowKey="rowKey">
          <template #emptyText>
            <empty
              v-if="!isLoading"
              title="站内信"
              :showNoDataText="
                showNoDataText({
                  mailType: postData.mailType,
                  mailStatus: postData.mailStatus,
                  keyword: postData.keyword,
                })
              "
            >
              <template #description>
                请立即
                <a href="javascript:;" @click="goCreate">新建站内信</a>
              </template>
            </empty>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'mailStatus'">
              <mail-status-tag :mailStatus="record.mailStatus" class="mail-status-tag" />
            </template>
            <template v-if="column.key === 'mailTitle'">
              <span class="user-name-text" :title="record.mailTitle">{{ record.mailTitle }}</span>
              <br />
              <span>{{ labelString(record.mailContent) || '--' }}</span>
            </template>
            <template v-if="column.key === 'isGlobalList'">
              <span>{{ SENDER_TYPE_MSG[record.isGlobal || SENDER_TYPE.GLOBAL] }}</span>
            </template>
            <template v-if="column.key === 'creatTime'">
              <div class="creat-time"><img src="@/assets/images/messageControl/create_time.png" alt="" />{{ record.creatTime || '--' }}</div>
              <div class="creat-time"><img src="@/assets/images/messageControl/send_time.png" alt="" />{{ record.sendTime || '--' }}</div>
            </template>
            <template v-if="column.key === 'mailType'">
              <span>{{ mailTypeMaps[record.mailType] }}</span>
            </template>
            <template v-if="column.key === 'creator'">
              <a-tooltip>
                <template #title v-if="record.creator">
                  <span>{{ record.creator }}</span>
                </template>
                <span>{{ record.creator || '--' }}</span>
              </a-tooltip>
            </template>
          </template>
        </a-table>
        <Pagination :total="total" :pageNum="postData.pageNum" :pageSize="postData.pageSize" :pageSizeOptions="[10, 20, 50, 100]" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
      </container-item>
    </Container>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import mailStatusTag from './components/mailStatusTag.vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import { SENDER_TYPE, SENDER_TYPE_MSG, MAIL_STATUS_MSG } from '@/constants';
import _ from 'lodash';
import { tableAttr, showNoDataText } from '@/utils';
import empty from '@/components/empty.vue';
import { MessageCenter } from '@/apis';

const { getList, getMailType } = MessageCenter;
let mailStatusFilters = [] as any;
for (const key in MAIL_STATUS_MSG) {
  mailStatusFilters.push({
    text: MAIL_STATUS_MSG[key],
    value: key,
  });
}
export default defineComponent({
  components: {
    PlusOutlined,
    subHeader,
    Container,
    ContainerItem,
    Pagination,
    mailStatusTag,
    empty,
  },
  data() {
    const query = this.$route.query;
    const defaultMailStatus = query.mailStatus ? String(query.mailStatus).split(',') : [];
    const defaultMailType = query.mailType ? String(query.mailType).split(',') : [];
    // 发送对象
    const defaultMailSender = query.isGlobalList ? String(query.isGlobalList).split(',') : [];

    return {
      isLoading: true,
      // MAIL_STATUS_MSG,
      mailTypeMaps: [],
      SENDER_TYPE,
      SENDER_TYPE_MSG,
      columns: [
        {
          title: '状态',
          dataIndex: 'mailStatus',
          key: 'mailStatus',
          width: 110,
          scopedSlots: { customRender: 'mailStatus' },
          filters: mailStatusFilters,
          defaultFilteredValue: defaultMailStatus,
        },
        {
          title: '站内信标题',
          key: 'mailTitle',
          width: 460,
          ellipsis: true,
          scopedSlots: { customRender: 'mailTitle' },
        },
        {
          title: '发送对象',
          key: 'isGlobalList',
          width: 200,
          dataIndex: 'isGlobal',
          scopedSlots: { customRender: 'isGlobal' },
          filters: [],
          defaultFilteredValue: defaultMailSender,
        },
        {
          title: '时间',
          key: 'creatTime',
          width: 165,
          scopedSlots: { customRender: 'creatTime' },
        },
        {
          title: '类型',
          key: 'mailType',
          width: 80,
          dataIndex: 'mailType',
          scopedSlots: { customRender: 'mailType' },
          filters: [],
          defaultFilteredValue: defaultMailType,
        },
        {
          title: '创建人',
          key: 'creator',
          dataIndex: 'creator',
          width: 140,
          ellipsis: true,
          scopedSlots: { customRender: 'creator' },
        },
      ],
      mailList: [],
      total: 0,
      postData: {
        mailType: defaultMailType,
        mailStatus: defaultMailStatus,
        isGlobalList: defaultMailSender,
        keyword: query.keyword || undefined,
        pageNum: Number(query.pageNum) || 1,
        pageSize: Number(query.pageSize) || 10,
      },
    };
  },
  created() {
    this.getMailType();
    // 发送对象支持筛选
    this.getMailSender();
  },
  methods: {
    showNoDataText,
    tableAttr,
    getPopupContainer(node) {
      if (node && this.mailList.length > 1) {
        return node.parentNode;
      }
      return document.body;
    },
    rewriteUrlByParams() {
      const { mailStatus, mailType, keyword, isGlobalList } = this.postData;
      const rewriteQuery = {
        mailStatus: mailStatus && mailStatus.length > 0 ? mailStatus.join(',') : undefined,
        mailType: mailType && mailType.length > 0 ? mailType.join(',') : undefined,
        isGlobalList: isGlobalList && isGlobalList.length > 0 ? isGlobalList.join(',') : undefined,
        keyword: keyword || undefined,
      };
      this.$router.replace({ query: { ...this.$route.query, ...this.postData, ...rewriteQuery } });
    },
    async getMailList(needRewriteUrl = true) {
      this.isLoading = true;
      needRewriteUrl && this.rewriteUrlByParams();
      const res: any = await getList({ type: 'POST', data: this.postData });
      if (res.state === 'OK') {
        this.mailList = res.body.list;
        this.isLoading = false;
        this.total = res.body.total;
      } else {
        this.isLoading = false;
      }
    },
    getMailSender() {
      const mailSenderFilters = [
        {
          text: SENDER_TYPE_MSG[SENDER_TYPE.GLOBAL],
          value: SENDER_TYPE.GLOBAL,
        },
        {
          text: SENDER_TYPE_MSG[SENDER_TYPE.ASSIGN],
          value: SENDER_TYPE.ASSIGN,
        },
      ];
      this.columns.forEach((item) => {
        if (item.dataIndex === 'isGlobal') {
          item.filters = mailSenderFilters;
        }
      });
    },
    async getMailType(this: any) {
      const res: any = await getMailType({});
      if (res.state === 'OK') {
        for (let i = 0; i < res.body.length; i++) {
          this.mailTypeMaps[res.body[i].mailTypeId] = res.body[i].mailTypeName;
        }
        let mailTypeFilters = res.body.map((item) => {
          return {
            text: item.mailTypeName,
            value: item.mailTypeId,
          };
        });
        this.columns.forEach((item) => {
          if (item.dataIndex === 'mailType') {
            item.filters = mailTypeFilters;
          }
        });
        this.getMailList(false);
      }
    },
    labelString(str) {
      str = str.replace(/<[^>]*>(([^<])*)/gi, function () {
        return arguments[1];
      });
      str = str.substring(0, str.length);
      return str;
    },
    handlerSearch(this: any, e) {
      this.isLoading = true;
      this.searchInfo(this);
    },
    searchInfo: _.debounce(function (this: any) {
      this.postData.pageNum = 1;
      this.postData.pageSize = 10;
      this.getMailList();
    }, 500),
    tableChange(pagination, filters, sorter, extra) {
      for (const key in filters) {
        this.postData[key] = filters[key];
      }
      this.postData.pageNum = 1;
      this.postData.pageSize = 10;
      this.getMailList();
    },
    changePageNum(pageNum) {
      this.postData.pageNum = pageNum;
      this.getMailList();
    },
    changePageSize(pageSize) {
      this.postData.pageSize = pageSize;
      this.postData.pageNum = 1;
      this.getMailList();
    },
    goCreate() {
      this.$router.push({
        path: '/message-management/messageControl-create',
        query: this.$route.query,
      });
    },
    goDetail(record) {
      return {
        onClick: () => {
          this.$router.push({
            path: '/message-management/messageControl-detail',
            query: {
              ...this.$route.query,
              mailId: record.mailId,
            },
          });
        },
      };
    },
  },
});
</script>
<style lang="less" scoped>
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
:deep(.cus-row) {
  cursor: pointer;
  font-size: 12px;
  color: #606972;
  .user-name-text {
    color: #121f2c;
    &:hover {
      color: @primary-color;
    }
  }
}
:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;
  th {
    background-color: #f6f9fc;
  }
}
/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}
/deep/.ant-table-filter-column {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    // width: 24px;
    flex: 0;
  }
}
/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}
.creat-time {
  display: flex;
  align-items: center;
  img {
    margin-right: 4px;
  }
}
.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}
.table-empty {
  color: #606972;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  top: -120px;
}
</style>
