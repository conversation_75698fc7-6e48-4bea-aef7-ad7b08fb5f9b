/* 浏览器默认样式覆盖 */
*{
    box-sizing: border-box;
}
// html {
//   overflow-y: hidden;
// }
::-webkit-scrollbar {
    width : 6px;
    height: 6px;
}

::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius   : 6px;
}

::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.4);
}

::-webkit-scrollbar-track {
    box-shadow   : inset 0 0 6px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
}

p {
    margin: 0;
}

// iconfont图标支持颜色修改
svg {
    fill: currentColor;
  }
  
  path {
    fill: unset;
  } 