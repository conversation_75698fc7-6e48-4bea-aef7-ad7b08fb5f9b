<template>
  <div class="user-control-container">
    <sub-header title="角色管理"></sub-header>
    <Container>
      <container-item>
        <div class="top-bar">
          <a-tabs v-model:activeKey="activeTab" @change="tabChange">
            <a-tab-pane v-for="item in tabs" :key="item" :tab="item" force-render></a-tab-pane>
          </a-tabs>
          <a-space>
            <a-input v-model:value="pageData.keyword" @change="search" placeholder="角色/角色描述" style="margin-right: 20px">
              <template #prefix>
                <jt-icon type="iconsousuo" style="font-size: 18px" />
              </template>
            </a-input>
            <a-button v-if="showCreate" @click="goCreate" type="primary">
              <template #icon><PlusOutlined /></template>
              新建角色
            </a-button>
          </a-space>
        </div>
        <a-table :loading="tableAttr(isLoading).loading" :columns="columnsComputed" :data-source="currentTableData" :customRow="goDetail" @change="tableChange" :pagination="false" rowKey="rowKey">
          <template #emptyText>
            <empty
              v-if="!isLoading"
              title="数据"
              :showNoDataText="
                showNoDataText({
                  keyword: pageData.keyword,
                })
              "
            ></empty>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a-tooltip>
                <template #title>
                  <span>{{ record.name }}</span>
                </template>
                <span class="table-name overflow-ellipsis">{{ record.name }}</span>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'desc'">
              <a-tooltip>
                <template #title v-if="record.desc">
                  <span>{{ record.desc }}</span>
                </template>
                <span class="overflow-ellipsis">{{ record.desc || '-' }}</span>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'operation'">
              <a-button :disabled="deleteBtnDisabled(record)" @click.stop="removeRole(record)" class="table-delete" type="link">
                <template #icon><jt-icon type="icondelete_trash" /></template>
                删除
              </a-button>
            </template>
          </template>
        </a-table>
        <Pagination :total="currentTableTotal" :pageNum="pageData.pageNum" :pageSize="pageData.pageSize" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
      </container-item>
    </Container>
    <a-modal class="role-modal" style="max-width: 360px; max-height: 180px" :visible="visible" :closable="false" :footer="null" width="400px">
      <ExclamationCircleFilled style="font-size: 18px; color: red; margin-right: 6px" />
      <span>确定删除角色 “{{ currentItem.name }}” 吗？</span>
      <p>删除后不可恢复,请谨慎操作</p>
      <div>
        <a-button @click="visible = false">取消</a-button>
        <a-button @click="goDelete" type="primary" danger style="margin: 0 8px 0 10px">删除</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined, ExclamationCircleFilled } from '@ant-design/icons-vue';
import _ from 'lodash';

import { tableAttr, showNoDataText } from '@/utils';
import { Role } from '@/apis';

import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import empty from '@/components/empty.vue';
import { ROLE_TYPE } from '@/constants/role';
import { checkKeycloakAttr } from '@/utils/auth';

export default defineComponent({
  components: {
    PlusOutlined,
    ExclamationCircleFilled,
    subHeader,
    Container,
    ContainerItem,
    Pagination,
    empty,
  },
  data() {
    return {
      isLoading: true,
      listTab: [],
      activeTab: '',
      columns: [] as any,
      visible: false,
      currentItem: {} as any,
      tableList: [],
      total: 0,
      sortedInfo: {} as any,
      pageData: {
        keyword: '',
        pageNum: 1,
        pageSize: 10,
      },
    };
  },
  created() {
    this.initDataByRouteQuery();
    this.getTabList();
  },
  computed: {
    showCreate() {
      return checkKeycloakAttr('jtc-role-manage');
    },
    tabs() {
      return this.listTab.map((x: { code: string; name: string }) => x.name);
    },
    columnsComputed() {
      return [
        {
          title: '角色',
          dataIndex: 'name',
          key: 'name',
          scopedSlots: { customRender: 'name' },
          width: '10%',
          ellipsis: true,
        },
        {
          title: '角色描述',
          dataIndex: 'desc',
          key: 'desc',
          scopedSlots: { customRender: 'desc' },
          width: '30%',
          ellipsis: true,
        },
        {
          title: '权限数',
          key: 'permissionCount',
          dataIndex: 'permissionCount',
          sortOrder: this.sortedInfo.columnKey === 'permissionCount' && this.sortedInfo.order,
          sorter: true,
          // sorter: {
          //   compare: (a, b) => a.permissionCount - b.permissionCount,
          //   // multiple: 3, //排序优先级，数越大优先级越高
          // },
          width: '10%',
        },
        {
          title: '用户数',
          key: 'userCount',
          dataIndex: 'userCount',
          sortOrder: this.sortedInfo.columnKey === 'userCount' && this.sortedInfo.order,
          sorter: true,
          // sorter: {
          //   compare: (a, b) => a.userCount - b.userCount,
          //   // multiple: 2,
          // },
          width: '10%',
        },
        {
          title: '创建时间',
          key: 'createTime',
          dataIndex: 'createTime',
          sortOrder: this.sortedInfo.columnKey === 'createTime' && this.sortedInfo.order,
          sorter: true,
          // sorter: {
          //   compare: (a, b) => new Date(a.createTime).getTime() - new Date(b.createTime).getTime(),
          //   // multiple: 1,
          // },
          width: '20%',
        },
        {
          title: '操作',
          key: 'operation',
          scopedSlots: { customRender: 'operation' },
          width: '10%',
        },
      ];
    },
    currentTabInfo(): { code: string; name: string } | undefined {
      return this.listTab.find((x: { code: string; name: string }) => x.name === this.activeTab);
    },
    currentTableData() {
      return this.tableList
        .filter((x: any) => x.name.includes(this.pageData.keyword) || x.desc.includes(this.pageData.keyword))
        .sort((a: any, b: any) => {
          if (this.sortedInfo.columnKey === 'userCount' || this.sortedInfo.columnKey === 'permissionCount') {
            if (this.sortedInfo.order === 'ascend') {
              return a[this.sortedInfo.columnKey] - b[this.sortedInfo.columnKey];
            }
            return b[this.sortedInfo.columnKey] - a[this.sortedInfo.columnKey];
          }
          if (this.sortedInfo.columnKey === 'createTime') {
            if (this.sortedInfo.order === 'ascend') {
              return new Date(a.createTime).getTime() - new Date(b.createTime).getTime();
            }
            return new Date(b.createTime).getTime() - new Date(a.createTime).getTime();
          }
          return a - b;
        })
        .slice(this.pageData.pageSize * (this.pageData.pageNum - 1), this.pageData.pageSize * this.pageData.pageNum);
    },
    currentTableTotal() {
      return this.tableList.filter((x: any) => x.name.includes(this.pageData.keyword) || x.desc.includes(this.pageData.keyword)).length;
    },
  },
  methods: {
    tableAttr,
    showNoDataText,
    rewriteUrlParamsMixin(params) {
      this.$router.replace({ query: { ...this.$route.query, ...params } });
    },
    search(this: any) {
      this.changePageNum(1);
    },
    initDataByRouteQuery() {
      const { pageNum, pageSize, keyword, sorterKey, order } = this.$route.query;
      this.pageData.pageNum = pageNum ? +(pageNum as string) : 1;
      this.pageData.pageSize = pageSize ? +(pageSize as string) : 10;
      this.pageData.keyword = keyword ? (keyword as string) : '';
      this.sortedInfo.columnKey = sorterKey ? sorterKey : null;
      this.sortedInfo.order = order ? order : null;
    },
    async getTabList(this: any) {
      const res = await Role.getPlatformList();
      if (res.code === 0) {
        if (!res.data.length) {
          this.$router.push({
            path: '/no-auth',
          });
        }
        this.listTab = res.data;
        this.activeTab = this.$route.query.platform || this.tabs[0];
        this.getRoleList();
      }
    },
    tabChange(e) {
      this.activeTab = e;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 10;
      this.getRoleList();
      this.rewriteUrlParamsMixin({ platform: this.activeTab });
    },
    async getRoleList(this: any) {
      this.isLoading = true;
      const data = {
        platformCode: this.currentTabInfo.code,
      };
      const res = await Role.getPlatformRoleList({ data });
      this.isLoading = false;
      this.tableList = res.data;
      this.total = res.data.length;
    },
    tableChange(pagination, filters, sorter, extra) {
      this.sortedInfo = sorter;
      this.rewriteUrlParamsMixin({ sorterKey: sorter.columnKey, order: sorter.order });
    },
    changePageNum(pageNum) {
      this.pageData.pageNum = pageNum;
      this.rewriteUrlParamsMixin({ pageNum, pageSize: this.pageData.pageSize, keyword: this.pageData.keyword });
    },
    changePageSize(pageSize) {
      this.pageData.pageSize = pageSize;
      this.changePageNum(1);
    },
    goCreate(this: any) {
      this.$router.push({
        path: '/role-management/roleControl-create',
        query: { platform: this.activeTab, platformCode: this.currentTabInfo.code },
      });
    },
    goDetail(record) {
      return {
        onClick: () => {
          this.$router.push({
            path: '/role-management/roleControl-detail',
            query: {
              ...this.$route.query,
              id: record.id,
              platform: this.currentTabInfo?.name,
              platformCode: this.currentTabInfo?.code,
            },
          });
        },
      };
    },
    removeRole(record) {
      this.currentItem = record;
      this.visible = true;
    },
    async goDelete() {
      this.visible = false;
      const data = {
        platformCode: this.currentTabInfo?.code,
        roleId: this.currentItem?.id,
      };
      const res = await Role.delete({ data });
      if (res.code === 0) {
        message.success('删除角色成功');
        this.getRoleList();
      } else {
        message.error('删除角色失败，请稍后重试');
      }
    },
    deleteBtnDisabled(record) {
      if (record.type === ROLE_TYPE.COMMON_USER || record.type === ROLE_TYPE.ADMIN) {
        return true;
      }
      if (record.userCount !== 0) {
        return true;
      }
      return false;
    },
  },
});
</script>
<style lang="less" scoped>
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /deep/.ant-tabs-nav {
    margin: 0;
  }
  /deep/.ant-tabs-nav::before {
    border: none;
  }
  /deep/.ant-tabs-tab {
    width: 136px;
    height: 32px;
    display: flex;
    justify-content: center;
    margin: 0;
    font-size: 14px;
    color: #aaacb4;
    line-height: 20px;
    border-bottom: 1px solid #e0e1e1;
  }
  /deep/.ant-tabs-tab-active {
    font-weight: 500;
    color: #0082ff;
  }
}
/deep/.ant-table {
  font-size: 12px;
  th {
    background: #edf1f3 !important;
    color: #121f2c;
    // padding: 10px;
  }
  td {
    color: #606972;
    // padding: 10px;
  }
}
.table-name:hover {
  cursor: pointer;
  color: #0082ff;
}
.table-delete {
  position: absolute;
  top: 0;
  left: 0;
  height: 52px;
}
.table-empty {
  color: #606972;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  top: -120px;
}
.ant-modal {
  background: red !important;
  max-width: 360px;
  max-height: 180px;
}
.role-modal {
  span {
    font-weight: 500;
    color: #121f2c;
  }
  p {
    font-size: 12px;
    color: #606972;
    margin: 20px 0 0 25px;
  }
  div {
    margin-top: 30px;
    text-align: right;
  }
}
</style>
