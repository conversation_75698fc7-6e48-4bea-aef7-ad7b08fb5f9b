{
	"compilerOptions": {
		"target": "esnext",
		"module": "esnext",
		"strict": true,
		"jsx": "preserve",
		"importHelpers": true,
		"moduleResolution": "node",
		"experimentalDecorators": true,
		"skipLibCheck": true,
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,
		"sourceMap": true,
		"baseUrl": ".",
		"allowJs": true,
		"types": [
			"webpack-env",
			"node"
		],
		"paths": {
			"@/*": [
				"src/*"
			]
		},
		"lib": [
			"esnext",
			"dom",
			"dom.iterable",
			"scripthost"
		],
		"noImplicitAny": false
	},
	// "include": [
	// 	"src/**/*.ts",
	// 	"src/**/*.tsx",
	// 	"src/**/*.vue",
	// 	"tests/**/*.ts",
	// 	"tests/**/*.tsx",
	// 	"src/image.d.ts"
	// ],
	"exclude": [
		"node_modules",
		"src"
	]
}
