import defaultConfig from './default.config';
import { proxyPrefix, proxyTargetHost } from './proxy.config';

interface EnvConfig {
  KEYCLOAK_URL: string; // keycloak登录地址
  USE_USER_IDENTITY: string; // 1 使用用户身份信息 0 不使用
  USER_LOGIN_OPTION: string; // "password,smscode"表示支持密码登录和短信验证码登录
  KEYCLOAK_LOGOUT_PREFIX: string; // keycloak 登出 cas前缀
  KEYCLOAK_CLIENT_ID: string; // keycloak cientId
  COMMON_MANAGEMENT_USER_MANAGEMENT_AVAILABLE_OPERATIONS: string; // 人工智能云管平台-用户管理可用的操作, create创建，edit编辑，batch_import批量导入，batch_outport批量导出
  MESSAGE_URL_PATH: string; // 消息中心的url后缀
  CAPABILITY_INNER_VERSION: string; // 开放能力是否为对内
  FEATURE_MESSAGECENTER: string; // 1 显示消息中心 0 不显示消息中心
  PUBLIC_NETWORK: string; // 是否为公网或者互联网
  FEATURE_SMS: string; // 工单详情处，是否支持短信和邮件 1支持 0不支持
  SHOW_DEMAND_ICON: string; // 是否显示需求反馈
}

export function getDefaultConfig(): EnvConfig {
  const config = { ...defaultConfig };
  config.KEYCLOAK_URL = `${proxyTargetHost}${config.KEYCLOAK_URL}`;
  return config;
}

export function getEnvConfig(key: keyof EnvConfig): EnvConfig[keyof EnvConfig] {
  const config = getEnvConfigFromBody(key) || getDefaultConfig()[key];
  // console.log(`getEnvConfig: ${key} = ${config}`);
  return config;
}

function getEnvConfigFromBody(key: string): string {
  const target = document.querySelector('body');
  const value = target?.getAttribute(key.replace(/_/g, ''));
  return value || '';
}

export function getProxyPrefix(): string {
  return proxyPrefix;
}
