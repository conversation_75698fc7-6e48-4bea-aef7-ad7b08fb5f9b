<template>
  <div class="user-control-container">
    <sub-header title="页面审核"></sub-header>
    <Container>
      <container-item>
        <div class="top-bar">
          <a-tabs v-model:activeKey="activeTab" @change="tabChange">
            <a-tab-pane v-for="item in listTab" :key="item" :tab="item" force-render></a-tab-pane>
          </a-tabs>
          <div style="display: flex">
            <a-input placeholder="页面标题/提交人" @change="handlerSearch" v-model:value="keyword">
              <template #prefix>
                <jt-icon type="iconsousuo" style="font-size: 18px" />
              </template>
            </a-input>
          </div>
        </div>
        <a-table :loading="tableAttr(loading).loading" :columns="columns" :data-source="tableList" @change="tableChange" rowKey="rowKey" :pagination="false">
          <template #emptyText>
            <empty
              v-if="!loading"
              title="页面"
              :showNoDataText="
                showNoDataText({
                  accessPlatform: accessPlatform,
                  keyword: keyword,
                })
              "
            >
            </empty>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <span @click="checkPage(record)" style="cursor: pointer">{{ record.name }}</span>
            </template>
            <template v-if="column.key === 'parents'">
              <a-tooltip v-if="record.parentCatalog[0]">
                <template #title v-if="record.parentCatalog[0].indexOf('...') != -1">
                  <span>{{ record.parents[0] }}</span>
                </template>
                <span>{{ record.parentCatalog[0] }}</span>
              </a-tooltip>
              <span v-if="record.parents[1]" style="color: #0082ff"> - </span>
              <a-tooltip v-if="record.parentCatalog[1]">
                <template #title v-if="record.parentCatalog[1].indexOf('...') != -1">
                  <span>{{ record.parents[1] }}</span>
                </template>
                <span>{{ record.parentCatalog[1] }}</span>
              </a-tooltip>
              <span v-if="record.parents.length === 0"> - </span>
            </template>
            <template v-if="column.key === 'platform'">
              <a-tag color="blue">{{ record.platform }}</a-tag>
            </template>
            <template v-if="column.key === 'operation'">
              <a-space size="middle" class="operation-button">
                <a-button @click="checkPage(record)" :disabled="record.dataType === 'catalog'" type="link">查看</a-button>
                <a-button @click="passPage(record)" type="link">通过</a-button>
                <a-button @click="rejectPage(record)" type="link" danger>驳回</a-button>
              </a-space>
            </template>
            <template v-if="column.key === 'approveStatus'">
              <a-space :size="3">
                <check-circle-outlined v-if="record.approveStatus == '2'" :style="{ color: '#0082FF' }" />
                <close-circle-outlined v-else :style="{ color: 'red' }" />
                <span :class="{ 'audit-reject': record.approveStatus == '1' }">{{ record.approveStatus == 2 ? '通过' : '驳回' }}</span>
              </a-space>
            </template>
          </template>
        </a-table>
        <Pagination :total="pagination.total" :pageNum="pagination.pageNum" :pageSize="pagination.pageSize" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
      </container-item>
    </Container>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { message } from 'ant-design-vue';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import { tableAttr, showNoDataText } from '@/utils';
import empty from '@/components/empty.vue';
import { HelpCenter } from '@/apis';
import _ from 'lodash';

const { selectAllUsed, saiList, saiOverruled, saiApprove } = HelpCenter;
export default defineComponent({
  components: {
    subHeader,
    Container,
    ContainerItem,
    Pagination,
    CheckCircleOutlined,
    CloseCircleOutlined,
    empty,
  },
  data() {
    return {
      listTab: ['待审批', '审批记录'],
      activeTab: '待审批',
      keyword: '',
      loading: true,
      columns: [],
      tableList: [],
      pagination: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      accessPlatform: '',
    };
  },
  created() {
    this.getColumns();
  },
  methods: {
    showNoDataText,
    tableAttr,
    tabChange(this: any, e) {
      this.activeTab = e;
      this.tableList = [];
      this.loading = true;
      this.keyword = '';
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 10;
      this.columns = this.columns.slice(0, 5);
      if (e === '审批记录') {
        this.columns.push({ title: '审核时间', key: 'approveTime', dataIndex: 'approveTime' }, { title: '审核意见', key: 'approveStatus', scopedSlots: { customRender: 'approveStatus' } });
      } else {
        this.columns.push({ title: '操作', key: 'operation', scopedSlots: { customRender: 'operation' } });
      }
      this.getTableList();
    },
    async getColumns(this: any) {
      const platform = await selectAllUsed({ name: 'HELP', data: { accessType: 'platform' } });
      const others = await selectAllUsed({ name: 'HELP', data: { accessType: 'other' } });
      const models = await selectAllUsed({ name: 'HELP', data: { accessType: 'bg_model' } });
      if (platform.state === 'OK' && others.state === 'OK' && models.state === 'OK') {
        for (let i = 0; i < models.body.length; i++) {
          platform.body.push(models.body[i]);
        }
        for (let i = 0; i < others.body.length; i++) {
          platform.body.push(others.body[i]);
        }
        let list = [] as any;
        for (let i = 0; i < platform.body.length; i++) {
          if (platform.body[i].clickable == 1) {
            list.push({
              text: platform.body[i].platformName,
              value: platform.body[i].platformCode,
            });
          }
        }
        this.columns = [
          {
            title: '页面标题',
            dataIndex: 'name',
            key: 'name',
            scopedSlots: { customRender: 'name' },
          },
          {
            title: '父目录',
            dataIndex: 'parents',
            key: 'parents',
            scopedSlots: { customRender: 'parents' },
          },
          {
            title: '平台',
            key: 'platform',
            dataIndex: 'platform',
            filters: list,
          },
          {
            title: '提交审核时间',
            key: 'submitTime',
            dataIndex: 'submitTime',
          },
          {
            title: '提交人',
            key: 'submitUserName',
            dataIndex: 'submitUserName',
          },
          {
            title: '操作',
            key: 'operation',
            scopedSlots: { customRender: 'operation' },
          },
        ];
        this.getTableList();
      }
    },
    async getTableList(this: any) {
      let queryType = 0; // queryType=0是待审批，其他任何值都是审批记录
      this.loading = true;
      if (this.activeTab === '审批记录') queryType = 1;
      await saiList({
        name: 'HELP',
        type: 'POST',
        data: {
          accessPlatform: this.accessPlatform,
          keyword: this.keyword,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          queryType,
        },
      }).then((res: any) => {
        if (res.state === 'OK') {
          this.loading = false;
          this.tableList = res.body.list.map((item) => {
            let parentCatalog = [] as any;
            parentCatalog = item.parents;
            if (item.parents.length > 1) {
              let first = item.parents[0];
              let second = item.parents[1];
              if (first.length + second.length > 20) {
                if (first.length > second.length) {
                  parentCatalog = [first.slice(0, 20 - second.length) + '...', second];
                } else if (first.length === second.length) {
                  parentCatalog = [first.slice(0, 10) + '...', second.slice(0, 10) + '...'];
                } else {
                  parentCatalog = [first, second.slice(0, 20 - first.length) + '...'];
                }
              }
            }
            return {
              name: item.dataType === 'catalog' ? item.cataloName : item.pageName,
              platform: item.platformName,
              parentCatalog: parentCatalog,
              ...item,
            };
          });
          this.pagination.total = res.body.total;
        }
      });
    },
    changePageNum(pageNum) {
      this.pagination.pageNum = pageNum;
      this.getTableList();
    },
    changePageSize(pageSize) {
      this.pagination.pageNum = 1;
      this.pagination.pageSize = pageSize;
      this.getTableList();
    },
    handlerSearch(this: any, e) {
      this.loading = true;
      this.search(this);
    },
    search: _.debounce(function (this: any) {
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 10;
      this.getTableList();
    }, 500),
    tableChange(pagination, filters, sorter, extra) {
      if (filters.platform) {
        this.accessPlatform = filters.platform.toString();
      } else {
        this.accessPlatform = '';
      }
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 10;
      this.getTableList();
    },
    checkPage(this: any, record) {
      if (record.dataType === 'catalog') return;
      this.$router.push({ path: '/page-check/helpcenterControl-pagePreview', query: { pageId: record.id } });
    },
    async passPage(record) {
      await saiApprove({ name: 'HELP', data: { approveId: record.id } }).then((res: any) => {
        if (res.state === 'OK') {
          message.success('已通过');
          this.getTableList();
        }
      });
    },
    async rejectPage(record) {
      await saiOverruled({ name: 'HELP', data: { approveId: record.id } }).then((res: any) => {
        if (res.state === 'OK') {
          message.success('已驳回');
          this.getTableList();
        }
      });
    },
  },
});
</script>
<style lang="less" scoped>
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /deep/.ant-tabs-nav {
    margin: 0;
  }
  /deep/.ant-tabs-nav::before {
    border: none;
  }
  /deep/.ant-tabs-tab {
    width: 136px;
    height: 32px;
    display: flex;
    justify-content: center;
    margin: 0;
    font-size: 14px;
    color: #606972;
    line-height: 20px;
  }
  /deep/.ant-tabs-tab-active {
    font-weight: 500;
    color: #0082ff;
  }
}

:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;
  th {
    background-color: #f6f9fc;
  }
}
/deep/.ant-table-row {
  font-size: 12px;
  td {
    padding: 10px 16px;
  }
}
/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}
/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}
/deep/.ant-table-filter-column {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    width: 48px;
    flex: 0;
  }
}
.table-empty {
  color: #606972;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  top: -120px;
}
.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}
.platform {
  display: inline-block;
  width: 90px;
  height: 20px;
  line-height: 16px;
  text-align: center;
  border: 1px solid;
}
.deep-learn {
  color: yellow;
  border-color: yellow;
}
.ai-capacity {
  color: blue;
  border-color: blue;
}
.operation-button {
  button {
    font-size: 12px;
    padding: 0;
  }
}
.audit-reject {
  color: red;
}
</style>
