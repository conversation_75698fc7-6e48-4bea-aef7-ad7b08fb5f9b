<template>
  <a-layout-header class="header-container">
    <div class="left">
      <img @click="routeToHomePage" src="@/assets/images/jiutian-word.png" alt="" />
    </div>
    <div class="right">
      <LinkItems />
    </div>
  </a-layout-header>
</template>

<script>
import LinkItems from './AdminLinkItems';
import { getEnvConfig } from '@/config';

export default {
  name: 'Header',
  components: {
    LinkItems,
  },
  methods: {
    routeToHomePage() {
      window.location.assign(getEnvConfig('ADMIN_HEADER_HOMEPAGE_LINK'));
    },
  },
};
</script>

<style lang="less" scoped>
.header-container {
  position: absolute;
  top: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50px;
  padding-left: 24px;
  padding-right: 0px;
  z-index: 999;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 1px 0px 0px rgba(0, 92, 164, 0.06);
  backdrop-filter: blur(8px);
}
.left {
  display: flex;
  align-items: center;
  img {
    height: 32px;
    cursor: pointer;
  }
  .title {
    height: 60px;
    margin-left: 8px;
    margin-right: 40px;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    cursor: pointer;
  }
}

.right {
  a {
    color: #c2c5cf;
    font-size: 12px;
    &:hover {
      color: #5392ff;
    }
  }
}
.nav {
  font-size: 24px;
  line-height: 1;
}
</style>
