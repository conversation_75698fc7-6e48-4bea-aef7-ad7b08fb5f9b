import request from '@/request';
const DOCUMENT_TYPE = {
  HELP: {
    list: '/common_help/manage/page/listbyplatform',
    delete: {
      page: '/common_help/manage/page/delete',
      catalog: '/common_help/manage/catalog/delete',
    },
    publish: {
      page: '/common_help/manage/page/publish',
      catalog: '/common_help/manage/catalog/publish',
    },
    sort: '/common_help/manage/source/sort',
    detail: '/common_help/manage/page/detail',
    selectUserAccess: '/common_help/hap/selectUserAccess',
    isCatalogExist: '/common_help/manage/catalog/exist',
    catalogList: '/common_help/manage/catalog/list',
    catalogUpdate: '/common_help/manage/catalog/update',
    catalogAdd: '/common_help/manage/catalog/add',
    pageAdd: '/common_help/manage/page/add',
    pageUpdate: '/common_help/manage/page/update',
    isPageExist: '/common_help/manage/page/exist',
    selectAllUsed: '/common_help/hap/selectAllUsed',
    saiList: '/common_help/sai/list',
    saiApprove: '/common_help/sai/approve',
    saiOverruled: '/common_help/sai/overruled',
    saiDetail: '/common_help/sai/detail'
  },
  SKILL: {
    list: '/common_help/asc/manage/page/list',
    sort: '/common_help/asc/manage/source/sort',
    detail: '/common_help/asc/manage/page/detail',
  },
};

const HelpCenter = {
  getList: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].list, {
      method: type || 'GET',
      data,
    });
  },
  deleteItem: (params: any) => {
    const { name, record, type, data } = params;
    return request(DOCUMENT_TYPE[name].delete[record.type], {
      method: type || 'GET',
      data,
    });
  },
  publishItem: (params: any) => {
    const { name, record, type, data } = params;
    return request(DOCUMENT_TYPE[name].publish[record.type], {
      method: type || 'GET',
      data,
    });
  },
  sortItem: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].sort, {
      method: type || 'GET',
      data,
    });
  },
  getDetail: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].detail, {
      method: type || 'GET',
      data,
    });
  },
  selectUserAccess: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].selectUserAccess, {
      method: type || 'GET',
      data,
    });
  },
  isCatalogExist: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].isCatalogExist, {
      method: type || 'GET',
      data,
    });
  },
  getCatalogList: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].catalogList, {
      method: type || 'GET',
      data,
    });
  },
  updateCatalog: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].catalogUpdate, {
      method: type || 'GET',
      data,
    });
  },
  addCatalog: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].catalogAdd, {
      method: type || 'GET',
      data,
    });
  },
  addPage: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].pageAdd, {
      method: type || 'GET',
      data,
    });
  },
  updatePage: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].pageUpdate, {
      method: type || 'GET',
      data,
    });
  },
  isPageExist: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].isPageExist, {
      method: type || 'GET',
      data,
    });
  },
  selectAllUsed: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].selectAllUsed, {
      method: type || 'GET',
      data,
    });
  },
  saiList: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].saiList, {
      method: type || 'GET',
      data,
    });
  },
  saiApprove: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].saiApprove, {
      method: type || 'GET',
      data,
    });
  },
  saiOverruled: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].saiOverruled, {
      method: type || 'GET',
      data,
    });
  },
  saiDetail: (params: any) => {
    const { name, type, data } = params;
    return request(DOCUMENT_TYPE[name].saiDetail, {
      method: type || 'GET',
      data,
    });
  },
};
export default HelpCenter;
