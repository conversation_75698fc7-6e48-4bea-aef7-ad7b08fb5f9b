<template>
  <div class="user-control-container">
    <sub-header title="用户管理"></sub-header>
    <Container>
      <container-item>
        <div class="top-bar">
          <div>
            <h1>用户列表</h1>
          </div>
          <a-space>
            <div class="select-group">
              <a-select show-search :getPopupContainer="getPopupContainer" allowClear class="group-filter" placeholder="所属一级组织" v-model:value="query.firstGroup">
                <a-select-option v-for="item in firstGroupList" :key="item.value" :value="item.label">
                  {{ item.label }}
                </a-select-option>
              </a-select>
              <a-select show-search :getPopupContainer="getPopupContainer" allowClear class="group-filter" placeholder="所属二级组织" v-model:value="query.secondGroup">
                <a-select-option v-for="item in secondGroupList" :key="item.value" :value="item.label">
                  {{ item.label }}
                </a-select-option>
              </a-select>
              <a-select show-search :getPopupContainer="getPopupContainer" allowClear class="group-filter" placeholder="所属三级组织" v-model:value="query.thirdGroup">
                <a-select-option v-for="item in thirdGroupList" :key="item.value" :value="item.label">
                  {{ item.label }}
                </a-select-option>
              </a-select>
              <a-select show-search :getPopupContainer="getPopupContainer" allowClear class="group-filter" placeholder="所属四级组织" v-model:value="query.fourthGroup">
                <a-select-option v-for="item in fourthGroupList" :key="item.value" :value="item.label">
                  {{ item.label }}
                </a-select-option></a-select
              >
            </div>
            <div>
              <a-input :placeholder="isLoginType.isOnlyPassword() ? '用户名' : '用户名/手机号'" v-model:value="query.searchKey">
                <template #prefix>
                  <jt-icon type="iconsousuo" style="font-size: 18px" />
                </template>
              </a-input>
            </div>
            <a-space>
              <div>
                <a-button-group>
                  <a-button v-if="showBatchOutport" @click="handleBatchOutport">
                    <template #icon><upload-outlined /></template>
                    导出
                  </a-button>
                  <a-button v-if="showBatchImport" @click="handleBatchImport">
                    <template #icon><download-outlined /></template>
                    导入
                  </a-button>
                </a-button-group>
              </div>
              <a-button v-if="showCreate" @click="handleCreate" type="primary">
                <template #icon>
                  <PlusOutlined />
                </template>
                新建用户
              </a-button>
            </a-space>
          </a-space>
        </div>
        <a-table :loading="tableAttr(loading).loading" :getPopupContainer="getPopupContainer" :columns="columns" :data-source="dataSource" :customHeaderRow="customHeaderRow" :rowClassName="() => 'cus-row'" :customRow="customRow" :pagination="false" @change="handleChange">
          <template #emptyText>
            <empty v-if="!loading" title="用户" :showNoDataText="showNoDataText(query)">
              <template #description>
                请立即
                <a href="javascript:;" @click="handleCreate">新建用户</a>
              </template>
            </empty>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'userName'">
              <a-tooltip :title="record.userName">
                <div class="overflow-ellipsis userName">
                  <span class="user-name-text" @click="goToDetail(record)">
                    {{ record.userName || '--' }}
                  </span>
                </div>
              </a-tooltip>
            </template>
            <template v-else-if="column.key === 'phoneNum'">
              <span>
                {{ hidePhone(record.phoneNum) || '--' }}
              </span>
            </template>
            <template v-else-if="column.key === 'formatGroupPath'">
              <a-tooltip :title="record.formatGroupPath">
                <div class="overflow-ellipsis groupPath">
                  {{ record.formatGroupPath || '--' }}
                </div>
              </a-tooltip>
            </template>
            <template v-else-if="column.key === 'fullName'">
              <span>
                {{ record.fullName || '--' }}
              </span>
            </template>
            <template v-else-if="column.key === 'status'">
              <span>
                <a-tag style="border-radius: 8px" :color="record.status === 0 ? 'success' : record.status === 1 ? 'default' : 'error'">{{ statusFilter(record.status) }}</a-tag>
              </span>
            </template>
          </template>
        </a-table>
        <Pagination :total="pagination.total" :pageNum="pagination.pageNum" :pageSize="pagination.pageSize" :pageSizeOptions="[10, 20, 50, 100]" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
      </container-item>
    </Container>
    <batch-import-dialog :loading="importPopupLoading" :visible="batchImportDialogVisible" @cancel="batchImportDialogVisible = false" @ok="batchImportDialogOk"></batch-import-dialog>
    <transition name="tooltipanim">
      <import-result ref="importTooltipRef" v-if="importTooltipVisible" @toggleVisible="toggleVisible" @refreshTable="refreshTable" />
    </transition>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, nextTick, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { message } from 'ant-design-vue';
import { PlusOutlined, DownloadOutlined, UploadOutlined } from '@ant-design/icons-vue';
import batchImportDialog from './batch-import-dialog.vue';
import importResult from './import-result.vue';
import { debounce } from 'lodash';
import { hidePhone, tableAttr, showNoDataText } from '@/utils';
import request from '@/request';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import { auths, isLoginType } from './auth';
import { checkKeycloakAuth } from '@/utils/auth';
import { downloadFile } from '@/utils/file';
import empty from '@/components/empty.vue';
import { labelForValue } from './utils/baseTool';
import { STATUSENUM } from './utils/enum';
const keycloakAuths = {
  batch_import: checkKeycloakAuth('COMMON_MGM_USER_IMPORT'),
  batch_outport: checkKeycloakAuth('COMMON_MGM_USER_EXPORT'),
  create: checkKeycloakAuth('COMMON_MGM_USER_CREATE'),
  edit: checkKeycloakAuth('COMMON_MGM_USER_EDIT'),
};

const router = useRouter();
const route = useRoute();
let getGroupId = ref([]);
const pagination = reactive({
  pageSize: route.query.pageSize ? Number(route.query.pageSize) : 10,
  pageNum: route.query.pageNum ? Number(route.query.pageNum) : 1,
  total: 0,
});
const changePageNum = (pageNum) => {
  pagination.pageNum = pageNum;
  getData();
};
const changePageSize = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.pageNum = 1;
  getData();
};

const statusFilter = (status) => {
  const item = STATUSENUM.find((item) => item.value === status);
  return item ? item.text : '';
};
const defaultChannels = route.query.channels ? String(route.query.channels).split(',') : undefined;
const statusChannels = route.query.status ? String(route.query.status).split(',') : undefined;
const query = reactive({
  groupId: route.query.groupId || undefined,
  firstGroup: route.query.firstGroup || undefined,
  secondGroup: route.query.secondGroup || undefined,
  thirdGroup: route.query.thirdGroup || undefined,
  fourthGroup: route.query.fourthGroup || undefined,
  searchKey: route.query.searchKey || undefined,
  channels: defaultChannels,
  status: statusChannels,
});

const handleChange = (pagination, filters, sorter) => {
  query.channels = filters.channel ? filters.channel : undefined;
  query.status = filters.status ? filters.status : undefined;
  getData();
};
const dataSource = ref<any[]>([]);
const loading = ref(true);
const rewriteUrlByParams = (params) => {
  const { channels, searchKey } = params;
  const rewriteQuery = {
    channels: channels && channels.length > 0 ? channels.join(',') : undefined,
    searchKey: searchKey || undefined,
    groupId: '', // 编辑返回时滞空条件
  };
  router.replace({ query: { ...route.query, ...params, ...rewriteQuery } });
};
const getData = (needRewriteUrl = true) => {
  const obj = {
    pageSize: pagination.pageSize,
    pageNum: pagination.pageNum,
    groupId: query.groupId,
    firstGroup: query.firstGroup,
    secondGroup: query.secondGroup,
    thirdGroup: query.thirdGroup,
    fourthGroup: query.fourthGroup,
    searchKey: query.searchKey,
    channels: query.channels,
    status: query.status,
  };
  needRewriteUrl && rewriteUrlByParams(obj);
  request('/web/admin/um/v1/user/list', {
    method: 'POST',
    data: obj,
  }).then((res: any) => {
    loading.value = false;
    if (res.msg === 'OK') {
      dataSource.value = res.data.data;
      pagination.total = res.data.total;
    }
  });
};
getData(false);

const getPopupContainer = (node) => {
  if (node && dataSource.value.length > 5) {
    return node.parentNode;
  }
  return document.body;
};

const channelFilters = ref<any[]>([]);
const getChannelFilters = () => {
  request('/web/admin/um/v1/user/channel/list', {
    method: 'GET',
  }).then((res: any) => {
    if (res.msg === 'OK') {
      channelFilters.value = res.data.map((item: any) => {
        return {
          text: item,
          value: item,
        };
      });
    }
  });
};
getChannelFilters();

const columns = computed(() => {
  let columns = [
    {
      title: '用户名',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '手机号',
      dataIndex: 'phoneNum',
      key: 'phoneNum',
    },
    {
      title: '姓名',
      dataIndex: 'fullName',
      key: 'fullName',
    },
    {
      title: '所属组织',
      dataIndex: 'formatGroupPath',
      key: 'formatGroupPath',
    },
    {
      title: '注册时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
    {
      title: '注册渠道',
      dataIndex: 'channel',
      key: 'channel',
      filters: channelFilters.value,
      defaultFilteredValue: defaultChannels,
      width: 140,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: STATUSENUM,
      defaultFilteredValue: statusChannels,
    },
  ];
  if (isLoginType.isOnlyPassword()) {
    columns.splice(1, 1);
  }
  return columns;
});

const firstGroupList = ref<any[]>([]);
const secondGroupList = ref<any[]>([]);
const thirdGroupList = ref<any[]>([]);
const fourthGroupList = ref<any[]>([]);
const wrapList = (list) => {
  return list.map((item) => {
    return {
      label: item.groupName,
      value: item.groupId,
    };
  });
};
watch(
  () => query.firstGroup,
  async (val) => {
    if (val) {
      if (firstGroupList.value.length > 0) {
        let filterRes = labelForValue(firstGroupList.value, val);
        getGroupId.value.splice(0, 0, filterRes.value);
        query.groupId = filterRes.value;
        await getGroupList(filterRes.value).then((data) => {
          secondGroupList.value = data;
        });
      }
    } else {
      query.secondGroup = undefined;
      query.thirdGroup = undefined;
      query.fourthGroup = undefined;
      secondGroupList.value = [];
      thirdGroupList.value = [];
      fourthGroupList.value = [];
      query.groupId = '';
    }
  }
);
watch(
  () => query.secondGroup,
  async (val) => {
    if (val) {
      if (secondGroupList.value.length > 0) {
        let filterRes = labelForValue(secondGroupList.value, val);
        getGroupId.value.splice(1, 0, filterRes.value);
        query.groupId = filterRes.value;
        await getGroupList(filterRes.value).then((data) => {
          thirdGroupList.value = data;
        });
      }
    } else {
      query.thirdGroup = undefined;
      query.fourthGroup = undefined;
      thirdGroupList.value = [];
      fourthGroupList.value = [];
      if (query.firstGroup) {
        query.groupId = getGroupId.value[0];
      }
    }
  }
);
watch(
  () => query.thirdGroup,
  async (val) => {
    if (val) {
      if (thirdGroupList.value.length > 0) {
        let filterRes = labelForValue(thirdGroupList.value, val);
        getGroupId.value.splice(2, 0, filterRes.value);
        query.groupId = filterRes.value;
        await getGroupList(filterRes.value).then((data) => {
          fourthGroupList.value = data;
        });
      }
    } else {
      fourthGroupList.value = [];
      query.fourthGroup = undefined;
      if (query.secondGroup) {
        query.groupId = getGroupId.value[1];
      }
    }
  }
);
watch(
  () => query.fourthGroup,
  async (val) => {
    if (val) {
      if (fourthGroupList.value.length > 0) {
        let filterRes = labelForValue(fourthGroupList.value, val);
        getGroupId.value.splice(3, 0, filterRes.value);
        query.groupId = filterRes.value;
      }
    } else {
      if (query.thirdGroup) {
        query.groupId = getGroupId.value[2];
      }
    }
  }
);

const debounceGetData = debounce(getData, 500);

watch(
  query,
  (val) => {
    loading.value = true;
    pagination.pageNum = 1;
    debounceGetData();
  },
  { deep: true }
);

const getGroupList = async (groupId: any = '') => {
  try {
    const res = await request('/web/admin/um/v1/group/list-tree', {
      method: 'GET',
      data: { groupId },
    });
    if (res.msg === 'OK') {
      return wrapList(res.data);
    } else {
      return false;
    }
  } catch (e) {
    return e;
  }
};

getGroupList().then(async (value) => {
  firstGroupList.value = value;
  let filterRes = labelForValue(firstGroupList.value, query.firstGroup);
  query.groupId = filterRes.value;
  getGroupId.value.splice(0, 0, filterRes.value);
  await getGroupList(filterRes.value).then(async (data) => {
    secondGroupList.value = data;
  });
  if (query.secondGroup) {
    let filterRes = labelForValue(secondGroupList.value, query.secondGroup);
    query.groupId = filterRes.value;
    getGroupId.value.splice(1, 0, filterRes.value);
    await getGroupList(filterRes.value).then(async (data) => {
      thirdGroupList.value = data;
    });
  }
  if (query.thirdGroup) {
    let filterRes = labelForValue(thirdGroupList.value, query.thirdGroup);
    query.groupId = filterRes.value;
    getGroupId.value.splice(2, 0, filterRes.value);
    await getGroupList(filterRes.value).then((data) => {
      fourthGroupList.value = data;
    });
  }
  if (query.fourthGroup) {
    let filterRes = labelForValue(fourthGroupList.value, query.fourthGroup);
    query.groupId = filterRes.value;
    getGroupId.value.splice(3, 0, filterRes.value);
  }
});

const goToDetail = (record) => {
  router.push({
    path: '/user-management/userControl-detail',
    query: {
      ...route.query,
      id: record.userId,
    },
  });
};
const handleBatchOutport = async () => {
  message.success('正在导出，请稍后...');
  await downloadFile({ url: `/web/admin/um/v1/user/export` });
  message.success('导出完成');
};
const handleCreate = () => {
  router.push({
    path: '/user-management/userControl-create',
    // query: route.query,
  });
};

const batchImportDialogVisible = ref(false);
const handleBatchImport = () => {
  batchImportDialogVisible.value = true;
};
const importPopupLoading = ref(false);
const store = useStore();
const toggleVisible = (bol) => store.commit('UPDATE_TOOLTIPVISIBLE', bol);

const importTooltipRef = ref<any>(null);
const batchImportDialogOk = (fileList) => {
  console.log(fileList[0]);
  const params: any = new FormData();
  params.append('file', fileList[0]);
  importPopupLoading.value = true;

  // 异步导入学生列表，打开时进行请求
  request('/web/admin/um/v1/user/import', { method: 'POST', data: params, headers: { 'Content-Type': 'multipart/form-data' } }).then((res: any) => {
    if (res.msg === 'OK') {
      importPopupLoading.value = false;
      toggleVisible(true);
      batchImportDialogVisible.value = false;
      message.success('上传成功，等待处理');
      nextTick(() => {
        importTooltipRef.value.startInterval();
      });
    } else {
      importPopupLoading.value = false;
      message.error(res.errorMessage || '上传失败');
    }
  });
};
const importTooltipVisible = computed(() => store.state.importTooltipVisible);

const refreshTable = () => {
  getData();
};

const showCreate = computed(() => {
  return auths.create && keycloakAuths.create;
});

const loginType = computed(() => {
  return isLoginType;
});

const showBatchOutport = computed(() => {
  return auths.batch_outport && keycloakAuths.batch_outport;
});

const showBatchImport = computed(() => {
  return auths.batch_import && keycloakAuths.batch_import;
});

const customRow = (record) => {
  return {
    onClick: () => {
      goToDetail(record);
    },
  };
};

const customHeaderRow = () => {
  return {
    // props: {
    style: {
      fontsize: '12px',
    },
    // },
  };
};
</script>

<style lang="less" scoped>
.user-control-container {
  #container-wrap {
    height: calc(100vh - 108px);
  }
}

.top-bar {
  padding: 16px 0;
  padding-top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.groupPath {
  display: block;
  max-width: 200px;
}

.userName {
  display: block;
  max-width: 200px;
}

.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}

.group-filter {
  width: 120px;
  font-size: 12px;
}

/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}

/deep/.ant-table-filter-column {
  justify-content: left;

  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    width: 48px;
    flex: 0;
  }
}

/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}

:deep(.cus-row) {
  cursor: pointer;
  font-size: 12px;

  .user-name-text {
    &:hover {
      color: @primary-color;
    }
  }
}

:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;

  th {
    background-color: #f6f9fc;
  }
}
</style>
