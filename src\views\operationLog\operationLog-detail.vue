<template>
  <div class="message-control-container">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '操作日志', query: $route.query }">操作日志</router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>操作日志详情</a-breadcrumb-item>
    </a-breadcrumb>
    <div style="background: #ececec; padding: 15px">
      <a-card title="基本信息" :bordered="false" style="width: 100%">
        <div class="flex—main">
          <div>
            <span class="label">操作时间：</span>
            {{ baseInfo.eventTime }}
          </div>
          <div><span class="label">操作人：</span>{{ baseInfo.operator }}</div>
          <div>
            <span class="label">操作结果：</span><a-tag style="border-radius: 8px" :color="baseInfo.eventResult == 1 ? 'success' : 'error'">{{ statusFilter(baseInfo.eventResult) }}</a-tag>
          </div>
          <div><span class="label"> 操作模块：</span>{{ baseInfo.eventModule }}</div>
          <div><span class="label">操作：</span>{{ baseInfo.eventAction }}</div>
          <div><span class="label">操作对象：</span>{{ baseInfo.eventObject }}</div>
          <div><span class="label">操作对象ID：</span>{{ baseInfo.eventObjectId }}</div>
        </div>
      </a-card>
    </div>
    <div style="background: #ececec; padding: 15px">
      <a-card title="详细信息" :bordered="false" style="width: 100%">
        <a-table :columns="columns" :data-source="dataSource" :customHeaderRow="customHeaderRow" :rowClassName="() => 'cus-row'" :pagination="false"> </a-table>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import ApiOperationLog from '../../apis/operationLog';
import { STATUSENUM } from './utils/enum';
import dayjs from 'dayjs';
const route = useRoute();
const baseInfo = ref({
  eventModule: '',
  eventAction: '',
  operator: '',
  eventTime: '',
  eventResult: '',
  eventObject: '',
  eventObjectId: '',
});
const dataSource = ref([]);
const columns = computed(() => {
  let columns = [
    {
      title: '参数',
      dataIndex: 'userName',
      key: 'userName',
    },
    {
      title: '取值',
      dataIndex: 'phoneNum',
      key: 'phoneNum',
    },
  ];
  return columns;
});

const getDeatil = () => {
  const id = route.query.id;
  if (!id) {
    console.error('缺少必要的 id 参数');
    return;
  }
  ApiOperationLog.APILogDetail({ id })
    .then((res) => {
      if (res.data) {
        baseInfo.value = {
          eventModule: res.data.eventModule || '--', // 操作模块
          eventAction: res.data.eventAction || '--', // 操作类型
          operator: res.data.operator || '--', // 操作人
          eventTime: res.data.eventTime ? dayjs(res.data.eventTime).format('YYYY-MM-DD HH:mm:ss') : '--', // 转换操作时间, // 操作时间
          eventResult: res.data.eventResult || '--', // 操作结果
          eventObject: res.data.eventObject || '--', // 操作对象
          eventObjectId: res.data.eventObjectId || '--', // 操作对象id
        };
        // 如果 eventDetail 存在，将其转换为表格数据
        if (res.data.eventDetail) {
          try {
            const eventDetail = JSON.parse(res.data.eventDetail); // 转换为 JSON 对象
            dataSource.value = Object.keys(eventDetail).map((key, index) => ({
              key: index + 1, // 唯一标识
              userName: key, // 参数名
              phoneNum: eventDetail[key], // 参数值
            }));
          } catch (error) {
            console.error('eventDetail 解析失败:', error);
          }
        }
      }
    })
    .catch((error) => {
      throw new Error('获取详情失败');
    });
};
const statusFilter = (status) => {
  const item = STATUSENUM.find((item) => item.value == status);
  return item ? item.text : '';
};
onMounted(() => {
  getDeatil();
});
</script>

<style lang="less" scoped>
.nav {
  height: 58px;
  line-height: 58px;
  padding-left: 20px;
  background: #ffffff;
  font-weight: 500;
  color: #121f2c;
}
.flex—main {
  display: flex;
  flex-wrap: wrap; /* 自动换行 */
  justify-content: space-between; /* 每行元素均匀分布 */
}
.flex—main > div {
  width: calc(33.333% - 16px); /* 每行 3 个，减去间隔 */
  padding: 8px; /* 内部间距 */
}
.label {
  display: inline-block;
  width: 100px; /* 固定宽度，确保对齐 */
  text-align: right; /* 右对齐 */
  margin-right: 5px; /* 标签与内容之间的间距 */
}
</style>
