import { GET, POST } from '@/request';

export const sendSmsCode = (data, useError) => POST('/web/um/v1/public/smscode', data || {}, { useError });
export const userSendSmsCode = (data, useError) => GET('/messaging/web/auth/sendSmsCode', data || {}, { useError });

export const sendCode = (function () {
  let sending = false;
  return (params) => {
    if (sending) {
      return;
    }
    sending = true;
    return sendSmsCode(params, true).then((res) => {
      sending = false;
      return res;
    });
  };
})();

export const userSendCode = (function () {
  let sending = false;
  return () => {
    if (sending) {
      return;
    }
    sending = true;
    return userSendSmsCode({}, true).then((res) => {
      sending = false;
      return res;
    });
  };
})();
