import { echartsCommonOptions, colorMap } from '@/utils/echarts';
import { ChartData, DataParamsForUserBox } from '@/apis/interface';

export const getDataSeries = (data: ChartData[]): any => {
  let option = {};
  //  占用算力
  interface ChartsDatas {
    userMonthActiveCount: number[];
    userLoginCount: number[];
    userCount: number[];
  }
  interface ChartsOptions {
    xAxis: string[];
    datas: ChartsDatas;
  }
  const getLinearColor = (index) => {
    return {
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: colorMap[index].replace('0.85)', '0.15)'), // 100% 处的颜色
        },
        {
          offset: 1,
          color: colorMap[index].replace('0.85)', '0)'), // 0% 处的颜色
        },
      ],
      global: false, // 缺省为 false
    };
  };
  const chartsData: ChartsOptions = {
    xAxis: [],
    datas: {
      userMonthActiveCount: [],
      userLoginCount: [],
      userCount: [],
    },
  };
  const ObjectSortArr = [
    {
      key: 'userCount',
      name: '用户数（人）',
      show: true,
    },
    {
      key: 'userLoginCount',
      name: '登录用户数（人）',
      show: true,
    },
    {
      key: 'userMonthActiveCount',
      name: '月活用户数（人）',
      show: true,
    },
  ];
  data.forEach((x) => {
    chartsData.xAxis.push(x.formatTime.replace(/\s/, '\n'));
    chartsData.datas.userMonthActiveCount.push(x.userMonthActiveCount);
    chartsData.datas.userLoginCount.push(x.userLoginCount);
    chartsData.datas.userCount.push(x.userCount);
  });
  const series: any = [];
  for (let i = 0; i < ObjectSortArr.length; i++) {
    const item = ObjectSortArr[i];
    const itemData = chartsData.datas[item.key];
    series.push({
      name: item.name,
      data: itemData,
      type: 'line',
      symbolSize: 10,
      showSymbol: false,
      areaStyle: { color: getLinearColor(i) },
      itemStyle: {
        color: colorMap[i],
        borderWidth: 10,
      },
      lineStyle: { color: colorMap[i] },
    });
  }
  option = {
    color: colorMap,
    dataZoom: [
      {
        type: 'inside',
        start: (100 / 365) * 334, // 默认显示最近30天的数据
        end: 100,
      },
      {
        type: 'slider',
        left: '200px',
        right: '200px',
      },
    ],
    xAxis: {
      type: 'category',
      boundaryGap: true,
      data: chartsData.xAxis,
    },
    yAxis: {
      position: `left`,
      type: 'value',
      name: `用户数（人）`,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'none',
        label: {},
      },
      // alwaysShowContent: true,
      formatter: (params: DataParamsForUserBox[], ticket: string): string | HTMLElement | HTMLElement[] => {
        let rowString = ``;
        params.forEach((x: DataParamsForUserBox, i) => {
          rowString += `<div class="row">
              <p>
                <span class="dot-common" style="background-color:${colorMap[i]}"></span><span style="display:inline-block;min-width:50px">${x.data}</span>人
                <span style="margin-left:20px">${x.seriesName.replace('（人）', '')}</span>
              </p>
            </div>`;
        });
        return `
      <div class="tooltip-wrap">
        <div class="tooltip-content">
          ${rowString}
        </div>
        <div class="tooltip-footer">${params[0].axisValue}</div>
      </div>
      `;
      },
    },
    legend: {
      left: 'left',
      data: (() =>
        series.map((x) => {
          if (ObjectSortArr.find((y) => y.name === x.name)?.show) {
            return x.name;
          }
        }))(),
      itemHeight: 8,
    },
    series,
    ...echartsCommonOptions,
  };
  return option;
};
