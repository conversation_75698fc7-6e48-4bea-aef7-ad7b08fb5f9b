<template>
  <micro-components moduleName="unread-message" :data="{ requester, config, titleStyle }" />
</template>

<script>
import { GET, POST } from '@/request/index';
import { getEnvConfig } from '@/config/index';

export default {
  name: 'unread-message-remote',
  data() {
    return {
      requester: { GET, POST },
      config: {
        messageUrl: `/${getEnvConfig('MESSAGE_URL_PATH')}`,
      },
      titleStyle: {
        height: '50px',
        'line-height': '50px',
        'font-size': '12px',
        color: '#555555',
        background: 'rgba(255, 255, 255, 0.15)',
        hoverTitleColor: '#555555',
      },
    };
  },
};
</script>
