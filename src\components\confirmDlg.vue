<template>
  <a-modal :visible="visible" :closable="false" :footer="null" width="405px">
    <div class="confirm-modal">
      <slot name="title">
        <div class="modal-title">
          <ExclamationCircleFilled class="title-icon" :class="[{ 'danger': type === 'danger' }]" />
          <span>{{ title }}</span>
        </div>
      </slot>
      <slot name="content">
        <div class="modal-content">
          <p>{{ content }}</p>
        </div>
      </slot>
      <slot name="footer">
        <div class="modal-footer">
          <a-button @click="$emit('cancel')" style="margin-left: 8px">{{ cancelBtn || '取消' }}</a-button>
          <a-button @click="$emit('confirm')" type="primary" style="margin-left: 8px" :danger="type === 'danger'">{{ confirmBtn || '确定' }}</a-button>
        </div>
      </slot>
    </div>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';

export default defineComponent({
  components: { ExclamationCircleFilled },
  props: {
    type: String,
    visible: Boolean,
    title: String,
    content: String,
    cancelBtn: String,
    confirmBtn: String,
  },
  emits: ['cancel', 'confirm'],
  setup() {},
  data() {
    return {};
  },
  methods: {},
});
</script>
