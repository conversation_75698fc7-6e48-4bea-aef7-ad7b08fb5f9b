<template>
  <a-layout-header class="header-container">
    <div class="left">
      <img src="@/assets/images/preview/chinamobile-jiutian.png" alt="" />
      <span class="title">九天人工智能平台</span>
      <div class="jt-header-left-center">
        <ul class="jt-header-left-center-ul" style="margin-bottom: 0">
          <li>大模型</li>
          <li>解决方案</li>
          <li>生态合作</li>
          <li>关于我们</li>
        </ul>
      </div>
    </div>
    <div class="jt-header-right">
      <ul style="margin-bottom: 0" class="jt-header-right-ul">
        <li>消息中心</li>
        <li>帮助中心</li>
        <li>
          <div class="right" style="height: 50px; display: flex; align-items: center">
            <a-space>
              <a-dropdown v-if="userInfo.userId">
                <div class="user-box">
                  <img class="avatar" :src="userInfo.image || defaultAvatar" alt="" />
                  <p :title="userInfo.userName" style="margin: 0">{{ userInfo.userName }}</p>
                </div>
              </a-dropdown>
              <ul v-else class="header-menu">
                <li>
                  <a>登录</a>
                </li>
              </ul>
            </a-space>
          </div>
        </li>
      </ul>
    </div>
  </a-layout-header>
</template>

<script setup lang="ts">
import { useStore } from 'vuex';
import { computed } from 'vue';
import defaultAvatar from '@/assets/images/avatar_big.png';
const store = useStore();
const userInfo = computed(() => store.state.userInfo);
</script>

<style lang="less" scoped>
.header-container {
  position: absolute;
  top: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-bottom: 1px solid rgba(18, 31, 44, 0.08);
  backdrop-filter: blur(7px);
  height: 60px;
  padding-left: 24px;
  padding-right: 0px;
  z-index: 2014; // 会与其他z-index有冲突，故使用2014
  transition: 0.3s all ease;
  .left {
    display: flex;
    align-items: center;
    img {
      height: 32px;
    }
    .title {
      height: 60px;
      margin-left: 8px;
      margin-right: 40px;
      font-size: 16px;
      font-weight: 600;
      color: #121f2c;
      font-family: PingFangSC-Medium, PingFang SC, sans-serif;
    }
    .jt-header-left-center {
      .jt-header-left-center-ul {
        display: flex;
        li {
          width: 88px;
          height: 61px;
          font-size: 14px;
          font-weight: 600;
          color: #606972;
          display: flex;
          justify-content: center;
          border-bottom: 2px solid transparent;
        }
        .defaultStyle {
          color: #00b3cc;
          border-color: #00b3cc;
        }
      }
    }
  }
  .jt-header-right {
    display: flex;
    justify-content: center;
    align-items: center;
    &-ul {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      width: 266px;
      li {
        // width: 56px;
        font-size: 14px;
        font-weight: 600;
        color: #606972;
      }
    }
  }
  .user-box {
    display: flex;
    align-items: center;
    .avatar {
      width: 28px;
      height: 28px;
      margin-right: 8px;
      border-radius: 50%;
    }
    p {
      color: #555555;
      font-size: 12px;
      line-height: 20px;
      max-width: 85px;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
</style>
