<template>
  <!-- 底部样式 -->
  <div class="bottomTop">
    <div class="bottomTop-img">
      <div class="bottomTop-box">
        <div class="bottomTopLeft">
          <p>欢迎使用 AI 能力平台</p>
          <div class="button"><p>立即体验</p></div>
        </div>
        <div class="bottomTopRight">
          <div>
            <p>145个</p>
            <span>能力数</span>
          </div>
          <div>
            <p>300+亿次</p>
            <span>核心能力</span>
          </div>
          <div>
            <p>200+亿次</p>
            <span>应用数</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'dataTab',
};
</script>
<style lang="less" scoped>
.bottomTop {
  min-width: 1320px;
  height: 160px;
  background: linear-gradient(270deg, #00a9d0 0%, #00d1d1 100%);
  background: url('../../../assets/images/preview/left.png') no-repeat left top / 465px 160px, url('../../../assets/images/preview/right.png') no-repeat right top / 900px 160px, linear-gradient(to right, #00a9d0, #00d1d1);
  .bottomTop-img {
    padding-top: 40px;
    width: 1320px;
    margin: 0 auto;
    height: 100%;

    display: flex;
    justify-content: space-between;
    .bottomTop-box {
      display: flex;
      justify-content: space-between;
      margin: 0 auto;
      width: 1200px;
      .bottomTopLeft {
        p:nth-child(1) {
          height: 33px;
          font-size: 24px;
          font-weight: 500;
          color: #ffffff;
          line-height: 33px;
          margin: 0px 0 16px 0;
        }
        .button {
          width: 165px;
          height: 40px;
          border-radius: 2px;
          border: 1px solid #ffffff;
          cursor: pointer;
          &:hover {
            width: 165px;
            height: 40px;
            background: #4cd6de;
            cursor: pointer;
          }
          p {
            width: 64px;
            height: 40px;
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
            line-height: 40px;
            margin: 0 auto;
            cursor: pointer;
          }
        }
      }

      .bottomTopRight {
        display: flex;
        div:nth-child(1),
        div:nth-child(2) {
          margin-right: 64px;
        }
        div {
          p {
            height: 50px;
            font-size: 36px;
            font-weight: 600;
            color: #ffffff;
            line-height: 50px;
            margin-bottom: 4px;
          }

          span {
            height: 22px;
            font-size: 16px;
            font-weight: 400;
            color: #ffffff;
            line-height: 22px;
          }
        }
      }
    }
  }
}
</style>
