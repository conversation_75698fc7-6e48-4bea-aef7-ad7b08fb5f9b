<template>
  <a-modal :visible="visible" title="编辑组织" width="554px"  @cancel="cancel" @ok="handleOK">
    <a-form ref="formRef" name="form" :colon="false" :model="formData" :rules="rules" :wrapper-col="{ span: 15 }" autocomplete="off">
      <a-form-item label="组织名称" name="groupName">
        <a-input v-model:value.trim="formData.groupName" placeholder="请输入" allow-clear />
      </a-form-item>
    </a-form>
    <template #footer>
      <div>
        <a-button type="default" @click="cancel">取消</a-button>
        <a-button type="primary" :loading="confirmLoading" @click="handleOK">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { defineProps, watch, ref, reactive, defineEmits } from 'vue';
import { message, Modal } from 'ant-design-vue';
import request from '@/request';
import { debounce } from 'lodash';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  detail: {
    type: Object,
    default: () => {},
  },
});

const formRef = ref(null);

const formData = reactive({
  groupName: '',
});

watch(
  () => props.detail,
  (val) => {
    formData.groupName = val.groupName;
  },
  {
    deep: true,
    immediate: true,
  }
);

const inputBlur = async () => {
  let resData = null;
  const param = {
    isCreate: false,
    groupId: props.detail.groupId,
    groupName: formData.groupName,
  };
  const res = await request('/web/admin/um/v1/group/exist', {
    method: 'GET',
    data: param,
  })
  if (res.code === 0) {
    resData = res.data;
  } else {
    resData = null;
  }
  return resData;
};

const validateName = async (rule, value) => {
  if (value === props.detail.groupName) return;
  if (!value || value.length > 30) {
    return Promise.reject('请输入30字符以内的名称');
  } else {
    const isRepeat = await inputBlur();
    if (isRepeat) {
      return Promise.reject('兄弟组织中已有同名组织，不可重复创建');
    } else {
      return Promise.resolve();
    }
  }
};
const rules = {
  groupName: [
    { required: true, trigger: 'blur', validator: validateName }
  ],
};

const emits = defineEmits(['cancelModal']);
//关闭
const cancel = () => {
  formRef.value?.resetFields();
  emits('cancelModal');
};

const confirmLoading = ref(false);

const handleOK = debounce(() => {
  formRef.value.validate().then(() => {
    confirmLoading.value = true;
    const param = {
      groupName: formData.groupName,
      groupId: props.detail.groupId,
    };
    request('/web/admin/um/v1/group/update', {
      method: 'POST',
      data: param,
    }).then((res) => {
      if (res.code === 0) {
        if (res.data) {
          message.success('编辑组织成功');
          formRef.value?.resetFields();
          emits('cancelModal', props.detail.level);
        } else {
          message.error(res.msg);
        }
      } else {
        message.error(`编辑组织失败，${res.msg}`);
      }
      confirmLoading.value = false;
    });
  })
  .catch(err => {
    throw new Error(err);
  });
}, 500);
</script>

<style lang="less" scoped>
:deep .ant-form-item {
  margin-bottom: 32px;
}
:deep .ant-modal-title {
  color: #121f2c;
}
:deep .ant-form-item-label > label {
  color: #121f2c;
  margin-right: 5px;
  width: 130px;
  text-align: right;
  display: inline-block;
  vertical-align: sub;
  &::after {
    content: '';
  }
}
:deep .ant-form-item-with-help .ant-form-item-explain{
  height: 0;
  min-height: 0;
}
</style>
