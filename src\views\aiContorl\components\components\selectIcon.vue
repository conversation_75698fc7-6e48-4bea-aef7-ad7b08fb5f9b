<template>
  <div>
    <a-modal :visible="visible" title="选择功能图标" @ok="handleOk" @cancel="handleCancel" :width="550" :bodyStyle="{ height: '357px', padding: '30px', overflowY: 'auto' }">
      <div class="content">
        <div v-for="(item, index) in iconList" :key="index" class="content-item" :class="current === index ? 'active' : ''" @click="handleSelect(index)">
          <a-popover>
            <template #content>
              <span>{{ (item as any).key.split('.')[0] }}</span>
            </template>
            <img :src="(item as any)?.value as string" alt="" class="content-item-img" />
          </a-popover>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, ref } from 'vue';

const emits = defineEmits(['handleOk', 'handleCancel']);
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  current: {
    type: Number,
    default: 0,
  },
  iconList: {
    type: Array,
    default() {
      return [];
    },
  },
});

const current = ref(props.current);

const handleOk = () => {
  emits('handleOk', current.value);
};

const handleCancel = () => {
  emits('handleCancel');
};

const handleSelect = (val) => {
  current.value = val;
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  &-item {
    width: 64px;
    height: 64px;
    margin-right: 20px;
    margin-bottom: 20px;
    background: #f4f8ff;
    border-radius: 2px;
    padding: 8px;
    &:nth-child(6n) {
      margin-right: 0;
    }
    &:hover {
      border: 1px solid #0082ff;
    }
    &-img {
      width: 100%;
      height: 100%;
    }
  }
  .active {
    border: 1px solid #0082ff;
  }
}
</style>
