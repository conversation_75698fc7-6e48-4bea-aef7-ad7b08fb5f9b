/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-14 11:05:04
 * @Last Modified by: sun<PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-04-06 15:58:34
 */

import DynamicManagement from './dynamicMangement';
import HelpCenter from './helpCenter';
import MessageCenter from './messageCenter';
import Resource from './resource';
import uploadForEditor from './upload';
import User from './user';
import Role from './role';
export { uploadForEditor, User, Resource, HelpCenter, MessageCenter, Role, DynamicManagement };
