import { auths } from '@/views/userControl/auth';
import store from '@/store';

import { createRouter, createWebHashHistory, RouteRecordRaw } from 'vue-router';

import Compute from '../views/compute/index.vue';
import InviteCodeDetail from '../views/compute/inviteCodeDetail.vue';
import NoAuth from '@/views/no-auth.vue';
import { getEnvConfig, getDefaultConfig } from '@/config';
import UserManagementList from '../views/userControl/list.vue';
import organizationManageList from '../views/organizationManage/list.vue';
import RoleManagementList from '../views/roleControl/list.vue';
import Home from '../views/home/<USER>';
import NoAuth from '@/views/no-auth.vue';
import Admin from '@/views/admin/index.vue';
import UserManagementList from '../views/userControl/list.vue';
import organizationManageList from '../views/organizationManage/list.vue';
import RoleManagementList from '../views/roleControl/list.vue';
import MessageManagementList from '../views/messageControl/list.vue';
import UserManagementList from '../views/userControl/list.vue';
import PlatformDocumentList from '../views/helpcenterControl/platformDocument/list.vue';
import SkillDocumentList from '../views/helpcenterControl/skillDocument/list.vue';

import { updateBeforeUnloadListener, confirmBeforeLeavingPage } from './utils';
const routes: Array<RouteRecordRaw> = [
  {
    path: '/no-auth',
    name: 'noAuth',
    component: NoAuth,
  },
  {
    path: '/admin',
    name: 'Admin',
    component: Admin,
  },
  {
    path: '/home',
    name: 'Home',
    component: Home,
  },
  {
    path: '/capacity-management/preview',
    name: '预览页面',
    component: () => import('../views/aiContorl/preview.vue'),
  },
  {
    path: '/user-management',
    name: '用户管理',
    component: UserManagementList,
  },
  {
    path: '/user-management/userControl-detail',
    name: '用户详情',
    component: () => import('../views/userControl/userControl-detail.vue'),
  },
  {
    path: '/user-management/userControl-edit',
    name: '用户编辑',
    component: () => import('../views/userControl/userControl-edit.vue'),
  },
  {
    path: '/user-management/userControl-create',
    name: '用户创建',
    component: () => import('../views/userControl/userControl-edit.vue'),
  },
  {
    path: '/organization-management',
    name: '组织管理',
    component: organizationManageList,
  },
  {
    path: '/organization-management/detail',
    name: '组织详情',
    component: () => import('../views/organizationManage/detail.vue'),
  },
  // {
  //   path: '/organization-management/userControl-edit',
  //   name: '用户编辑',
  //   component: () => import('../views/organizationManage/organizationManage-edit.vue'),
  // },
  // {
  //   path: '/organization-management/userControl-create',
  //   name: '用户创建',
  //   component: () => import('../views/organizationManage/organizationManage-edit.vue'),
  // },
  // 角色管理推迟上线
  {
    path: '/role-management',
    name: '角色管理',
    component: RoleManagementList,
  },
  {
    path: '/role-management/roleControl-detail',
    name: '角色详情',
    component: () => import('../views/roleControl/roleControl-detail.vue'),
  },
  {
    path: '/role-management/roleControl-create',
    name: '角色创建',
    component: () => import('../views/roleControl/roleControl-roleEdit.vue'),
  },
  {
    path: '/role-management/roleControl-roleEdit',
    name: '角色编辑',
    component: () => import('../views/roleControl/roleControl-roleEdit.vue'),
  },
  {
    path: '/role-management/roleControl-roleUserEdit',
    name: '角色用户编辑',
    component: () => import('../views/roleControl/roleControl-roleUserEdit.vue'),
  },
  {
    path: '/workorder-manage',
    name: '工单管理',
    component: () => import('../views/workorderManage/index.vue'),
  },
  {
    path: '/workorder-manage/detail/:orderNumber',
    name: '工单详情',
    component: () => import('../views/workorderManage/workorderDetail/index.vue'),
  },
  {
    path: '/workorder-manage/receiver',
    name: '接收人管理',
    component: () => import('../views/workorderManage/receiverManage/index.vue'),
  },
  {
    path: '/message-management',
    name: '站内信发布',
    component: MessageManagementList,
  },
  {
    path: '/message-management/messageControl-detail',
    name: '站内信详情',
    component: () => import('../views/messageControl/messageControl-detail.vue'),
  },
  {
    path: '/message-management/messageControl-preview/:mailId',
    name: '站内信预览',
    component: () => import('../views/messageControl/messageControl-preview.vue'),
  },
  {
    path: '/message-management/messageControl-edit',
    name: '站内信编辑',
    component: () => import('../views/messageControl/messageControl-edit.vue'),
    meta: {
      confirmLeave: true,
    },
  },
  {
    path: '/message-management/messageControl-create',
    name: '站内信创建',
    component: () => import('../views/messageControl/messageControl-edit.vue'),
    meta: {
      confirmLeave: true,
    },
  },
  {
    path: '/message-check',
    name: '站内信审核',
    component: () => import('../views/messageCheck/pageCheck/list.vue'),
  },
  {
    path: '/message-check/helpcenterControl-pagePreview',
    name: '查看站内信',
    component: () => import('../views/messageCheck/pageCheck/pagePreview.vue'),
  },
  {
    path: '/capacity-management',
    name: '能力列表',
    component: () => import('../views/aiContorl/table-list.vue'),
  },
  {
    path: '/capacity-management/documentsPreview',
    name: '技术文档预览',
    component: () => import('../views/aiContorl/components/documentsPreview.vue'),
  },
  {
    path: '/capacity-management/capacity-create',
    name: '新建能力',
    component: () => import('../views/aiContorl/capacity-create.vue'),
  },
  {
    path: '/capacity-management/capacity-edit/:name',
    name: '编辑能力',
    component: () => import('../views/aiContorl/capacity-create.vue'),
  },
  {
    path: '/platform-document',
    name: '平台文档管理',
    component: PlatformDocumentList,
  },
  {
    path: '/platform-document/helpcenterControl-createPage',
    name: '新建页面',
    component: () => import('../views/helpcenterControl/platformDocument/createPage.vue'),
    meta: {
      confirmLeave: true,
    },
  },
  {
    path: '/platform-document/helpcenterControl-pagePreview',
    name: '页面预览',
    component: () => import('../views/helpcenterControl/platformDocument/pagePreview.vue'),
  },
  {
    path: '/skill-document',
    name: '能力文档管理',
    component: SkillDocumentList,
  },
  {
    path: '/skill-document/preview',
    name: '能力页面预览',
    component: () => import('../views/helpcenterControl/skillDocument/preview.vue'),
  },
  {
    path: '/page-check',
    name: '页面审核',
    component: () => import('../views/helpcenterControl/pageCheck/list.vue'),
  },
  {
    path: '/page-check/helpcenterControl-pagePreview',
    name: '查看页面',
    component: () => import('../views/helpcenterControl/pageCheck/pagePreview.vue'),
  },
  //动态管理
  {
    path: '/dynamic-manage',
    name: '动态管理',
    component: () => import('../views/dynamicManage/list'),
  },
  {
    path: '/dynamic-manage/dynamicManage-create',
    name: '动态管理新建',
    component: () => import('../views/dynamicManage/dynamicManage-edit'),
    meta: {
      confirmLeave: false,
    },
  },
  {
    path: '/dynamic-manage/dynamicManage-edit',
    name: '动态管理编辑',
    component: () => import('../views/dynamicManage/dynamicManage-edit'),
    meta: {
      confirmLeave: false,
    },
  },
  {
    path: '/dynamic-manage/dynamicManage-preview',
    name: '动态管理预览',
    component: () => import('../views/dynamicManage/dynamic-preview'),
  },
  {
    path: '/',
    redirect: '/home',
  },
  // 操作日志
  {
    path: '/operation-log',
    name: '操作日志',
    component: () => import('../views/operationLog/list.vue'),
  },
  {
    path: '/operation-log/operationLog-detail',
    name: '操作日志详情',
    component: () => import('../views/operationLog/operationLog-detail.vue'),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

router.beforeEach((to, from, next) => {
  updateBeforeUnloadListener(to, from);
  if (!confirmBeforeLeavingPage(to, from, next)) return;

  if (to.meta.title) {
    document.title = to.meta.title as string;
  } else {
    document.title = store.state.layoutConfig.subtab || '';
  }
  //hack一把，解决刷新后url会带一串莫名其妙的东西的问题
  if (to.fullPath.search('&state') > -1 && to.fullPath.search(/\?/) === -1) {
    const path = to.fullPath.split('&')[0];
    next(path);
  } else {
    next();
  }
});

function initHandle(path, to, next) {
  store.dispatch('initSideMenu').then((authList: string[]) => {
    // next('/no-auth') 先跳转至无权限页面，后续可能放开 next()
    if (authList.includes(path)) {
      return next();
    }
    return to.path === '/no-auth' ? next() : next('/no-auth');
    // return to.path === next();
  });
}

// 为实现功能，先将这个路由守卫注释掉，以后再放开；
router.beforeEach((to, from, next) => {
  const path = to.path.split('/')[1];
  if (to.path === '/admin') {
    store.commit('UPDATE_SPINNING', false);
    if (getEnvConfig('SHOW_ACCOUNT_MANAGEMENT') === '1') {
      document.title = '九天人工智能平台';
      return next();
    } else {
      return next('/no-auth');
    }
  }
  const { spinning, sideMenuAuth } = store.state;
  if (!spinning) {
    // 是否初次访问页面
    // next('/no-auth') 先跳转至无权限页面，后续可能放开 next()
    if (sideMenuAuth.includes(path)) {
      return next();
    }
    return to.path === '/no-auth' ? next() : next('/no-auth');
  } else {
    // 初始化侧边栏处理
    initHandle(path, to, next);
  }
});
// 以后放开
router.beforeEach((to, from, next) => {
  const path = to.path.split('/').slice(-1)[0];
  if (path === 'userControl-edit' && !auths.edit) {
    return next('/no-auth');
  }
  next();
});

export default router;
