import { Modal } from 'ant-design-vue';

export const windowBeforeUnloadListener = (e) => {
  const msg = '这个提示语不会被显示';
  const event = e || window.event;

  // 兼容IE8和Firefox 4之前的版本
  if (event) {
    event.preventDefault();
    event.returnValue = msg;
  }

  // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
  return msg;
};

export const updateBeforeUnloadListener = (to, from) => {
  // 当路径相同时，不需要处理
  if (to.path === from.path) return;

  console.log(to.meta.confirmLeave ? '绑定 beforeunload' : '移除 beforeunload');
  window.onbeforeunload = to.meta.confirmLeave ? windowBeforeUnloadListener : null;
};

export const confirmBeforeLeavingPage = (to, from, next) => {
  // 当路径相同时，不需要处理
  if (to.path === from.path) return true;

  const shouldConfirmLeave = from.meta.confirmLeave && to.query.confirmLeave !== '1';

  if (!shouldConfirmLeave) return true;

  Modal.confirm({
    title: '确定离开当前页面吗？',
    content: '编辑内容将丢失，请谨慎操作',
    okText: '确定',
    cancelText: '取消',
    onOk: () => next(),
    onCancel: () => {
      window.onbeforeunload = windowBeforeUnloadListener;
    },
  });

  return false;
};
