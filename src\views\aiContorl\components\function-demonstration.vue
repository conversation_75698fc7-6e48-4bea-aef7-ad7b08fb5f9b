<!-- 功能演示相关素材组件 -->
<template>
  <div class="capacity-content-container function-demonstration-info">
    <div class="content-header">
      <div class="content-header-title">功能演示相关素材</div>
    </div>
    <div class="content-box">
      <div class="content-form-box">
        <div class="alert-box">
          <a-alert class="alert-bar" message="为能力提供相关的功能演示，该模块需能力提供方与平台侧线下沟通共同完成（后续提交上架申请后，相关人员会根据手机号联系您），如需功能演示模块，请先上传演示模块需要用到的素材，方便后期维护以及一键导出至公网环境。" type="info" show-icon />
        </div>
        <a-form :colon="false" :model="formState" :rules="rules" ref="formRef" :label-col="labelCol" :wrapper-col="wrapperCol">
          <div class="form-box">
            <div class="field-item">
              <a-form-item label="是否需要功能演示" name="needFunctionDemonstration" help="功能演示模块需与平台侧线下沟通共同完成，请联系****************************** 添加并开发功能演示，上架审核时将审核该演示功能。">
                <a-radio-group v-model:value="formState.needFunctionDemonstration" @change="handleNeedFunctionDemonstrationChange" :disabled="capacityCategorySelectDisabled">
                  <a-radio :value="NEED_FUNCTION_DEMONSTRATION_ENUM.NEED">是</a-radio>
                  <a-radio :value="NEED_FUNCTION_DEMONSTRATION_ENUM.NOT_NEED">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
            <div class="field-item select-box fix-position">
              <a-form-item label="上传类型素材" name="mediaType">
                <a-select :disabled="capacityCategorySelectDisabled || !formState.needFunctionDemonstration" @change="handleMediaTypeChange" v-model:value="formState.mediaType" placeholder="请选择" :options="mediaTypeOptions" :getPopupContainer="(triggerNode) => triggerNode.parentNode" />
              </a-form-item>
            </div>
            <div class="field-item upload-box">
              <a-form-item label="演示模块素材" name="fileListArr" v-show="formState.mediaType !== MEDIA_ENUM.TEXT" :help="typeTips">
                <file-uploader :isEdit="isEdit" :disabledControl="!formState.needFunctionDemonstration" class="file-uploader" :media-type-choose="formState.mediaType" :typeTips="typeTips" :defaultFileList="formState.fileListArr" :actionUrl="url" :acceptTypes="mediaAcceptTypes" :max="maxFileNum" :min="minFileNum" @onChange="handleFileListChange"></file-uploader>
              </a-form-item>
            </div>
            <div class="field-item upload-box" :help="typeTips">
              <a-form-item label="演示模块素材" name="fileText" v-show="formState.mediaType === MEDIA_ENUM.TEXT" :help="typeTips">
                <div id="editor-root-dom" :class="formState.needFunctionDemonstration ? 'editor-enable' : 'editor-disabled'"></div>
              </a-form-item>
            </div>
          </div>
        </a-form>
        <div class="btn-box">
          <div class="block-one"></div>
          <div class="block-two">
            <a-button class="pre-btn" @click="goToPreStep"><left-outlined :style="{ color: '#7F828F' }" /></a-button>
            <a-button class="next-btn" @click="goToNextStep" type="primary">下一步</a-button>
            <a-button class="save-btn" @click="toSaveDraft" type="primary" ghost>保存</a-button>
          </div>
        </div>
      </div>
      <div class="show-box">
        <div class="show-header-box">入驻能力页示意</div>
        <div class="show-img-box"><img :src="imageUrl" alt="示意图" /></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import { reactive, ref, defineExpose, defineComponent, toRefs, computed, watch } from 'vue';
import type { FormInstance } from 'ant-design-vue';
import { LeftOutlined } from '@ant-design/icons-vue';
import { useStore } from 'vuex';
import request, { requestBlob } from '@/request';
import fileUploader from './components/file-uploader.vue';
import wangEditor from 'wangeditor';
import { NEED_FUNCTION_DEMONSTRATION_ENUM, MEDIA_ENUM, getBase64, getVideoInfo, getVideoPosterInfo, MEDIA_TYPE_OPTIONS, getPicUrl, MEDIA_TYPE_MAP, CAPACITY_CATEGORY_ENUM } from './components/utils';

let editor = null as any; // 富文本显示器

const defaultAcceptType = '.jpg,.jpeg,.png,.bmp,.JPG,.JPEG,.PNG,.BMP';

export default defineComponent({
  components: { fileUploader, LeftOutlined },
  props: {
    isEdit: {
      // 是否为编辑和新建
      type: Boolean,
    },
    appInfo: {
      // 编辑的时候回填这个数据
      type: Object,
    },
    toNext: {
      // 下一步函数
      type: Function,
    },
    saveDraft: {
      // 保存草稿
      type: Function,
    },
    toPre: {
      // 上一步函数
      type: Function,
    },
  },
  emits: ['toNext', 'saveDraft', 'toPre'],
  setup(props: any, { emit }) {
    const formRef = ref<FormInstance>(); // 表单引用
    const { isEdit, appInfo } = toRefs(props);
    const isDraft = ref(true); // 当前草稿状态
    const store = useStore();

    // 生成媒体类型选项
    const formatMediaTypeOptions = (capacityType, mediaTypeOptionsOrigin) => {
      // 如果这个类型存在的话
      if (MEDIA_TYPE_MAP.get(capacityType)) {
        return mediaTypeOptionsOrigin.filter((item) => MEDIA_TYPE_MAP.get(capacityType)?.includes(item.value));
      }
      return mediaTypeOptionsOrigin;
    };
    // 初始媒体类型选项备份
    const mediaTypeOptionsOrigin = ref(MEDIA_TYPE_OPTIONS);

    const capacityType = computed(() => store.state.appInfo.baseInfo.type || CAPACITY_CATEGORY_ENUM.FACE_RECOFNITION); // 步骤一选择的能力类型
    // 媒体类型select的options
    const mediaTypeOptions = ref(formatMediaTypeOptions(capacityType.value, mediaTypeOptionsOrigin.value));

    const imageUrl = ref(''); // 修改能力类别的时候，更换右侧示意图
    const capacityCategorySelectDisabled = ref(false); // 能力类别禁止修改的同时，不能修改是否选择功能模块，不能选择媒体类型

    const getPriview = async (md5IdList: string[], mediaType) => {
      try {
        const newListIds = md5IdList.filter((item) => item) || [];
        if (!newListIds.length) {
          formState.fileListArr = [];
          return;
        }
        const requestList = newListIds?.map((item, index) =>
          requestBlob('/aiipweb/om-service/os/getObject', {
            method: 'GET',
            data: {
              object: item,
              category: '3',
            },
          }).then(async (res) => {
            if (mediaType === MEDIA_ENUM.IMG) {
              return { url: res, name: `file${index + 1}`, uid: index, status: 'done', thumbUrl: res, response: { body: item } };
            }
            if (mediaType === MEDIA_ENUM.VIDEO) {
              const videoInfo = await getVideoInfo(res);
              const obj = (await getVideoPosterInfo(videoInfo)) as any;
              return { url: res, name: `file${index + 1}`, uid: index, status: 'done', thumbUrl: obj?.posterUrl || (await getBase64(res)), response: { body: item } };
            }
            if (mediaType === MEDIA_ENUM.AUDIO) {
              const infoArr = item?.split('_') || [];
              return { url: res, name: infoArr?.[1] || `file${index + 1}`, uid: infoArr?.[0], status: 'done', response: { body: item } };
            }
          })
        );
        const defaultFileList = (await Promise.all(requestList)) as any;
        formState.fileListArr = defaultFileList;
      } catch (error) {
        console.log(error);
      }
    };

    // 表单数据
    let formState = reactive({
      needFunctionDemonstration: NEED_FUNCTION_DEMONSTRATION_ENUM.NEED, // 是否需要功能演示
      mediaType: mediaTypeOptions.value?.[0]?.value, // 上传类型素材
      fileListArr: [], // 演示素材，包括图片，视频与音频
      fileText: '', // 文本类
    });

    // 初始化表格数据
    const initFormStateData = async () => {
      try {
        if (!isEdit.value) {
          if (!appInfo?.value?.material?.type) {
            formState.mediaType = mediaTypeOptions.value?.[0]?.value; // 选择的类型根据第一个可以选择的选项设定
          }
          return;
        }
        const dataOrigin = appInfo.value?.material || {};
        formState.needFunctionDemonstration = dataOrigin?.needToShow;
        formState.mediaType = dataOrigin?.type || formatMediaTypeOptions(capacityType.value, mediaTypeOptionsOrigin.value)?.[0]?.value;
        formState.fileText = '';
        formState.fileListArr = [];
        if (formState.mediaType === MEDIA_ENUM.TEXT) {
          // 如果是文本，materials就是文本
          formState.fileText = dataOrigin.materials[0];
          editor?.txt?.html(dataOrigin.materials[0]); // 将本文填充
        } else {
          // 通过materials的id从接口中获取数据流并且生成url并且赋值
          formState.fileListArr = store.state.defaultMediaUrlList || [];
        }
      } catch (e) {
        console.log('初始化数据错误：', e);
      }
    };

    // 校验是否需要功能演示
    const validateNeedFunctionDemonstration = async (_rule: Rule, value: string) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      return Promise.resolve();
    };
    // 校验选择的媒体类型
    const validateMediaType = async (_rule: Rule, value: string) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      if (!value && typeof value !== 'number') {
        return Promise.reject('请选择');
      }
      return Promise.resolve();
    };

    // 校验输入的文本
    const validateFileText = async (rule, value, callback) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      if (formState.mediaType === MEDIA_ENUM.TEXT && formState.needFunctionDemonstration) {
        if (!value) {
          return Promise.reject('请输入');
        }
        const arr = value?.split('&') || [];
        if (value && value.length && arr?.length > 15) {
          return Promise.reject(getTypeTips());
        }
        const exitLongText = arr?.some((item) => item.length > 500);
        if (exitLongText) {
          return Promise.reject(getTypeTips());
        }
      }
      return Promise.resolve();
    };

    // 获取上传图片最大数量
    const getMaxMediaNum = () => {
      switch (formState.mediaType) {
        case MEDIA_ENUM.IMG: {
          return 3;
        }
        case MEDIA_ENUM.VIDEO: {
          return 3;
        }
        case MEDIA_ENUM.AUDIO: {
          return 6;
        }
        case MEDIA_ENUM.TEXT: {
          return 15;
        }
        default: {
          return 3;
        }
      }
    };

    // 获取上传图片最小数量
    const getMinMediaNum = () => {
      switch (formState.mediaType) {
        case MEDIA_ENUM.IMG: {
          return 3;
        }
        case MEDIA_ENUM.VIDEO: {
          return 3;
        }
        case MEDIA_ENUM.AUDIO: {
          return 3;
        }
        case MEDIA_ENUM.TEXT: {
          return 1;
        }
        default: {
          return 3;
        }
      }
    };

    // 上传文件
    const getTypeTips = () => {
      switch (formState.mediaType) {
        case MEDIA_ENUM.IMG: {
          return '请上传3张图片，支持PNG、JPG、JPEG、BMP格式，大小不得超过5M。图片比例要求16:9，且尺寸不小于宽800*高450px；必须上传3张图片。';
        }
        case MEDIA_ENUM.VIDEO: {
          return '请上传3个视频文件，支持mp4、avi、mov等格式，单个视频时长不超过5s，大小不超过200M。必须上传3个视频文件。';
        }
        case MEDIA_ENUM.TEXT: {
          return '请上传所有文本内容至输入框内，使用&分隔每条文本。单个文本字数不超过500字，最多允许上传15个文本文件。';
        }
        case MEDIA_ENUM.AUDIO: {
          return '请上传至少3个音频文件，支持mp3、wav、wma等格式，单个音频大小不超过10M，最多允许上传6个音频文件。';
        }
        default: {
          return '请上传至少3张图片，支持PNG、JPG、JPEG、BMP格式，大小不得超过5M。图片比例要求16:9，且尺寸不小于宽766*高403px；必须上传3张图片。';
        }
      }
    };

    // 校验上传的附件
    const validateFileListArr = async (rule, value, callback) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      if (formState.mediaType !== MEDIA_ENUM.TEXT && formState.needFunctionDemonstration) {
        if (!value?.length) {
          return Promise.reject(getTypeTips());
        }
        const exitErrorFile = value.some((item) => item.status === 'error' || item.status === 'removed' || item?.response?.body === '');
        if (exitErrorFile) {
          return Promise.reject(getTypeTips());
        }
        const successFile = value.filter((item) => item.status === 'done');
        if (successFile.length < getMinMediaNum()) {
          return Promise.reject(getTypeTips());
        }
      }
      return Promise.resolve();
    };

    // 表单的校验规则
    const rules: Record<string, Rule[]> = {
      needFunctionDemonstration: [{ required: true, validator: validateNeedFunctionDemonstration, trigger: 'change' }],
      mediaType: [{ required: true, validator: validateMediaType, trigger: ['change'] }],
      fileListArr: [{ required: true, validator: validateFileListArr, trigger: ['change'] }],
      fileText: [{ required: true, validator: validateFileText, trigger: ['change'] }],
    };

    // 素材类型有变化的时候
    const handleMediaTypeChange = (value) => {
      formState.mediaType = value;
      // 需要清空素材
      formState.fileListArr = [];
      formState.fileText = '';
    };

    // 文件列表有变化的时候
    const handleFileListChange = (fileList) => {
      formState.fileListArr = fileList;
    };

    // 是否需要功能演示选项变化的时候
    const handleNeedFunctionDemonstrationChange = (e) => {
      formState.mediaType = mediaTypeOptions.value?.[0].value;
      formState.fileListArr = [];
      formState.fileText = '';
      formState.needFunctionDemonstration = e.target.value;
      changeEditorDisabled(e.target.value);
    };

    // 获取枚举类型工具类
    const getOptionsUtil = async (label) => {
      try {
        const res = await request('/aiipweb/om-service/dict/getDict', { method: 'GET', data: { dictName: label } });
        if (res.state === 'OK') {
          // 根据选择的能力类型来处理选项
          mediaTypeOptionsOrigin.value = res.body;
          mediaTypeOptions.value = formatMediaTypeOptions(capacityType.value, res.body);
        }
      } catch (e) {
        console.log(e);
      }
    };

    const formatParams = () => {
      const params = {
        needToShow: formState.needFunctionDemonstration,
        type: formState.mediaType,
        materials: [],
      } as any;
      if (formState.mediaType === MEDIA_ENUM.TEXT) {
        const arr: any = [];
        if (formState.fileText) {
          arr.push(formState.fileText);
        }
        params.materials = arr || [];
      } else {
        if (formState?.fileListArr?.length) {
          params.materials = formState.fileListArr?.filter((item: any) => item?.status === 'done' && item?.response?.body).map((item: any) => item?.response?.body || '');
        }
      }
      return params;
    };

    // 将数据同步到store中
    const updateToStore = () => {
      const params = formatParams();
      store.commit('UPDATE_APPINFO_METERIAL', params);
    };

    // 跳转到下一步
    const goToNextStep = () => {
      isDraft.value = false; // 打开校验
      formRef.value?.validate().then((value) => {
        updateToStore();
        emit('toNext');
      });
    };
    const toSaveDraft = () => {
      isDraft.value = true; // 关闭校验
      updateToStore();
      emit('saveDraft');
    };
    const goToPreStep = () => {
      updateToStore();
      emit('toPre');
    };
    // 获取图片
    const getImageUrl = async () => {
      imageUrl.value = await getPicUrl(capacityType.value, 'step2.png');
    };
    watch(
      () => store.state.defaultMediaUrlList,
      (newValue) => {
        formState.fileListArr = newValue || [];
      }
    );

    // 控制编辑器禁用以及置灰
    const changeEditorDisabled = (value) => {
      if (value === false) {
        editor?.disable();
      } else {
        editor?.enable();
      }
    };

    const typeTips = computed(() => {
      return getTypeTips();
    });

    const mediaAcceptTypes = computed(() => {
      switch (formState.mediaType) {
        case MEDIA_ENUM.IMG: {
          return defaultAcceptType;
        }
        case MEDIA_ENUM.VIDEO: {
          return '.wmv,.asf,.asx,.rm,.rmvb,.mpg,.mpeg,.mpe,.mov,.mp4,.m4v,.avi,.dat,.mkv,.flv,.vob';
        }
        case MEDIA_ENUM.AUDIO: {
          return '.mp3,.wav,.wma';
        }
        case MEDIA_ENUM.TEXT: {
          return '';
        }
        default: {
          return defaultAcceptType;
        }
      }
    });
    const maxFileNum = computed(() => {
      return getMaxMediaNum();
    });
    const minFileNum = computed(() => {
      return getMinMediaNum();
    });
    const url = computed(() => {
      return './aiipweb/om-service/os/upload';
    });
    getOptionsUtil('materialCategory');
    getImageUrl();
    defineExpose({
      updateToStore,
    });
    return {
      formRef,
      formState,
      rules,
      handleMediaTypeChange,
      labelCol: { span: 5 },
      wrapperCol: { span: 16 },
      handleFileListChange,
      handleNeedFunctionDemonstrationChange,
      goToNextStep,
      toSaveDraft,
      goToPreStep,
      getMaxMediaNum,
      getMinMediaNum,
      getTypeTips,
      mediaTypeOptions,
      mediaTypeOptionsOrigin,
      MEDIA_ENUM,
      NEED_FUNCTION_DEMONSTRATION_ENUM,
      updateToStore,
      initFormStateData,
      getImageUrl,
      capacityType,
      formatMediaTypeOptions,
      getPriview,
      imageUrl,
      capacityCategorySelectDisabled,
      changeEditorDisabled,
      typeTips,
      mediaAcceptTypes,
      maxFileNum,
      minFileNum,
      url,
    };
  },
  mounted() {
    this.initWangEditor();
    this.initFormStateData(); // 需要等到编辑器挂载之后
    this.capacityCategorySelectDisabled = this.appInfo?.abilityId ? true : false;
    this.changeEditorDisabled(this.formState.needFunctionDemonstration);
  },
  watch: {
    capacityType: {
      handler: function (newVal, oldValue) {
        if (newVal !== oldValue) {
          const mediaTypeOptionsNew = this.formatMediaTypeOptions(newVal, this.mediaTypeOptionsOrigin); // 更换下拉选项
          this.mediaTypeOptions = mediaTypeOptionsNew; // 更新下拉选项
          this.formState.mediaType = mediaTypeOptionsNew?.[0]?.value; // 更新选中类型
          this.formState.fileListArr = []; // 清空选择的材料
          this.formState.fileText = ''; // 清空文本
          this.getImageUrl(); // 更换图片链接
        }
      },
      immediate: true,
    },
  },
  methods: {
    initWangEditor() {
      let self = this as any;
      try {
        editor = new wangEditor('#editor-root-dom');
        editor.config.menus = [
          'head', // 标题
          'bold', // 粗体
          'fontSize', // 字号
          'fontName', // 字体
          'italic', // 斜体
          // "underline", // 下划线
          // "strikeThrough", // 删除线
          'foreColor', // 文字颜色
          'backColor', // 背景颜色
          'link', // 插入链接
          // "list", // 列表
          'justify', // 对齐方式
          // "quote", // 引用
          // "emoticon", // 表情
          // "image", // 插入图片
          // "table", // 表格
          // "code", // 插入代码
          'undo', // 撤销
          'redo', // 重复
        ];
        editor.config.onchange = function (html) {
          const txt = editor.txt.text();
          self.formState.fileText = txt == ' ' ? '' : txt;
          self.formRef?.validateFields('fileText'); // 需要手动触发一次校验
        };
        editor.config.uploadImgHooks = {
          customInsert: function (insertImgFn: any, result: any) {
            insertImgFn(result.body);
          },
        };
        editor.config.zIndex = 99;
        editor.create();
      } catch (e) {
        console.log('初始化编辑器错误', e);
      }
    },
    // 销毁编辑器
    destoryWangEditor() {
      if (!editor) {
        return;
      }
      editor.destroy();
    },
  },
  // computed: {
  //   typeTips() {
  //     return this.getTypeTips();
  //   },
  //   mediaAcceptTypes() {
  //     switch (this.formState.mediaType) {
  //       case MEDIA_ENUM.IMG: {
  //         return defaultAcceptType;
  //       }
  //       case MEDIA_ENUM.VIDEO: {
  //         return '.wmv,.asf,.asx,.rm,.rmvb,.mpg,.mpeg,.mpe,.mov,.mp4,.m4v,.avi,.dat,.mkv,.flv,.vob';
  //       }
  //       case MEDIA_ENUM.AUDIO: {
  //         return '.mp3,.wav,.wma';
  //       }
  //       case MEDIA_ENUM.TEXT: {
  //         return '';
  //       }
  //       default: {
  //         return defaultAcceptType;
  //       }
  //     }
  //   },
  //   maxFileNum() {
  //     return this.getMaxMediaNum();
  //   },
  //   minFileNum() {
  //     return this.getMinMediaNum();
  //   },
  //   url() {
  //     return './aiipweb/om-service/os/upload';
  //   },
  // },
});
</script>

<style lang="less" scoped>
.capacity-content-container {
  box-sizing: border-box;
  width: 100%;
  background-color: #fff;
  margin-top: 20px;
  padding: 20px;
}
// 头部标题
.content-header {
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16px;
  .content-header-title {
    font-size: 16px;
    font-weight: 500;
    color: #121f2c;
    line-height: 24px;
  }
}
.content-box {
  box-sizing: border-box;
  display: flex;
  height: 100%;
  .content-form-box {
    // background-color: red;
    flex: 0 0 70.4%;
    padding-right: 20px;
    border-right: 2px solid #efefef;
    .alert-bar {
      padding: 8px 9px 8px 17px;
      align-items: flex-start;
      /deep/ .ant-alert-message {
        font-size: 12px;
        color: #666666;
      }
      /deep/ .ant-alert-icon {
        margin-top: 3px;
      }
    }
    .form-box {
      margin-top: 14px;
      .field-item {
        margin-bottom: 32px;
      }
      .select-box {
        /deep/ .ant-form-item-control-input {
          width: 327px;
        }
      }
      /deep/ .ant-form-item-explain {
        font-size: 12px;
        color: #a0a6ab;
      }

      /deep/ .ant-form-item-extra {
        font-size: 12px;
        color: #a0a6ab;
      }
      .fix-position {
        margin-top: -8px;
      }
    }
    .btn-box {
      margin-top: 50px;
      display: flex;
      margin-bottom: 200px;
      .block-one {
        flex: 0 0 20.83333333%;
        width: 20.83333333%;
      }
      .pre-btn {
        width: 32px;
        margin-right: 8px;
        .anticon-left {
          transform: translateX(-7px);
        }
      }
      .next-btn {
        width: 120px;
        margin-right: 8px;
      }
      .save-btn {
        width: 88px;
      }
    }
  }
  .show-box {
    box-sizing: border-box;
    padding-left: 20px;
    width: 100%;
    .show-header-box {
      font-weight: 500;
      color: #121f2c;
      height: 24px;
      display: flex;
      align-items: center;
      margin-bottom: 16px;
    }
    .show-header-box::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: #0082ff;
      margin-right: 12px;
    }
    .show-img-box {
      width: 100%;
      height: 100%;
      img {
        width: 100%;
      }
    }
  }
  #editor-root-dom {
    box-sizing: border-box;
    width: 586px;
  }
  .editor-disabled {
    /deep/ .w-e-text-container {
      background-color: #f5f5f5;
    }
  }
  .editor-enable {
    /deep/ .w-e-text-container {
      background-color: #fff;
    }
  }
}
</style>
