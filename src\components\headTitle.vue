<template>
  <h3 class="jt-head-title">{{ title }}</h3>
</template>
<!--
  标题效果组件（前有一个label横杠）
  <head-title :title="这是一个标题"/>
 -->
<script>
import { defineComponent } from 'vue';
export default defineComponent({
  name: 'HeadTitle',
  props: {
    title: {
      type: String,
      required: true,
    },
  },
});
</script>

<style lang="less" scoped>
@import '@/assets/styles/index.less';

.jt-head-title {
  color: @jt-title-color;
  // font-size: @jt-font-size-lger;
  position: relative;
  margin-left: 12px;
  font-size: 14px;
  font-weight: 500;
  line-height: 24px;
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background-color: @jt-primary-color;
    position: absolute;
    left: -12px;
    top: 0;
    bottom: 0;
    margin: auto;
  }
}
</style>
