<template>
  <a-modal class="pwd-form-dialog" width="536px" :maskClosable="false" :visible="visible" title="修改密码" @ok="handleOk" @cancel="$emit('cancel')">
    <div class="tab-container">
      <a-tabs v-model:activeKey="type" v-if="bothLoginMethod">
        <a-tab-pane v-if="phoneNumEditable && bothLoginMethod" tab="手机号验证" key="phone"> </a-tab-pane>
        <a-tab-pane key="password" tab="旧密码验证" force-render> </a-tab-pane>
      </a-tabs>
    </div>
    <a-form key="password-form" v-if="type === 'password'" :colon="false" class="form-content" ref="passwordRuleForm" :model="passwordForm" :rules="passwordRules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="旧密码" name="oldJtpw">
        <a-input-password placeholder="请输入旧密码" autocomplete="new-password" v-model:value="passwordForm.oldJtpw" />
      </a-form-item>
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="新密码" name="newJtpw">
        <a-input-password placeholder="请输入新密码" autocomplete="new-password" v-model:value="passwordForm.newJtpw" />
      </a-form-item>
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="确认密码" name="confirmJtpw">
        <a-input-password placeholder="请再次输入新密码" autocomplete="new-password" v-model:value="passwordForm.confirmJtpw" />
      </a-form-item>
    </a-form>
    <a-form key="phone-form" v-if="type === 'phone'" :colon="false" class="form-content" ref="phoneRuleForm" :model="phoneForm" :rules="phoneRules" :label-col="labelCol" :wrapper-col="wrapperCol">
      <a-form-item label="手机号" name="phoneNum">
        <p>{{ hidePhone || '--' }}</p>
      </a-form-item>
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="验证码" name="code">
        <a-input class="code-input" placeholder="请输入验证码" autocomplete="new-password" v-model:value="phoneForm.code" />
        <a-button style="width: 115px" class="code-button" :class="timmer > 0 ? 'code-button-timmer' : 'code-button-normal'" :disabled="timmer > 0" @click="handleSendCode" type="primary" ghost>{{ timmer > 0 ? `重新获取 (${timmer})` : `${sended ? '重新获取' : '获取验证码'}` }}</a-button>
      </a-form-item>
      <!-- 这里是为了避免chrome自动填入密码，所以增加一个隐藏的input -->
      <!-- <input type="password" style="display: none; width: 0; height: 0" /> -->
      <a-form-item label="新密码" name="newJtpw" help="8-20个字符，必须包含大、小写字母和数字">
        <a-input-password name="pwd" placeholder="请输入新密码" autocomplete="new-password" v-model:value="phoneForm.newJtpw" />
      </a-form-item>
      <a-form-item label="确认密码" name="confirmJtpw">
        <a-input-password placeholder="请再次输入新密码" autocomplete="new-password" v-model:value="phoneForm.confirmJtpw" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script>
import { phoneHider, jtpwRegex } from '../utils';
import { userSendCode } from '../api/sms';
import { encryptStr } from '../utils/encrypt';
import { getEnvConfig } from '@/config';

const phoneNumEditable = getEnvConfig('USER_INFO_PHONE_EDITABLE') === '1';

export default {
  props: { phoneNum: String, visible: Boolean, jtPK: String },
  emits: ['ok', 'cancel'],
  data() {
    return {
      labelCol: { span: 5 },
      wrapperCol: { span: 19 },
      type: 'phone',
      passwordForm: {
        oldJtpw: '',
        newJtpw: '',
        confirmJtpw: '',
      },
      phoneForm: {
        // phoneNum: this.phoneNum,
        newJtpw: '',
        confirmJtpw: '',
        code: '',
      },
      passwordRules: {
        oldJtpw: [{ required: true, message: '请输入', trigger: ['change', 'blur'] }],
        newJtpw: [
          { required: true, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          { pattern: jtpwRegex, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          {
            validator: (rule, value) => {
              if (value) {
                if (window.zxcvbn(value).score >= 2) {
                  return Promise.resolve();
                } else {
                  return Promise.reject('密码过于简单或存在安全风险，请修改');
                }
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        confirmJtpw: [{ required: true, validator: this.passwordPwdCheck, message: '两次密码输入不一致', trigger: ['blur', 'change'] }],
      },
      phoneRules: {
        code: [{ required: true, message: '请输入验证码' }],
        newJtpw: [
          { required: true, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          { pattern: jtpwRegex, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          {
            validator: (rule, value) => {
              if (value) {
                if (window.zxcvbn(value).score >= 2) {
                  return Promise.resolve();
                } else {
                  return Promise.reject('密码过于简单或存在安全风险，请修改');
                }
              }
            },
            trigger: ['change', 'blur'],
          },
        ],
        confirmJtpw: [{ required: true, validator: this.phonePwdCheck, message: '两次密码输入不一致', trigger: ['blur', 'change'] }],
      },
      timmer: 0,
      sending: false,
      sended: false,
      phoneNumEditable,
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.$refs[`passwordRuleForm`] && this.$refs[`passwordRuleForm`].resetFields();
        this.$refs[`phoneRuleForm`] && this.$refs[`phoneRuleForm`].resetFields();
        this.timmer = 0;
        this.sending = false;
        this.sended = false;
      }
    },
    type() {
      this.$refs[`passwordRuleForm`] && this.$refs[`passwordRuleForm`].resetFields();
      this.$refs[`phoneRuleForm`] && this.$refs[`phoneRuleForm`].resetFields();
    },
    'passwordForm.newJtpw'() {
      this.$refs[`passwordRuleForm`].validateField('confirmJtpw');
    },
    'phoneForm.newJtpw'() {
      this.$refs[`phoneRuleForm`].validateField('confirmJtpw');
    },
  },
  computed: {
    hidePhone() {
      return phoneHider(this.phoneNum);
    },
    onlyPwdMethod() {
      return getEnvConfig('USER_LOGIN_OPTION') === 'password';
    },
    bothLoginMethod() {
      return getEnvConfig('USER_LOGIN_OPTION').includes('smscode') && getEnvConfig('USER_LOGIN_OPTION').includes('password');
    },
  },
  methods: {
    passwordPwdCheck(rule, value) {
      if (value === this.passwordForm.newJtpw) {
        return Promise.resolve();
      } else {
        return Promise.reject('新密码和旧密码不一致');
      }
    },
    phonePwdCheck(rule, value) {
      if (value === this.phoneForm.newJtpw) {
        return Promise.resolve();
      } else {
        return Promise.reject('新密码和旧密码不一致');
      }
    },
    handleOk() {
      this.$refs[`${this.type}RuleForm`].validate().then((valid) => {
        if (valid) {
          const form = this[`${this.type}Form`];
          const encryptForm = { ...form, newPassword: encryptStr(form.newJtpw, this.jtPK), oldPassword: form.oldJtpw && encryptStr(form.oldJtpw, this.jtPK) };
          this.$emit('ok', { ...encryptForm, passwordType: this.type });
        } else {
          return false;
        }
      });
    },
    handleSendCode() {
      if (!this.phoneNum) {
        this.$notification.error({
          message: '提示',
          description: '请绑定手机号后再试',
        });
        return;
      }
      userSendCode().then((res) => {
        if (!res.errorCode) {
          this.sended = true;
          this.timmer = 60;
          this.timmerDecrease();
          this.$message.success('发送成功');
        } else {
          this.$message.error(res.errorMessage || '发送失败');
        }
      });
    },
    timmerDecrease() {
      if (this.timmer > 0) {
        this.timmer--;
        setTimeout(() => {
          this.timmerDecrease();
        }, 1000);
      }
    },
  },
  mounted() {
    if (this.onlyPwdMethod) {
      this.type = 'password';
    } else if (phoneNumEditable) {
      this.type = 'phone';
    }
  },
};
</script>
<style lang="less">
.pwd-form-dialog {
  .ant-modal-header {
    padding: 0 0 0 20px;
    .ant-modal-title {
      height: 49px;
      line-height: 49px;
    }
  }
  .ant-modal-body {
    padding: 20px 0;
  }
  .ant-form-item-explain,
  .ant-form-item-explain-error {
    font-size: 12px;
  }
  .ant-modal-footer {
    height: 64px;
    padding: 16px 31px;
  }
  .ant-tabs-nav .ant-tabs-tab {
    padding: 12px 64px;
  }
  .form-content {
    padding: 0 66px;
  }
  .tab-container {
    display: flex;
    margin-bottom: 24px;
    justify-content: center;
    /deep/ .ant-radio-button-wrapper {
      width: 120px;
      text-align: center;
    }
  }
  .code-input {
    width: 197px;
    margin-right: 7px;
  }
  .code-button-timmer {
    background: #cbcfd2 !important;
    color: #ffffff !important;
    &:hover {
      background: #cbcfd2 !important;
    }
  }
}
</style>
