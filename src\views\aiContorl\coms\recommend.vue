<template>
  <div class="recomd">
    <div class="recomd-wrap">
      <!-- 标题 -->
      <div class="title">{{ title }}</div>
      <!-- 列表 -->
      <div class="recomd-ul">
        <div class="recomd-item" v-for="(item, index) in list" @click="route(item)" :key="index">
          <!-- <a-popover v-if="item.disabled"> -->
          <!-- <template slot="content">
              <p>暂未开放</p>
            </template> -->
          <!-- <div class="recomd-title">{{ item.name }}</div>
            <div class="content">{{ item.intro }}</div>
            <img class="img" :src="item.img" alt="" />
          </a-popover>
          <template v-else> -->
          <div class="recomd-title">{{ item.name || recommendListName }}</div>
          <div class="content">{{ item.description || recommendListDescription }}</div>
          <img class="img" :src="item.img || recommendListImg" alt="" />
          <!-- </template> -->
        </div>
      </div>
    </div>

    <!-- 底部样式 -->
    <div class="bottomTop">
      <div class="bottomTop-img">
        <div class="bottomTop-box">
          <div class="bottomTopLeft">
            <p>更多能力尽在“九天”人工智能平台</p>
            <div class="button" @click="jump"><p>立即体验</p></div>
          </div>
          <div class="bottomTopRight">
            <div>
              <p>320+项</p>
              <span>核心能力</span>
            </div>
            <div>
              <p>670+亿次</p>
              <span>AI能力累计调用</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'recomdList',
  props: {
    //  标题相关推荐
    title: {
      type: String,
      default: '',
    },
    // 展示项数组
    list: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      recommendListImg: require('@/assets/images/preview/<EMAIL>'),
      recommendListName: '能力名称',
      recommendListDescription: '这是一段简介这是一段简介这是一段简介这是一段简介这是一段简介这是一段简介这是一段简介这是一段简介这是一段简介',
    };
  },
  methods: {
    route(item) {
      console.log(item.name);
      if (item.disabled) return;
      this.$router.push({ name: item.name });
    },

    jump() {
      // this.$router.push({
      //   path: '/home',
      // });
    },
  },
};
</script>
<style lang="less" scoped>
.recomd {
  width: 100%;
  .recomd-wrap {
    width: 100%;
    height: 310px;
    padding: 40px 0 56px 0;
    min-width: 1320px;
    background: url('../../../assets/images/preview/<EMAIL>') no-repeat;
    background-size: 1920px auto;
    background-position: center center 0;
    @media screen and (min-width: 1024px) {
      opacity: 1;
      transform: translate(0);
    }
    @media screen and (min-width: 1920px) {
      background-size: cover;
    }
    .title {
      color: #121f2c;
      text-align: center;
      font-size: 28px;
      font-weight: 600;
    }
    .recomd-ul {
      // max-width: 1200px;
      width: 1180px;
      margin: 0 auto;
      display: flex;
      margin-top: 24px;
      justify-content: space-between;
    }
    .recomd-item {
      padding: 24px;
      cursor: pointer;
      width: 380px;
      height: 150px;
      border-radius: 2px;
      background-color: #fff;
      position: relative;
      border-top-left-radius: 2px;
      border-top-right-radius: 2px;
      border-top: 4px solid;
      border-image: linear-gradient(74deg, #1defc2 0%, #00c3ea 100%) 1;
      box-shadow: 0px 4px 15px 0px rgba(25, 42, 44, 0.08);
      &:not(:first-child) {
        margin-left: 2%;
      }
      &:hover {
        background: linear-gradient(180deg, #ecfcff 0%, #ffffff 100%);
        box-shadow: 0px 4px 15px 0px rgba(25, 42, 44, 0.08);
        .recomd-title {
          color: #00b3cc;
        }
      }
      .recomd-title {
        font-size: 16px;
        color: #121f2c;
        line-height: 24px;
        font-weight: 600;
      }
      .content {
        color: #606972;
        line-height: 22px;
        font-size: 14px;
        margin-top: 12px;
        max-width: 320px;
        position: relative;
        z-index: 999;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3; /*这里设置几行*/
        overflow: hidden;
      }
      .img {
        width: 96px;
        position: absolute;
        right: 16px;
        bottom: 16px;
      }
    }
  }
  .bottomTop {
    min-width: 1320px;
    height: 160px;
    background: linear-gradient(270deg, #00a9d0 0%, #00d1d1 100%);
    background: url('../../../assets/images/preview/left.png') no-repeat left top / 465px 160px, url('../../../assets/images/preview/right.png') no-repeat right top / 900px 160px, linear-gradient(to right, #00a9d0, #00d1d1);
    .bottomTop-img {
      padding-top: 40px;
      width: 1320px;
      margin: 0 auto;
      height: 100%;

      display: flex;
      justify-content: space-between;
      .bottomTop-box {
        display: flex;
        justify-content: space-between;
        margin: 0 auto;
        width: 1200px;
        .bottomTopLeft {
          p:nth-child(1) {
            height: 33px;
            font-size: 24px;
            font-weight: 500;
            color: #ffffff;
            line-height: 33px;
            margin: 0px 0 16px 0;
          }
          .button {
            width: 165px;
            height: 40px;
            border-radius: 2px;
            border: 1px solid #ffffff;
            cursor: pointer;
            &:hover {
              width: 165px;
              height: 40px;
              background: #4cd6de;
              cursor: pointer;
            }
            p {
              width: 64px;
              height: 40px;
              font-size: 16px;
              font-weight: 500;
              color: #ffffff;
              line-height: 40px;
              margin: 0 auto;
              cursor: pointer;
            }
          }
        }

        .bottomTopRight {
          display: flex;
          div:nth-child(1) {
            margin-right: 120px;
          }
          div {
            p {
              height: 50px;
              font-size: 36px;
              font-weight: 600;
              color: #ffffff;
              line-height: 50px;
              margin-bottom: 4px;
            }

            span {
              height: 22px;
              font-size: 16px;
              font-weight: 400;
              color: #ffffff;
              line-height: 22px;
            }
          }
        }
      }
    }
  }
}
</style>
