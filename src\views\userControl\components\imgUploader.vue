<template>
  <div class="container">
    <div class="viewer">
      <img :src="wrapedValue || defaultUrl" alt="" />
    </div>
    <a-upload
      :headers="{
        Authorization: 'Bearer ' + refreshToken,
      }"
      name="file"
      :multiple="false"
      :accept="acceptTypes"
      action="./object/web/storage/image/upload"
      :beforeUpload="beforeUpload"
      @remove="handleRemove"
      @change="onChange"
    >
      <a-button style="color: #606972"> <upload-outlined /> 更换头像 </a-button>
    </a-upload>
    <p class="ant-upload-hint1">支持jpg、jpeg、gif、png、bmp格式，大小不超过10MB，建议使用正方形图片</p>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import defaultUrl from '@/assets/images/avatar_big.png';
import { mapState } from 'vuex';
export default defineComponent({
  components: {
    UploadOutlined,
  },
  props: {
    value: String,
    required: Boolean,
    acceptTypes: {
      required: false,
      type: String,
      default: '.jpg,.jpeg,.gif,.png,.bmp,.JPG,.JPEG,.GIF,.PNG,.BMP',
    },
  },
  emits: ['update:value'],
  data() {
    return {
      defaultUrl,
    };
  },
  computed: {
    ...mapState(['refreshToken']),
    wrapedValue() {
      return this.value ? `./${this.value}` : '';
    },
  },
  methods: {
    handleRemove() {
      this.$emit('change', '');
    },
    onChange(info) {
      const file = info.file;
      if (file.status === 'done') {
        if (file.response && file.response.state === 'OK') {
          this.$message.success(`文件上传成功`);
          this.$emit('update:value', file.response.body.url);
        } else {
          this.$message.error(`文件上传失败:${file.response.body?.message || '未知错误'}`);
        }
      } else if (file.status === 'error') {
        this.$message.error(`文件上传失败`);
      }
    },
    beforeUpload(file) {
      if (!this.checkFileType(this.acceptTypes, file.name)) {
        this.$notification.error({
          message: '错误',
          description: '上传文件格式错误',
        });
        return false;
      }
    },
    checkFileType(types, fileName) {
      return types.split(',').includes('.' + fileName.split('.')[1]);
    },
  },
});
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.ant-upload-hint1 {
  width: 219px;
  margin-top: 9px;
  font-size: 12px;
  font-weight: 400;
  color: #a0a6ab;
  line-height: initial;
  text-align: center;
}
.viewer {
  margin-bottom: 24px;
  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
  }
}
</style>
