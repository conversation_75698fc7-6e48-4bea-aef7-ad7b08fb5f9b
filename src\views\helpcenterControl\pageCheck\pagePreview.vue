<template>
  <div class="user-control-container">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '页面审核' }"> 页面审核 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>查看页面</a-breadcrumb-item>
    </a-breadcrumb>
    <Container>
      <container-item>
        <div class="top-bar">
          <head-title :title="'页面详情'" />
          <div class="page-state">
            <a-space size="large">
              <a-button v-if="pageInfo.approveStatus == 0" @click="passPage" type="primary">通过</a-button>
              <a-button v-if="pageInfo.approveStatus == 0" @click="rejectPage" danger>驳回</a-button>
              <a-tag v-if="pageInfo.approveStatus == 2" color="blue">已通过</a-tag>
              <a-tag v-if="pageInfo.approveStatus == 1" color="red">已驳回</a-tag>
            </a-space>
          </div>
        </div>
        <a-row>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">页面标题：</a-col>
              <a-col :span="colSpan.content" class="row-content">{{ checkData(pageInfo.pageName) }}</a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">提交审核时间：</a-col>
              <a-col :span="colSpan.content" class="row-content">{{ checkData(pageInfo.submitTime) }}</a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">父目录：</a-col>
              <a-col v-if="pageInfo.parents" :span="colSpan.content" class="row-content">
                <a-tooltip v-if="pageInfo.parents[0]">
                  <template #title v-if="pageInfo.parents[0].length > 10">
                    <span>{{ pageInfo.parents[0] }}</span>
                  </template>
                  <span>{{ pageInfo.parents[0].length > 10 ? pageInfo.parents[0].substring(0, 10) + '...' : pageInfo.parents[0] }}</span>
                </a-tooltip>
                <a-tooltip v-if="pageInfo.parents[1]">
                  <template #title v-if="pageInfo.parents[1].length > 10">
                    <span>{{ pageInfo.parents[1] }}</span>
                  </template>
                  <span style="color: #0082ff"> - </span>
                  <span>{{ pageInfo.parents[1].length > 10 ? pageInfo.parents[1].substring(0, 10) + '...' : pageInfo.parents[1] }}</span>
                </a-tooltip>
                <span v-if="pageInfo.parents.length == 0"> - </span>
              </a-col>
              <a-col v-else :span="colSpan.content" class="row-content"> - </a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">提交人：</a-col>
              <a-col :span="colSpan.content" class="row-content">{{ checkData(pageInfo.submitUserName) }}</a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">平台：</a-col>
              <a-col :span="colSpan.content" class="row-content" v-if="pageInfo.accessPlatformName">
                <a-tag color="blue">{{ pageInfo.accessPlatformName }}</a-tag>
              </a-col>
            </a-row>
          </a-col>
        </a-row>
      </container-item>
      <container-item>
        <head-title :title="'内容预览'" />
        <div id="document-container">
          <document-viewer :documentItem="documentItem" :titles="titles" />
        </div>
      </container-item>
    </Container>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { message } from 'ant-design-vue';
import HeadTitle from '@/components/headTitle.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import documentViewer from '@/components/documentViewer.vue';
import { HelpCenter } from '@/apis';

const { saiDetail, saiApprove, saiOverruled } = HelpCenter;
export default defineComponent({
  components: { HeadTitle, Container, ContainerItem, documentViewer },
  data() {
    return {
      pageId: '',
      pageInfo: {} as any,
      colSpan: {
        title: 6,
        content: 16,
      },
      titles: [],
      documentItem: {
        pageName: '',
        updateTime: '',
        pageContent: '',
      },
    };
  },
  created(this: any) {
    this.pageId = this.$route.query.pageId;
    if (!this.pageId) {
      this.router.push('/no-auth');
      return;
    }
    this.getpageInfo();
  },
  computed: {},
  methods: {
    checkData(val) {
      return val ? val : '-';
    },
    async getpageInfo() {
      await saiDetail({ name: 'HELP', data: { approveId: this.pageId } }).then((res: any) => {
        if (res.state === 'OK') {
          let { pageName, titles, accessPlatformName, updateTime, pageContent } = res.body.pageVo;
          let { id, approveStatus, submitTime, submitUserName } = res.body.sourceApproveInfo;
          this.pageInfo = { id, pageName, parents: res.body.parents, accessPlatformName, approveStatus, submitTime, submitUserName };
          this.titles = titles;
          this.documentItem.pageName = pageName;
          this.documentItem.updateTime = updateTime;
          this.documentItem.pageContent = pageContent;
        }
      });
    },
    async passPage() {
      await saiApprove({ name: 'HELP', data: { approveId: this.pageInfo.id } }).then((res: any) => {
        if (res.state === 'OK') {
          message.success('已通过');
          this.getpageInfo();
        }
      });
    },
    async rejectPage() {
      await saiOverruled({ name: 'HELP', data: { approveId: this.pageInfo.id } }).then((res: any) => {
        if (res.state === 'OK') {
          message.success('已驳回');
          this.getpageInfo();
        }
      });
    },
  },
});
</script>
<style lang="less" scoped>
#document-container {
  overflow: auto;
  height: calc(100vh - 160px);
  &::-webkit-scrollbar {
    display: none;
  }
}
.nav {
  height: 58px;
  line-height: 58px;
  padding-left: 20px;
  background: #ffffff;
  font-weight: 500;
  color: #121f2c;
}
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .page-state {
    span {
      width: 70px;
      height: 32px;
      line-height: 30px;
      text-align: center;
      border-radius: 5px;
      font-size: 14px;
    }
  }
}
.ant-row {
  padding-bottom: 16px;
  .row-title {
    text-align: right;
    color: #606972;
  }
  .row-content {
    padding-left: 8px;
    color: #121f2c;
  }
}
</style>
