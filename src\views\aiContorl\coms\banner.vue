<template>
  <div class="wrapper" :style="`background:url(${props.backGroundTypeVal}) no-repeat center center / 2060px 440px #e1ebed;`">
    <div class="warpper-top">
      <div class="warpper-center">{{ props.details.title || '能力名称' }}</div>
      <div class="warpper-bottom" v-html="props.details.content || defaultVal"></div>
      <div class="warpper-footer">
        <button @click="immediateUse('#demo')">立即使用</button>
        <button @click="technicalDocument">技术文档</button>
      </div>
      <div class="fill">
        <img src="../../../assets/images/preview/fire.png" alt="" />
        <p>能力正式上线，限时免费体验！</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { object, string } from 'vue-types';
const props = defineProps({
  details: {
    type: Object,
    default: () => {},
  },
  backGroundTypeVal: {
    type: String,
    default: '',
  },
  sourceName: {
    type: String,
    default: '',
  },
});
const defaultVal = ref('<span style="color:#121F2C">请输入</span><span style="color:#A0A6AB">这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介这是一段能力简介</span>');
const immediateUse = (val) => {
  document.querySelector(val).scrollIntoView({
    behavior: 'smooth',
    block: 'start',
  });
};
const router = useRouter();
const technicalDocument = () => {
  let routeUrl = router.resolve({ path: '/capacity-management/documentsPreview', query: { res: props.sourceName } });
  const a = document.createElement('a');
  const event = new MouseEvent('click');
  console.log(routeUrl.href);
  a.href = routeUrl.href;
  a.target = '_blank';
  a.rel = 'noopener noreferrer';
  a.dispatchEvent(event);
};
</script>

<style scoped lang="less">
.wrapper {
  width: 100%;
  min-width: 1320px;
  height: 420px;
  padding-top: 116px;
  position: relative;
  .warpimg {
    width: 280px;
    height: 340px;
    position: absolute;
    right: 0;
    bottom: 0;
  }
  .warpper-top {
    position: absolute;
    max-width: 1180px;
    left: (50%);
    margin-left: -590px;
    .warpper-center {
      margin: 0px 0 14px 0px;
      height: 62px;
      font-size: 44px;
      font-weight: 600;
      color: #121f2c;
      line-height: 62px;
    }
    .warpper-bottom {
      width: 640px;
      font-size: 16px;
      font-weight: 400;
      color: #121f2c;
      line-height: 26px;
      margin-bottom: 24px;
      word-wrap: break-word;
    }
    .warpper-footer {
      font-size: 16px;
      button:nth-child(1) {
        width: 120px;
        height: 40px;
        box-shadow: 0px 10px 12px 0px rgba(1, 126, 149, 0.2);
        background: linear-gradient(90deg, #04e6ec 0%, #0096de 100%);
        border-radius: 2px;
        margin-right: 12px;
        border: none;
        color: #ffffff;
        &:hover {
          cursor: pointer;
          box-shadow: 0px 10px 12px 0px rgba(1, 126, 149, 0.3);
        }
      }
      button:nth-child(2) {
        width: 120px;
        height: 40px;
        background: #ffffff;
        box-shadow: 0px 10px 12px 0px rgba(1, 126, 149, 0.15);
        border-radius: 2px;
        margin-right: 24px;
        border: none;
        color: #00b3cc;
        &:hover {
          cursor: pointer;
          box-shadow: 0px 10px 12px 0px rgba(1, 126, 149, 0.2);
        }
      }
    }
    .fill {
      margin-top: 24px;
      display: flex;
      img {
        width: 20px;
        height: 20px;
      }
      p {
        margin-left: 8px;
        color: #ff6300;
        line-height: 20px;
        font-size: 14px;
      }
    }
  }
}
</style>
