<template>
  <div class="user-control-container">
    <sub-header title="组织管理"></sub-header>
    <Container>
      <container-item>
        <div class="top-bar">
          <p class="top-title">组织列表</p>
          <a-space>
            <div>
              <a-input placeholder="组织名称" allow-clear v-model:value="search.searchKey" style="width: 180px;">
                <template #prefix>
                  <jt-icon type="iconsousuo" style="font-size: 15px" />
                </template>
              </a-input>
            </div>
            <div class="select-group">
              <a-select show-search :getPopupContainer="getPopupContainer" allowClear :filter-option="filterOption" class="group-filter" placeholder="所属一级组织" v-model:value="firstGroup" :options="firstGroupList" :field-names="{ label: 'groupName', value: 'groupId' }"></a-select>
              <a-select show-search :getPopupContainer="getPopupContainer" allowClear :filter-option="filterOption" class="group-filter" placeholder="所属二级组织" v-model:value="secondGroup" :options="secondGroupList" :field-names="{ label: 'groupName', value: 'groupId' }"></a-select>
              <a-select show-search :getPopupContainer="getPopupContainer" allowClear :filter-option="filterOption" class="group-filter" placeholder="所属三级组织" v-model:value="thirdGroup" :options="thirdGroupList" :field-names="{ label: 'groupName', value: 'groupId' }"></a-select>
            </div>
            <a-space>
              <div>
                <a-button-group>
                  <a-button v-if="showBatchOutport" :disabled="isExport" style="font-size: 12px;" @click="handleBatchOutport">
                    <template #icon><jt-icon type="iconfenxiang" /></template>
                    导出
                  </a-button>
                </a-button-group>
              </div>
              <a-button v-if="showCreate" @click="handleCreate" style="font-size: 12px;" type="primary">
                <template #icon><PlusOutlined /></template>
                新建组织
              </a-button>
            </a-space>
          </a-space>
        </div>
        <a-table :loading="tableAttr(loading).loading" :getPopupContainer="getPopupContainer" :columns="columns" :data-source="dataSource" :customHeaderRow="customHeaderRow" :rowClassName="() => 'cus-row'" :customRow="customRow" :pagination="false" @change="handleChange">
          <template #emptyText>
            <empty v-if="!loading" title="组织" :showNoDataText="showNoDataText(search)">
              <template #description>
                请立即
                <a href="javascript:;" @click="handleCreate">新建组织</a>
              </template>
            </empty>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'groupName'">
              <a-tooltip :title="record.groupName">
                <div class="overflow-ellipsis groupName">
                  <!-- <span class="user-name-text" @click="goToDetail(record)"> -->
                  <span class="user-name-text">
                    {{ record.groupName || '--' }}
                  </span>
                </div>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'firstGroup'">
              <a-tooltip :title="`${record.firstGroup}`">
                <div class="overflow-ellipsis groupName">
                  <span>
                    {{ record.firstGroup || '--' }}
                  </span>
                </div>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'secondGroup'">
              <a-tooltip :title="record.secondGroup">
                <div class="overflow-ellipsis groupName">
                  <span>
                    {{ record.secondGroup || '--' }}
                  </span>
                </div>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'thirdGroup'">
              <a-tooltip :title="record.thirdGroup">
                <div class="overflow-ellipsis groupName">
                  <span>
                    {{ record.thirdGroup || '--' }}
                  </span>
                </div>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'groupId'">
              <div class="btn-group">
                <a-space :size="8">
                  <a-button :disabled="!showCreate" size="large" type="link" style="padding-left: 0;font-size: 12px;" @click.stop="edit(record)">
                    <span><jt-icon type="iconbianji" class="icon" style="padding-right: 5px;font-size: 13px;"></jt-icon>编辑</span>
                  </a-button>
                  <a-button :disabled="record.userCount !== 0 || !showCreate" size="large" type="link" style="padding-left: 0;font-size: 12px;" @click.stop="delColumn(record)">
                    <span><jt-icon type="iconshanchu1" class="icon" style="padding-right: 5px;font-size: 13px;"></jt-icon>删除</span>
                  </a-button>
                </a-space>
              </div>
            </template>
          </template>
        </a-table>
        <Pagination :total="pagination.total" :pageNum="pagination.pageNum" :pageSize="pagination.pageSize" :pageSizeOptions="[10, 20, 50, 100]" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
      </container-item>
    </Container>
    <create-org :visible="createModalOpen" :firstGroupList="firstGroupList" @cancelModal="cancelModal"></create-org>
    <del-modal :visible="delModalOpen" :del-org="delOrg" @cancelModal="cancelModal"></del-modal>
    <edit-modal :visible="editModalOpen" :detail="detailInfo" @cancelModal="cancelModal"></edit-modal>
  </div>
</template>

<script lang="ts" setup>
import { defineComponent, reactive, ref, nextTick, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import { debounce } from 'lodash';
import { tableAttr, showNoDataText } from '@/utils';
import request from '@/request';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import { checkKeycloakAttr } from '@/utils/auth';
import { downloadFile } from '@/utils/file';
import empty from './empty.vue';
import CreateOrg from './create-org.vue';
import DelModal from './components/del-modal.vue';
import EditModal from './components/edit-modal.vue';
const keycloakAuths = {
  batch_outport: checkKeycloakAttr('jtc-group-export'),
  create: checkKeycloakAttr('jtc-group-manage'),
  edit: checkKeycloakAttr('jtc-group-manage'),
};

const showCreate = computed(() => {
  return keycloakAuths.create;
});

const showBatchOutport = computed(() => {
  return keycloakAuths.batch_outport;
});

const router = useRouter();
const route = useRoute();

const search = reactive({
  groupId: route.query?.groupId || '',
  searchKey: route.query?.searchKey || '',
});
const pagination = reactive({
  pageSize: route.query?.pageSize ? Number(route.query.pageSize) : 10,
  pageNum: route.query?.pageNum ? Number(route.query.pageNum) : 1,
  total: 0,
});

const baseColumns = ref<any>([
  {
    title: '组织名称',
    dataIndex: 'groupName',
    key: 'groupName',
    width: 120,
  },
  {
    title: '所属一级组织',
    dataIndex: 'firstGroup',
    key: 'firstGroup',
    width: 120,
  },
  {
    title: '所属二级组织',
    dataIndex: 'secondGroup',
    key: 'secondGroup',
    width: 120,
  },
  {
    title: '所属三级组织',
    dataIndex: 'thirdGroup',
    key: 'thirdGroup',
    width: 120,
  },
  {
    title: '子组织数',
    dataIndex: 'subGroupCount',
    key: 'subGroupCount',
    width: 120,
  },
  {
    title: '用户数',
    dataIndex: 'userCount',
    key: 'userCount',
    width: 120,
  },
  {
    title: '活跃用户数',
    dataIndex: 'activeUser',
    key: 'activeUser',
    sorter: true,
    width: 120,
    customRender: ({ text }) => {
      return typeof(text) == 'number' ? text : '--';
    },
  },
]);

const actionColumn = [
  {
    title: '操作',
    dataIndex: 'groupId',
    key: 'groupId',
    width: 120,
  },
];

const columns = ref<any>([]);
const dataSource = ref<any[]>([]);
const loading = ref<any>(true);
const sortField = ref<any>(null);
const isAsc = ref<any>(null);
const isExport = ref<any>(false);
// 获取组织列表
const getTableList = () => {
  loading.value = true;
  const obj = {
    searchKey: search.searchKey,
    sortField: sortField.value,
    isAsc: isAsc.value,
    groupId: search.groupId,
    pageSize: pagination.pageSize,
    pageNum: pagination.pageNum,
  };
  request('/web/admin/um/v1/group/list', {
    method: 'POST',
    data: obj,
  }).then((res: any) => {
    if (res.code === 0) {
      dataSource.value = res.data?.data;
      pagination.total = res.data?.total;
      if (dataSource.value.length === 0) {
        isExport.value = true;
      } else {
        isExport.value = false;
      }
    } else {
      isExport.value = true;
    }
    loading.value = false;
  });
  router.push({ query: {} });
};

const changePageNum = (pageNum) => {
  pagination.pageNum = pageNum;
  getTableList();
};
const changePageSize = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.pageNum = 1;
  getTableList();
};

const handleChange = (pagination, filters, sorter) => {
  if (Object.keys(sorter).length !== 0) {
    const order = sorter.order;
    // isAsc.value = order === 'ascend' ? true : false;
    isAsc.value = order ? (order === 'descend' ? false : true) : null;
    sortField.value = order ? 'activeUser' : null;
  }
  pagination.pageNum = 1;
  getTableList();
};

const firstGroup = ref<any>(route.query?.firstGroup || null);
const secondGroup = ref<any>(null);
const thirdGroup = ref<any>(null);
const firstGroupList = ref<any[]>([]);
const secondGroupList = ref<any[]>([]);
const thirdGroupList = ref<any[]>([]);
// 获取组织树
const getGroupList = async (value) => {
  let resData = [];
  const res = await request('/web/admin/um/v1/group/list-tree', {
    method: 'GET',
    data: { groupId: value || null },
  });
  if (res.code === 0) {
    resData = res.data;
  } else {
    resData = [];
  }
  return resData;
};

watch(
  () => firstGroup.value,
  async (val) => {
    if (val) {
      search.groupId = val;
      secondGroupList.value = await getGroupList(val);
      if (route.query.secondGroup) {
        secondGroup.value = route.query.secondGroup;
      } else {
        secondGroup.value = null;
      }
    } else {
      search.groupId = '';
      secondGroupList.value = [];
      secondGroup.value = null;
    }
    // thirdGroup.value = null;
    // thirdGroupList.value = [];
  },
  {
    deep: true,
    immediate: true,
  }
);
watch(
  () => secondGroup.value,
  async (val) => {
    if (val) {
      search.groupId = val;
      thirdGroupList.value = await getGroupList(val);
      if (route.query.thirdGroup) {
        thirdGroup.value = route.query.thirdGroup;
      } else {
        thirdGroup.value = null;
      }
    } else {
      search.groupId = firstGroup.value || '';
      thirdGroupList.value = [];
      thirdGroup.value = null;
    }
  }
);
watch(
  () => thirdGroup.value,
  (val) => {
    if (val) {
      search.groupId = val;
    } else {
      search.groupId = secondGroup.value || firstGroup.value || '';
    }
  }
);

const debounceGetData = debounce(getTableList, 500);

watch(
  () => keycloakAuths.create,
  (val) => {
    if (val) {
      columns.value = [...baseColumns.value, ...actionColumn];
    } else {
      columns.value = [...baseColumns.value];
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

watch(search,
  (val) => {
    pagination.pageNum = route.query?.pageNum || 1;
    debounceGetData();
  }, {
    deep: true,
    immediate: true,
  }
);

const goToDetail = (record) => {
  router.push({
    path: '/organization-management/detail',
    query: {
      id: record.groupId,
      searchKey: search.searchKey,
      firstGroup: firstGroup.value,
      secondGroup: secondGroup.value,
      thirdGroup: thirdGroup.value,
      pageNum: pagination.pageNum,
      pageSize: pagination.pageSize,
    },
  });
};

// 导出
const handleBatchOutport = debounce(() => {
  // message.success('正在导出，请稍后...');
  const url = `/web/admin/um/v1/group/export?groupId=${search.groupId}&searchKey=${search.searchKey}`;
  downloadFile({ url })?.catch(() => {
    message.error(`导出失败，请重试`);
  });
}, 500);

const createModalOpen = ref<any>(false);
// 新建组织
const handleCreate = () => {
  createModalOpen.value = true;
};
// 关闭
const cancelModal = async (value) => {
  createModalOpen.value = false;
  delModalOpen.value = false;
  editModalOpen.value = false;
  if (value) {
    if (value === 'create') {
      if (search.groupId === '') {
        pagination.pageNum = 1;
        getTableList();
      }
      firstGroupList.value = await getGroupList();
      firstGroup.value = null;
      secondGroup.value = null;
      thirdGroup.value = null;
      search.groupId = '';
    } else {
      getTableList();
      handleOrgNameChange();
    }
  }
};

// 处理修改、删除组织更新组织树信息
const handleOrgNameChange = async () => {
  if (search.groupId) {
    if (thirdGroup.value) return;
    if (secondGroup.value) {
      thirdGroupList.value = await getGroupList(search.groupId);
    } else {
      secondGroupList.value = await getGroupList(search.groupId);
    }
  } else {
    firstGroupList.value = await getGroupList();
  }
};

const editModalOpen = ref<any>(false);
const detailInfo = ref<any>({});
const edit = (record) => {
  detailInfo.value = record;
  editModalOpen.value = true;
};

const delModalOpen = ref<any>(false);
const delOrg = ref<any>({});
// 删除
const delColumn = (record) => {
  delOrg.value = record;
  delModalOpen.value = true;
};

const customRow = (record) => {
  return {
    onClick: () => {
      goToDetail(record);
    },
  };
};

const customHeaderRow = () => {
  return {
    style: {
      fontsize: '12px',
    },
  };
};

const getPopupContainer = (node) => {
  if (node && dataSource.value.length > 5) {
    return node.parentNode;
  }
  return document.body;
};

const filterOption = (input, option) => {
  return option.groupName.indexOf(input) >= 0;
};

onMounted(async () => {
  firstGroupList.value = await getGroupList();
});
</script>

<style lang="less" scoped>
.user-control-container {
  #container-wrap {
    height: calc(100vh - 108px);
  }
}
.top-bar {
  padding: 16px 0;
  padding-top: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .top-title {
    font-weight: 500;
    font-size: 16px;
    color: #121f2c;
  }
}
.groupPath {
  display: block;
  max-width: 200px;
}
.groupName {
  display: block;
  max-width: 200px;
}
.btn-group {
  font-size: 12px;
  color: #0082ff;
}

.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}

.group-filter {
  width: 144px;
  font-size: 12px;
}

.pagination {
  padding: 16px 0 48px;
}

:deep .ant-input {
  font-size: 12px;
}
:deep .ant-table-content .ant-table-thead tr th {
  background: rgba(237, 241, 243, 0.65);
  padding: 10px 16px;
  color: #121f2c;
}
:deep .ant-table-tbody > tr > td {
  padding: 0 16px;
  height: 40px;
  line-height: 40px;
  color: #606972;
}
:deep .ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}
:deep .ant-table-filter-column {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    width: 48px;
    flex: 0;
  }
}
:deep .ant-table-column-sorter {
  color: #121f2c;
}
:deep .ant-table-column-sorters:hover .ant-table-column-sorter {
  color: #121f2c;
}
:deep .ant-table-row:hover td {
  background: #f8f9fa !important;
}
:deep(.cus-row) {
  cursor: pointer;
  font-size: 12px;
  .user-name-text {
    &:hover {
      color: @primary-color;
    }
  }
}
:deep .ant-table-wrapper:not(.auto-height-table) .ant-spin-nested-loading {
  min-height: 50px;
}
:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;
  th {
    background-color: #f6f9fc;
  }
}
</style>
