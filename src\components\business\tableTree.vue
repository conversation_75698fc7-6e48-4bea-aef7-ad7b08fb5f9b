<template>
  <div class="help-manage-body">
    <a-table :loading="tableAttr(loading).loading" :locale="tableAttr(loading).locale" :columns="columns" :data-source="dataSource" rowKey="id" :expandedRowKeys="expandRowKeys" @expand="handleExpand" :pagination="false">
      <template #emptyText v-if="!loading">
        <empty title="页面" :showNoDataText="true">
          <template #description v-if="showEmptyDescription">
            请立即
            <a href="javascript:;" @click="emit('createDirectory')">新建目录</a>
            或
            <a href="javascript:;" @click="emit('editPage')">新建页面</a>
          </template>
        </empty>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'name'">
          <a-space>
            <span class="sort-btns">
              <jt-icon type="icondown" :class="{ 'disable-btn': !record.downArrow }" class="sort-btn" @click="() => handleSort(record, 'down', record.downArrow)"></jt-icon>
              <jt-icon type="iconup" :class="{ 'disable-btn': !record.upArrow }" class="sort-btn" @click="() => handleSort(record, 'up', record.upArrow)"></jt-icon>
            </span>
            <span>{{ record.name }}</span>
            <FolderOutlined v-if="record.type === 'catalog'" />
          </a-space>
        </template>
        <template v-if="column.key === 'state'">
          <div class="publish-status" :class="record.status == '1' ? 'published' : record.status == '0' ? 'unpublished' : 'check-pending'">{{ record.status == '1' ? '已发布' : record.status == '0' ? '未发布' : '待审核' }}</div>
        </template>
        <template v-if="column.key === 'publishTime'">
          <span>{{ record.publishTime || '-' }}</span>
        </template>
        <template v-if="column.key === 'updateTime'">
          <span>{{ record.updateTime || '-' }}</span>
        </template>
        <template v-if="column.key === 'id'">
          <a-space size="middle" class="operation">
            <template v-if="type === 'HELP'">
              <a-button :disabled="record.status == 2" @click="() => handleEdit(record)" type="link">编辑</a-button>
              <a-tooltip placement="top">
                <template #title v-if="record.children && record.children.length > 0 && record.status != 2">
                  <span>该目录已有子目录/子页面，请全部删除后再试</span>
                </template>
                <a-button :disabled="(record.children && record.children.length > 0) || record.status == 2" @click="() => handleDelete(record)" type="link">删除</a-button>
              </a-tooltip>
              <a-button v-if="record.status == 0 && (!record.pid || (record.parentHasPublished && record.level == 2) || (record.parentHasPublished && record.level === 3 && record.grandparentHasPublished))" @click="() => handlePublish(record)" type="link">发布</a-button>
              <a-button v-else-if="(!record.pid && record.status == 1) || (record.level === 2 && record.status == 1) || (record.level === 3 && record.status == 1) || (record.level === 3 && record.status == 0 && record.parentHasPublished && !record.grandparentHasPublished)" disabled type="link">发布</a-button>
              <a-tooltip v-else>
                <template #title v-if="record.status != 2">
                  <span>请将父目录“{{ record.parentName }}”发布后再试</span>
                </template>
                <a-button disabled type="link">发布</a-button>
              </a-tooltip>
            </template>
            <a-button :disabled="record.type === 'catalog'" type="link">
              <router-link target="_blank" :to="{ path: preiewUrl[type], query: { pageId: record.id } }">预览</router-link>
            </a-button>
          </a-space>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, createVNode, onUnmounted, watch } from 'vue';
import { FolderOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { HelpCenter } from '@/apis';
import { tableAttr } from '@/utils';
import empty from '@/components/empty.vue';
const { deleteItem, publishItem, sortItem } = HelpCenter;

const preiewUrl = {
  HELP: '/platform-document/helpcenterControl-pagePreview',
  SKILL: '/skill-document/preview',
};

export default defineComponent({
  components: {
    FolderOutlined,
    empty,
  },
  props: ['platformCode', 'columns', 'dataSource', 'expandKeys', 'type', 'loading', 'showEmptyDescription'],
  emits: ['editDirectory', 'refreshData'],
  setup(props, { emit }) {
    const currentCatalogId = ref(undefined);
    const expandRowKeys: any[] = reactive(props.expandKeys);
    watch(
      () => props.dataSource,
      () => {
        // if (expandcatalogId) {
        //   const parentId = findParentIdById(expandcatalogId);
        //   parentId && expandRowKeys.push(parentId);
        //   expandRowKeys.push(expandcatalogId);
        // }
      },
      { deep: true }
    );
    // 点击展开/折叠图标状态管理，这里要在每次数据刷新时保持展开状态不变
    const handleExpand = (expanded, record) => {
      let index = expandRowKeys.indexOf(record.id);
      if (expanded) {
        index === -1 && expandRowKeys.push(record.id);
      } else {
        expandRowKeys.splice(index, 1);
      }
    };
    // 编辑目录或页面
    const handleEdit = (record) => {
      emit('editDirectory', record);
    };

    // 删除目录或页面
    const handleDelete = (record) => {
      if (!record.children || record.children.length == 0) {
        Modal.confirm({
          title: () => `确定删除${record.type === 'page' ? '页面' : '目录'}"${record.name}"吗？`,
          icon: () => createVNode(ExclamationCircleOutlined),
          content: () => createVNode('span', {}, '删除后无法恢复，请谨慎操作'),
          cancelText: () => '取消',
          okText: () => '删除',
          okType: 'danger',
          onOk() {
            const reqData = {};
            reqData[`${record.type}Id`] = record.id;
            deleteItem({ record, name: props.type, data: reqData }).then((res: any) => {
              if (res.state === 'OK') {
                emit('refreshData');
                message.success(`${record.type === 'page' ? '页面' : '目录'}"${record.name}"删除成功`);
              }
            });
          },
        });
      }
    };

    // 发布目录或页面
    const handlePublish = async (record) => {
      const reqData = {};
      reqData[`${record.type}Id`] = record.id;
      const res = (await publishItem({ record, name: props.type, data: reqData })) as any;
      if (res.state === 'OK') {
        emit('refreshData');
        message.success(`${record.type === 'page' ? '页面' : '目录'}"${record.name}"发布成功`);
      }
    };

    // 向上或下排序，返回排序的record的key数组
    const handleSort = async (record, type: 'up' | 'down', bol: boolean) => {
      if (!bol) return;
      const data = findParentRecordById(record.id);
      const keys = data.map((item) => item.id);
      const currentIndex = keys.indexOf(record.id);
      const targetIndex = type === 'up' ? currentIndex - 1 : currentIndex + 1;
      keys.splice(targetIndex, 0, keys.splice(currentIndex, 1)[0]);
      const reqData = keys.map((key, index) => {
        return { id: key, sort: index };
      });
      await sortItem({ name: props.type, type: 'POST', data: reqData }).then((res: any) => {
        if (res.state === 'OK') {
          message.success(`${record.type === 'page' ? '页面' : '目录'}"${record.name}"修改排序成功`);
          emit('refreshData');
        }
      });
    };

    // 根据id查询同级别的record
    const findParentRecordById = (id) => {
      const dataList = props.dataSource;
      return recursionFind(dataList);
      // 递归寻找当前key的所有兄弟record
      function recursionFind(data) {
        for (let i = 0; i < data.length; i++) {
          if (data[i].id === id) {
            return data;
          }
          if (data[i].children && data[i].children.length > 0) {
            const findList = recursionFind(data[i].children);
            if (findList) {
              return findList;
            }
          }
        }
      }
    };

    return {
      emit,
      expandRowKeys,
      currentCatalogId,
      preiewUrl,
      handleEdit,
      handleDelete,
      handlePublish,
      handleSort,
      handleExpand,
      tableAttr,
    };
  },
});
</script>

<style lang="less" scoped>
.help-manage-body {
  .table-empty {
    color: #606972;
    font-size: 18px;
    font-weight: bold;
    position: relative;
    top: -120px;
  }
  .publish-status {
    height: 20px;
    line-height: 18px;
    padding: 0 8px;
    border-radius: 2px;
    font-size: 12px;
    display: inline-block;
  }
  .published {
    border: 1px solid #17bb85;
    color: #17bb85;
  }
  .unpublished {
    border: 1px solid #a0a6ab;
    color: #606972;
  }
  .check-pending {
    border: 1px solid #cf1322;
    color: #cf1322;
  }
  .operation {
    button {
      font-size: 12px;
      padding: 0;
    }
  }
}
.sort-btns {
  // display: flex;
  .sort-btn {
    cursor: pointer;
    transition: color 0.6s;
    color: #aaacb4;
    &.disable-btn {
      visibility: hidden;
    }
    &:hover {
      color: #0082ff;
    }
  }
}
:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;
  th {
    background-color: #f6f9fc;
  }
}
/deep/.ant-table-row {
  font-size: 12px;
}
/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}
/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}
</style>
