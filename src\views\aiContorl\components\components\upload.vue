<template>
  <div>
    <loading v-if="showGlobalLoading"></loading>
    <a-upload v-model:file-list="fileList" name="file" action="#" :show-upload-list="false" @change="handleChangeEdior">
      <a-button>
        <upload-outlined></upload-outlined>
        上传文件
      </a-button>
    </a-upload>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits } from 'vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import { UploadChangeParam } from 'ant-design-vue';
import requestBlob from '@/request';
import { debounce } from 'lodash';
import { message } from 'ant-design-vue';
import loading from '../Loading.vue';
const emit = defineEmits(['callBackMethod']);
const showGlobalLoading = ref(false);
async function handleChange(info: UploadChangeParam) {
  showGlobalLoading.value = true;
  const blob: any = await readAsBlob(info.file.originFileObj);
  const infoFileSize: any = info.file.size;
  const fileSize = infoFileSize / 1024 / 1024 < 50;
  const fileName = info.file.name.substring(info.file.name.lastIndexOf('.') + 1);
  const extension = fileName === 'doc' || fileName === 'docx';
  if (!fileSize) {
    message.error('上传文件大小不能超过50M');
    showGlobalLoading.value = false;
    return;
  }
  if (!extension) {
    message.error('上传的文件类型应为doc、docx');
    showGlobalLoading.value = false;
    return;
  }
  const suffix = info.file.name.substring(info.file.name.lastIndexOf('.') + 1);
  const formData = new FormData();
  formData.append('file', blob);
  formData.append('suffix', suffix);
  requestBlob('/aiipweb/om-service/manage/word2Html', {
    method: 'POST',
    data: formData,
    headers: { 'Content-Type': 'multipart/form-data' },
  }).then((res) => {
    showGlobalLoading.value = false;
    emit('callBackMethod', res);
  });
}
const handleChangeEdior = debounce(handleChange, 1000);
/**
 * 根据文件获取blob
 * @param {file} file 文件
 */
function readAsBlob(file) {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(file);
    reader.onload = function () {
      resolve(new Blob([reader.result]));
    };
  });
}
const fileList = ref([]);
</script>

<style lang="scss" scoped></style>
