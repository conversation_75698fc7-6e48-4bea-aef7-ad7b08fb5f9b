<template>
  <a-breadcrumb class="nav">
    <a-breadcrumb-item>
      <router-link :to="{ name: '能力文档管理' }"> 能力文档管理 </router-link>
    </a-breadcrumb-item>
    <a-breadcrumb-item>预览页面</a-breadcrumb-item>
  </a-breadcrumb>
  <Container>
    <container-item>
      <div id="document-container">
        <document-viewer :documentItem="documentItem" :titles="titles" />
      </div>
    </container-item>
  </Container>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref } from 'vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import documentViewer from '@/components/documentViewer.vue';
import { useRouter, useRoute } from 'vue-router';
import { HelpCenter } from '@/apis';
const { getDetail } = HelpCenter;

export default defineComponent({
  components: {
    Container,
    ContainerItem,
    documentViewer,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const pageId = route.query.pageId;
    if (!pageId) {
      router.push('/no-auth');
      return;
    }
    const titles = ref([]);
    const documentItem = ref({
      pageName: '',
      updateTime: '',
      pageContent: '',
    });
    const getPageDataById = async (pageId) => {
      await getDetail({ name: 'SKILL', data: { pageId } }).then((res: any) => {
        if (res.state === 'OK') {
          titles.value = res.body.titles;
          const { pageName, updateTime, pageContent } = res.body;
          documentItem.value = { pageName, updateTime, pageContent };
        }
      });
    };

    onMounted(() => {
      getPageDataById(pageId);
    });

    return { documentItem, titles };
  },
});
</script>

<style lang="less" scoped>
.nav {
  height: 58px;
  line-height: 58px;
  padding-left: 20px;
  background: #ffffff;
  font-weight: 500;
  color: #121f2c;
}
#document-container {
  overflow: auto;
  height: calc(100vh - 190px);
  &::-webkit-scrollbar {
    display: none;
  }
}
</style>
