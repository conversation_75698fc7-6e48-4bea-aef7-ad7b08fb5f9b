export default {
  methods: {
    // table查询参数发生变化时，重置url的query参数
    rewriteUrlParamsMixin(params) {
      this.$router.replace({ query: { ...this.$route.query, ...params } });
    },
    // 页面初始化需要根据url的query参数初始化data查询参数（发生在数据初始化查询之前）
    reWriteDataByUrlParamsMixin() {
      const queryParams = this.$route.query;
      Object.keys(queryParams).forEach((key) => {
        if (this.pagination && (key === 'pageNum' || key === 'pageSize')) {
          this.pagination[key] = queryParams[key];
        } else if (this.selectFilter && ['firstGroup', 'secondGroup', 'thirdGroup', 'fourthGroup'].includes(key)) {
          this.selectFilter[key] = queryParams[key];
        } else {
          const keycloakParams = ['state', 'session_state', 'code'];
          if (!keycloakParams.includes(key)) {
            this[key] = queryParams[key];
          }
        }
      });
    },
    // 根据column的dataIndex和order字段重置排序
    changeOrderColumn(dataIndex, order) {
      this.columns.forEach((column) => {
        if (column.sorter) {
          if (column.dataIndex === dataIndex) {
            column.sortOrder = order;
          } else {
            column.sortOrder = false;
          }
        }
      });
    },
    // 初始化根据url的query参数设置table的column数据
    initSorterColumn() {
      const queryParams = this.$route.query;
      if (queryParams.orderColumn) {
        const order = queryParams.isAsc === 'true' ? 'ascend' : queryParams.isAsc === 'false' ? 'descend' : false;
        this.changeOrderColumn(queryParams.orderColumn, order);
      }
    },
  },
  // 在created生命周期中根据url的query参数进行数据的初始化，在组件中mounted中请求数据
  created() {
    this.initSorterColumn();
  },
};
