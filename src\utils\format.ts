export function formattedNumber(num: number | string): string {
  if (num === null || num === undefined) {
    return '-';
  }
  const str = num?.toString();
  const reg = str.indexOf('.') > -1 ? /(\d)(?=(\d{3})+\.)/g : /(\d)(?=(?:\d{3})+$)/g;
  return str.replace(reg, '$1,');
}

interface FormattedNumberCNOptions {
  getUnits?: number; // 返回的内容的类型 0正常模式（数字+单位） 1只返回处理后的数字 2只返回处理后的单位
}
/**
 * 将数字转换为带中文单位的字符串
 * @param num :number 需要转换的数字
 * @param param1 :FormattedNumberCNOptions 转换中可配置的选项
 * @returns 转换结果
 */
export function formattedNumberCN(num: number, { getUnits = 0 }: FormattedNumberCNOptions = {}): string | number {
  let result: string | number = 0;
  let units = '';
  if (num < 1000) {
    result = num;
  } else if (num < 100000 && num > 1000) {
    result = formattedNumber(num);
  } else {
    result = formattedNumber((num / 10000).toFixed(1));
    units = '万';
  }
  switch (getUnits) {
    case 1:
      return result;
    case 2:
      return units;
    default:
      return result + units;
  }
}

/***
 * 乘法，获取精确乘法的结果值
 * @param arg1
 * @param arg2
 * @returns
 */
function accMul(arg1, arg2) {
  let m = 0;
  let s1 = '';
  let s2 = '';
  try {
    s1 = arg1.toString();
    s2 = arg2.toString();
  } catch (e) {
    s1 = '0';
    s2 = '0';
  }
  try {
    m += s1.split('.')[1].length;
  } catch (e) {
    // console.warn(e);
  }
  try {
    m += s2.split('.')[1].length;
  } catch (e) {
    // console.warn(e);
  }
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
}
/**
 * 获取对应值的带单位的额度
 * @returns 转换后的储存量 string
 * ps:先将获取到的数据统一转换为字节（b），然后再从字节层面统一向上计算
 */
export function getUsageResult(data: number | string, toFixedLength = 1, { getUnits = 0 }: FormattedNumberCNOptions = {}): string | number {
  const dataSize: number = +data;
  let size: number | string = 0;
  let sizeTxt: number | string = '';
  if (dataSize / 1024 < 1) {
    size = Math.ceil(dataSize * 100) / 100;
    size = size.toFixed(toFixedLength);
    sizeTxt = 'B';
  } else if (dataSize / 1024 / 1024 < 1) {
    size = Math.ceil((dataSize / 1024) * 100) / 100;
    size = size.toFixed(toFixedLength);
    sizeTxt = 'K';
  } else if (dataSize / 1024 / 1024 / 1024 < 1) {
    size = Math.ceil((dataSize / 1024 / 1024) * 100) / 100;
    size = size.toFixed(toFixedLength);
    sizeTxt = 'M';
  } else if (dataSize / 1024 / 1024 / 1024 / 1024 < 1) {
    size = Math.ceil((dataSize / 1024 / 1024 / 1024) * 100) / 100;
    size = size.toFixed(toFixedLength);
    sizeTxt = 'G';
  } else {
    size = Math.ceil((dataSize / 1024 / 1024 / 1024 / 1024) * 100) / 100;
    size = size.toFixed(toFixedLength);
    sizeTxt = 'T';
  }
  switch (getUnits) {
    case 1:
      return size;
    case 2:
      return sizeTxt;
    default:
      return size + sizeTxt;
  }
}
/**
 * B转化为TB
 * @param data, 单位B
 * @param toFixedLength, 默认1
 * @returns 保留小数位的TB值
 */
export function convertToTB(data: number | string, toFixedLength = 1): string {
  let sizeTxt: number | string;
  sizeTxt = Math.ceil((+data / 1024 / 1024 / 1024 / 1024) * 100) / 100;
  sizeTxt = sizeTxt.toFixed(toFixedLength);
  return sizeTxt;
}

export function toFixed(num, length: number) {
  if (+num % 1 === 0) {
    return num;
  }
  return parseFloat(num).toFixed(length);
}

export const getConfig = {
  // SHOW_STORAGE: getEnvConfig('SHOW_STORAGE') == '1', // 储存相关是否可见
};

// 对数据进行格式化处理，主要是需要进行是否可以发布、删除等按钮的逻辑控制
export const formateDataSource = (data = []) => {
  if (data && data.length === 0) {
    return [];
  }
  const result: any = [];
  for (let i = 0; i < data.length; i++) {
    const toplevelItem: any = data[i];
    const istopPublished = toplevelItem.status == 1;
    const item = { ...toplevelItem, upArrow: i !== 0, downArrow: i !== data.length - 1, level: 1 };
    result.push(item);

    if (item.children && item.children.length > 0) {
      // 二级
      for (let j = 0; j < item.children.length; j++) {
        const secondlevelItem = item.children[j];
        const isSecondPublished = secondlevelItem.status == 1;
        const secondItem = { ...secondlevelItem, parentHasPublished: istopPublished, parentName: item.name, upArrow: j !== 0, downArrow: j !== item.children.length - 1, level: 2 };
        item.children[j] = secondItem;
        if (secondItem.children && secondItem.children.length > 0) {
          // 三级
          for (let k = 0; k < secondItem.children.length; k++) {
            const thirdlevelItem = secondItem.children[k];
            const thirdItem = { ...thirdlevelItem, parentHasPublished: isSecondPublished, grandparentHasPublished: istopPublished, parentName: secondItem.name, upArrow: k !== 0, downArrow: k !== secondItem.children.length - 1, level: 3 };
            secondItem.children[k] = thirdItem;
          }
        }
      }
    }
  }
  return result;
};

export const findParentIdById = (data = [], id) => {
  if (!id) return;
  for (let i = 0; i < data.length; i++) {
    const dataItem: any = data[i];
    if (dataItem.id == id) return dataItem.pid;
    if (dataItem.children && dataItem.children.length > 0) {
      for (let j = 0; j < dataItem.children.length; j++) {
        if (dataItem.children[j].id == id) {
          return dataItem.children[j].pid;
        }
      }
    }
  }
};
