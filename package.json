{"name": "jt-education-frontend-management", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve-setnode": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve", "serve-test": "cross-env PROXY_ENV=TEST  vue-cli-service serve", "serve-test-setnode": "SET NODE_OPTIONS=--openssl-legacy-provider && cross-env PROXY_ENV=TEST  vue-cli-service serve", "vite": "vite", "vite-test": "cross-env PROXY_ENV=TEST  vite", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "rapper": "rapper --type normal --rapper<PERSON><PERSON> \"src/rapper\" --apiUrl \"http://rap2api.taobao.org/repository/get?id=288321&token=kzAPm3xFqYNRnWqes5OVtHxVvvVhm0pW\" --rapUrl \"http://rap2.taobao.org\""}, "overrides": {"@babel/parser": "7.25.0"}, "dependencies": {"@ant-design/icons-vue": "6.1.0", "@micro-zoe/micro-app": "0.8.11", "@vueuse/core": "7.7.1", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "ant-design-vue": "3.2.20", "axios": "1.8.0", "clipboard": "2.0.11", "core-js": "3.41.0", "js-md5": "0.7.3", "keycloak-js": "21.1.2", "lodash": "4.17.21", "vue": "3.5.13", "vue-class-component": "8.0.0-rc.1", "vue-loader-v16": "16.0.0-beta.5.4", "vue-router": "4.5.0", "vuex": "4.1.0", "wangeditor": "4.7.15"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "1.0.3", "@types/node": "18.19.80", "@typescript-eslint/eslint-plugin": "4.33.0", "@typescript-eslint/parser": "4.33.0", "@vitejs/plugin-vue": "2.3.4", "@vitejs/plugin-vue-jsx": "1.3.10", "@vue/cli-plugin-babel": "4.5.19", "@vue/cli-plugin-eslint": "4.5.19", "@vue/cli-plugin-router": "4.5.19", "@vue/cli-plugin-typescript": "4.5.19", "@vue/cli-plugin-vuex": "4.5.19", "@vue/cli-service": "4.5.19", "@vue/compiler-sfc": "3.5.13", "@vue/eslint-config-prettier": "6.0.0", "@vue/eslint-config-typescript": "7.0.0", "cross-env": "7.0.3", "echarts": "5.6.0", "eslint": "6.8.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-vue": "7.20.0", "less": "4.2.2", "less-loader": "7.3.0", "less-vars-to-js": "1.3.0", "lint-staged": "11.2.6", "prettier": "2.8.8", "rap": "1.3.1", "typescript": "4.1.6", "vite": "2.9.18", "vite-plugin-path-resolve": "1.0.1", "vite-plugin-require-transform": "1.0.21", "vue-echarts": "6.7.3"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,vue}": ["vue-cli-service lint", "git add"]}}