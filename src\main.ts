import { createApp } from 'vue';
import router from './router';
import store from './store';
import { checkLogin } from './keycloak';
// import 'ant-design-vue/dist/antd.variable.less';
import 'ant-design-vue/dist/antd.variable.min.css';
import antd from 'ant-design-vue';
import jtIcon from './lib/jtIcon';
import App from './App.vue';

import './lib/micro-components/useMicroComponents';
import MicroComponents from '@/lib/micro-components/microComponents.vue';

checkLogin().finally(() => {
  const app = createApp(App);
  app.use(store).use(router).use(antd).use(jtIcon, '');
  app.component('micro-components', MicroComponents);
  app.mount('#app');
});
