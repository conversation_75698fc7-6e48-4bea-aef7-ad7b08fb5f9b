<template>
  <a-layout-header class="header">
    <div>
      <jt-icon class="trigger" :type="collapsed ? 'icondaohangzhankai' : 'icondaohang<PERSON><PERSON><PERSON>'" @click="$emit('toggleCollapsed')" />
    </div>
    <div class="right">
      <a-space>
        <message-alert v-if="showMessageCenter"></message-alert>
        <a-dropdown v-if="userInfo.userId">
          <div class="user-box">
            <img class="avatar" :src="userInfo.image || defaultAvatar" alt="" />
            <p :title="userInfo.userName" style="margin: 0">{{ userInfo.userName }}</p>
          </div>
          <template #overlay>
            <a-menu style="display: inline-block">
              <!-- <a-menu-item>
                <a>个人中心</a>
              </a-menu-item> -->
              <a-menu-item @click="handleLogout"><span style="font-size: 12px; color: #555555">退出登录</span> </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <ul v-else class="header-menu">
          <li>
            <a @click="gotoLogin">登录</a>
          </li>
        </ul>
      </a-space>
    </div>
  </a-layout-header>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { useStore } from 'vuex';
import { logout, keycloak } from '../keycloak';
import defaultAvatar from '../assets/images/avatar_big.png';
import messageAlert from './messageAlert.vue';
import { getEnvConfig } from '@/config';

export default defineComponent({
  components: {
    messageAlert,
  },
  props: {
    collapsed: Boolean,
  },
  setup() {
    const store = useStore();
    return {
      userInfo: computed(() => store.state.userInfo),
    };
  },
  data() {
    return {
      defaultAvatar,
      showMessageCenter: getEnvConfig('FEATURE_MESSAGECENTER') === '1',
    };
  },
  methods: {
    handleLogout() {
      let redirectUrl = location.href;

      if (this.$route.path === '/no-auth') {
        redirectUrl = `${location.origin}${location.pathname}`;
      }
      logout(redirectUrl);
    },
    gotoLogin() {
      const loginUrl = keycloak.createLoginUrl();
      window.location.replace(loginUrl);
    },
  },
});
</script>

<style lang="less" scoped>
.header {
  position: sticky;
  top: 0;
  width: 100%;
  display: flex;
  background: #fff;
  height: 50px;
  padding: 0 32px 0 8px;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1px;
  box-shadow: 0px -1px 8px 1px rgba(0, 0, 0, 0.2);
  z-index: 100;
  .trigger {
    font-size: 18px;
  }
  .right {
    height: 50px;
    display: flex;
    align-items: center;
  }
}
.user-box {
  display: flex;
  align-items: center;
  cursor: pointer;
  .avatar {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    border-radius: 50%;
  }
  p {
    color: #555555;
    font-size: 12px;
    line-height: 20px;
    max-width: 85px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.top-link {
  font-size: 12px;
  color: #555;
}
</style>
