import request from '@/request';

const APIs = {
  platformList: '/web/admin/um/v1/platform/list', // 获取平台列表
  platformRoleList: '/web/admin/um/v1/role/list', // 获取用户角色列表
  permissionList: '/web/admin/um/v1/permission/list', // 获取获取权限列表
  existName: '/web/admin/um/v1/role/exist', // 判断某个角色名称是否存在
  create: '/web/admin/um/v1/role/create', // 创建角色
  update: '/web/admin/um/v1/role/update', // 修改角色
  delete: '/web/admin/um/v1/role/delete', // 删除角色
  getRoleBasicInfo: '/web/admin/um/v1/role/basic-info', // 获取某个角色的基本信息
  getPermissionInfo: '/web/admin/um/v1/role/permission-info', // 获取某个角色的基本信息
  getPermissionTypeList: '/web/admin/um/v1/permission/type-list', // 获取权限类型列表
  getRoleUserList: '/web/admin/um/v1/role/user-list', // 分页+搜索获取某个角色的用户列表
  getIdByName: '/web/admin/um/v1/user/id-by-name', // 根据用户名获取用户ID
  getUserAll: '/web/admin/um/v1/role/user-all', // 获取某个自定义角色的全量用户列表
  userUpdate: '/web/admin/um/v1/role/user-update', // 修改角色对应的用户
};

const Role = {
  getPlatformList: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.platformList, {
      method: type || 'GET',
      data,
    });
  },
  getPlatformRoleList: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.platformRoleList, {
      method: type || 'GET',
      data,
    });
  },
  getPermissionList: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.permissionList, {
      method: type || 'GET',
      data,
    });
  },
  existName: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.existName, {
      method: type || 'GET',
      data,
    });
  },
  create: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.create, {
      method: type || 'POST',
      data,
    });
  },
  update: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.update, {
      method: type || 'POST',
      data,
    });
  },

  delete: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.delete, {
      method: type || 'GET',
      data,
    });
  },
  getRoleBasicInfo: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.getRoleBasicInfo, {
      method: type || 'GET',
      data,
    });
  },
  getPermissionInfo: (params: any = {}) => {
    const { type, data } = params;
    return request(APIs.getPermissionInfo, {
      method: type || 'GET',
      data,
    });
  },
  getPermissionTypeList: (params: any = {}) => request(APIs.getPermissionTypeList, { method: params.type || 'GET', data: params.data }),
  getRoleUserList: (params: any = {}) => request(APIs.getRoleUserList, { method: params.type || 'POST', data: params.data }),
  getIdByName: (params: any = {}) => request(APIs.getIdByName, { method: params.type || 'GET', data: params.data }),
  getUserAll: (params: any = {}) => request(APIs.getUserAll, { method: params.type || 'GET', data: params.data }),
  userUpdate: (params: any = {}) => request(APIs.userUpdate, { method: params.type || 'POST', data: params.data }),
};
export default Role;
