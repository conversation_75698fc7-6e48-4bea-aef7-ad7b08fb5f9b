import request from '@/request';

const DYNAMIC_API = {
  list: '/homepage/web/dynamic/manage/getList',
  create: '/homepage/web/dynamic/manage/add',
  update: '/homepage/web/dynamic/manage/update',
  public:'/homepage/web/dynamic/manage/public',
  delete: '/homepage/web/dynamic/manage/delete',
  detail: '/homepage/web/dynamic/manage/detail',
  
  download: '/homepage/web/dynamic/file/down',



  
  userUpload: '/messagecenter/web/receiver/import',

  userExport: '/messagecenter/web/receiver/downloadResult',
  successUserExport: '/messagecenter/web/receiver/downloadRecord',
  send: '/messagecenter/web/manage/sendMail',

  cancel: '/messagecenter/web/manage/cancelMail',
  mailType: '/messagecenter/web/getAllMailType',
};

const DynamicManagement = {
  // 增删改查基础功能
  getList: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.list, { method: type || 'GET', data });
  },
  deleteDynamic: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.delete, { method: type || 'GET', data });
  },
  publicDynamic: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.public, { method: type || 'GET', data });
  },
  createDynamic: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.create, { method: type || 'GET', data });
  },
  updateDynamic: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.update, { method: type || 'GET', data });
  },
  getDetail: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.detail, { method: type || 'GET', data });
  },

 
  

  // 上传用户列表
  uploadSenderUser: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.userUpload, { method: type || 'GET', data });
  },
  download: DYNAMIC_API.download,
  exportUser: DYNAMIC_API.userExport,
  exportSuccessUser: DYNAMIC_API.successUserExport,

  sendMail: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.send, { method: type || 'GET', data });
  },

  
  cancelMail: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.cancel, { method: type || 'GET', data });
  },


  getMailType: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(DYNAMIC_API.mailType, { method: type || 'GET', data });
  },
};
export default DynamicManagement;
