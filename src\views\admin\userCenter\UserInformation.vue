<template>
  <div class="user-information-container">
    <div v-if="!editing" class="user-information flex">
      <a-descriptions class="descriptions-container" :column="1" :colon="true">
        <a-descriptions-item label="用户名"> {{ userInfo.userName || '-' }} </a-descriptions-item>
        <a-descriptions-item v-if="phoneLoginValid" label="手机号"> {{ hidePhone || '-' }} </a-descriptions-item>
        <a-descriptions-item label="姓名"> {{ userInfo.fullName || '-' }} </a-descriptions-item>
        <a-descriptions-item label="身份"> {{ userInfo.identity }} </a-descriptions-item>
        <a-descriptions-item label="身份信息"> {{ identityInfo || '-' }} </a-descriptions-item>
        <a-descriptions-item label="邮箱">
          {{ userInfo.email || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="简介"> {{ userInfo.introduction || '-' }} </a-descriptions-item>
        <a-descriptions-item label="">
          <a-space :size="12">
            <a-button @click="handleEdit" class="operate-btn" type="primary">编辑</a-button>
            <a-button v-if="supportPwdModify" class="operate-btn" @click="handleModifyPwd">修改密码</a-button>
          </a-space>
        </a-descriptions-item>
      </a-descriptions>
      <div class="avatar-container">
        <img :src="userInfo.image || defaultUrl" alt="" />
      </div>
    </div>
    <user-information-form v-else :jtPK="jtPK" :value="formValue" @ok="onFormOk" @close="handleClose" @changeActiveTab="changeActiveTab"></user-information-form>
    <pwd-form-dlg :jtPK="jtPK" :phoneNum="userInfo.phoneNum" :visible="dlgVisible" @ok="onDlgOk" @cancel="() => (dlgVisible = false)"></pwd-form-dlg>
  </div>
</template>

<script>
import userInformationForm from './userInformationForm.vue';
import pwdFormDlg from './pwdFormDlg.vue';
import { checkImgUrl, phoneHider } from '../utils';
import { GET, POST } from '@/request';
import request from '@/request';
import { getEnvConfig } from '@/config';
export default {
  name: 'UserInformation',
  components: { userInformationForm, pwdFormDlg },
  data() {
    return {
      jtPK: '',
      userInfo: {
        userName: '',
        fullName: '',
        identity: '',
        email: '',
        introduction: '',
        image: '',
        phoneNum: '',
      },
      editing: this.$route.query.edit === 'true',
      dlgVisible: false,
      defaultUrl: require('@/assets/images/avatar_admin.png'),
    };
  },
  watch: {
    editing(val) {
      if (this.$route.query.ecloud == 'true' && this.editing != true) {
        this.$router.replace({ query: { ...this.$route.query, edit: val, ecloud: 'false' } });
      } else {
        this.$router.replace({ query: { ...this.$route.query, edit: val } });
      }
      if (!val) {
        this.getUserInfo();
      }
    },
  },
  mounted() {
    this.getJtPK();
    this.getUserInfo();
  },
  computed: {
    supportPwdModify() {
      return getEnvConfig('USER_LOGIN_OPTION').includes('password');
    },
    phoneLoginValid() {
      return getEnvConfig('USER_LOGIN_OPTION').includes('smscode');
    },
    identityInfo() {
      const userInfo = this.userInfo;
      const identityInfoGenerators = {
        教师: (userInfo) => {
          return [userInfo.school, userInfo.faculty, userInfo.identity].filter((item) => !!item).join(' ');
        },
        学生: (userInfo) => {
          return [userInfo.school, userInfo.faculty, userInfo.major, userInfo.identity, `(学号${userInfo.stuNum || '-'})`].filter((item) => !!item).join(' ');
        },
        开发者: (userInfo) => {
          return [userInfo.companyArea, userInfo.company, userInfo.identity].filter((item) => !!item).join(' ');
        },
      };

      const generator = identityInfoGenerators[userInfo.identity];
      return generator ? generator(userInfo) : '';
    },
    formValue() {
      return { ...this.userInfo };
    },
    hidePhone() {
      return phoneHider(this.userInfo.phoneNum);
    },
  },
  methods: {
    handleClose() {
      this.editing = false;
    },
    async getJtPK() {
      const res = await request('/web/um/v1/public/key');
      if (res.code === 0) {
        this.jtPK = res.data;
      }
    },
    handleEdit() {
      this.editing = true;
    },
    handleModifyPwd() {
      this.dlgVisible = true;
    },
    async getUserInfo() {
      const res = await GET('/web/um/v1/user/detail', {}, { useError: false });
      if (res.code === 0) {
        if (!checkImgUrl(res.data.image)) {
          res.data.image = '';
        }
        this.userInfo = { ...res.data, companyArea: res.data.companyArea || res.data.area };
        this.$store.commit('UPDATE_USERINFO', res.data);
      }
    },
    async onFormOk(form) {
      if (form.area == '其他') {
        form.school = form.schoolInput;
      }
      const params = {
        fullName: form.fullName,
        identity: form.identity,
        phoneNum: form.phoneNum,
        email: form.email,
        school: form.school,
        area: form.area || form.companyArea,
        faculty: form.faculty,
        major: form.major,
        stuNum: form.stuNum,
        introduction: form.introduction,
        image: form.image,
        company: form.company,
        smsCode: form.code,
      };
      const res = await POST('/web/um/v1/user/update', params, { useError: false });
      if (res.code === 0) {
        await this.getUserInfo();

        const redirectUrl = this.$route.query.redirectUrl;
        this.$notification['success']({
          message: '保存成功',
          description: '用户信息保存成功',
        });

        if (redirectUrl) {
          this.$router.push(redirectUrl);
        } else {
          this.editing = false;
        }
      } else {
        this.$notification['error']({
          message: '保存失败',
          description: res.msg || res.errorMessage,
        });
      }
    },
    onDlgOk(form) {
      const params = {
        oldPassword: form.oldPassword,
        newPassword: form.newPassword,
        changeMethod: form.passwordType,
        smsCode: form.code,
      };
      POST('/web/um/v1/user/password/change', params, { useError: false }).then((res) => {
        if (res.code === 0) {
          this.$notification['success']({
            message: '修改密码成功',
            description: '修改密码成功',
          });
          this.dlgVisible = false;
        } else {
          this.$notification['error']({
            message: '修改密码失败',
            description: res.errorMessage || res.msg,
          });
        }
      });
    },
    changeActiveTab() {
      this.$emit('changeActiveTab', '2', 'userInform');
    },
  },
};
</script>

<style lang="less" scoped>
.user-information-container {
  padding: 28px 0;
}
.flex {
  display: flex;
}
.avatar-container {
  margin-right: 200px;
  img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
  }
}
.descriptions-container {
  width: 600px;
  margin-left: 100px;
}
/deep/ .ant-descriptions-item-label {
  display: block !important;
  margin-right: 24px;
  text-align: right;
  width: 100px;
}
/deep/ .ant-descriptions-row > th,
/deep/ .ant-descriptions-row > td {
  padding-bottom: 32px;
}
.operate-btn {
  width: 92px;
}
.label {
  margin-right: 8px;
}

/deep/ .ant-descriptions-item-content {
  max-width: 440px;
  color: #00141a;
}
</style>
