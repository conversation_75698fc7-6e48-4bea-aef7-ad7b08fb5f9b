<template>
  <div class="jiutian-footer">
    <div class="footer-container">
      <div class="jiutian-intro-text">
        <p style="font-size: 16px">联系我们</p>
        <p class="email"><EMAIL></p>
        <p>北京市西城区宣武门西大街32号中国移动创新大楼</p>
        <div class="logo-container">
          <img src="@/assets/images/preview/logo-d.png" alt="" />
          <a v-if="showRecordNumber" href="https://beian.miit.gov.cn" target="_blank" rel="noopenner noreferrer">京ICP备05002571号-5</a>
        </div>
      </div>
      <div class="jiutian-qr-code">
        <img src="@/assets/images/preview/gzh.png" alt="" />
        <span>九天人工智能公众号</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'Footer',
  data() {
    return {
      showRecordNumber: true,
    };
  },
};
</script>
<style lang="less" scoped>
.jiutian-footer {
  height: 280px;
  background: #002633;
  .footer-container {
    padding-top: 56px;
    width: 1200px;
    margin: auto;
    display: flex;
    justify-content: space-between;
    .jiutian-intro-text {
      color: #ffffff;
      .email {
        padding: 8px 0px;
      }
      .logo-container {
        display: flex;
        align-items: center;
        padding-top: 36px;
        img {
          height: 32px;
        }
        a {
          padding-left: 21px;
          font-size: 12px;
        }
      }
    }
    .jiutian-qr-code {
      padding-top: 16px;
      display: flex;
      flex-direction: column;
      img {
        width: 128px;
        height: 128px;
      }
      span {
        color: #a0a6ab;
        line-height: 20px;
        padding-top: 6px;
      }
    }
  }
}
</style>
