/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-13 17:18:17
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-09 14:06:58
 */
// 首页概览数据区分
export const SUMMARY_TYPE = {
  TRAIN: '1',
  REASON: '2',
};
export const SUMMARY_CLASS = ['cpu', 'gpu', 'memory', 'vgpu', 'storage'];
export const SUMMARY_CLASS_UNIT = {
  'cpu': '核',
  'gpu': '卡',
  'vgpu': '卡',
  'memory': 'T',
  'storage': 'T',
}
export const SUMMARY_CLASS_NAME = {
  [SUMMARY_TYPE.TRAIN]: {
    'cpu': '训练CPU',
    'gpu': '训练加速卡',
    'memory': '训练内存',
    'vgpu': '训练加速卡vGPU',
    'storage': '训练存储',
  },
  [SUMMARY_TYPE.REASON]: {
    'cpu': '推理CPU',
    'gpu': '推理加速卡',
    'memory': '推理内存',
    'vgpu': '推理加速卡vGPU',
    'storage': '推理存储',
  },
};
export const detailColumsForHome = (type: string) => {
  switch (type) {
    case 'train':
      return [
        { title: '平台', dataIndex: 'platform', width: '15%' },
        { title: '训练加速卡（卡）', dataIndex: 'GPU', width: '17%' },
        { title: '训练加速卡vGPU（卡）', dataIndex: 'vGPU', width: '17%' },
        { title: '训练CPU（核）', dataIndex: 'CPU', width: '17%' },
        { title: '训练内存（TB）', dataIndex: 'memory', width: '17%' },
        { title: '训练存储（TB）', dataIndex: 'storage', width: '17%' },
      ];
    case 'reason':
      return [
        { title: '平台', dataIndex: 'platform', width: '15%' },
        { title: '推理加速卡（卡）', dataIndex: 'GPU', width: '17%' },
        { title: '推理加速卡vGPU（卡）', dataIndex: 'vGPU', width: '17%' },
        { title: '推理CPU（核）', dataIndex: 'CPU', width: '17%' },
        { title: '推理内存（TB）', dataIndex: 'memory', width: '17%' },
        { title: '推理存储（TB）', dataIndex: 'storage', width: '17%' },
      ];
    default:
      break;
  }
};
// 侧边栏路径方式
export const LINK_FLAG = {
  RELATIVE: 0, // 相对路径
  ABSOLUTE: 1, // 绝对路径
};

// 帮助中心管理
export const detailColumsForTableTree = (type: string) => {
  switch (type) {
    case 'helpDocument':
      return [
        {
          dataIndex: 'name',
          key: 'name',
          title: '页面标题',
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '状态',
          dataIndex: 'state',
          key: 'state',
          scopedSlots: { customRender: 'state' },
        },
        {
          title: '最新发布时间',
          dataIndex: 'publishTime',
          key: 'publishTime',
          scopedSlots: { customRender: 'publishTime' },
        },
        {
          title: '最后编辑时间',
          key: 'updateTime',
          dataIndex: 'updateTime',
          scopedSlots: { customRender: 'updateTime' },
        },
        {
          title: '操作',
          key: 'id',
          scopedSlots: { customRender: 'id' },
        },
      ];
    case 'skillDocument':
      return [
        {
          dataIndex: 'name',
          key: 'name',
          title: '页面标题',
          scopedSlots: { customRender: 'name' },
        },
        {
          title: '状态',
          dataIndex: 'state',
          key: 'state',
          scopedSlots: { customRender: 'state' },
        },
        {
          title: '最新发布时间',
          dataIndex: 'publishTime',
          key: 'publishTime',
          scopedSlots: { customRender: 'publishTime' },
        },
        {
          title: '操作',
          key: 'id',
          scopedSlots: { customRender: 'id' },
        },
      ];
    default:
      break;
  }
};

// 站内信管理
export const SENDER_TYPE = {
  ASSIGN: '0', // 指定对象
  GLOBAL: '1', // 全部对象
};
export const SENDER_TYPE_MSG = {
  [SENDER_TYPE.GLOBAL]: '全部用户',
  [SENDER_TYPE.ASSIGN]: '指定用户',
};
// 站内信相关map集合
export const MAIL_STATUS = {
  FAILED: '0',
  SENT: '1',
  DRAFT: '2',
  TERMINATED: '3',
  REVIEWING: '4',
};
export const MAIL_STATUS_MSG = {
  [MAIL_STATUS.FAILED]: '发送失败',
  [MAIL_STATUS.SENT]: '发送成功',
  [MAIL_STATUS.DRAFT]: '草稿',
  [MAIL_STATUS.TERMINATED]: '发送终止',
  [MAIL_STATUS.REVIEWING]: '审核中',
};
export const MAIL_TYPE = {
  OTHER: '0',
  SERVICE: '1',
  ACTIVITY: '2',
  PRODUCT: '3',
};
export const MAIL_TYPE_MSG = {
  [MAIL_TYPE.OTHER]: '其他',
  [MAIL_TYPE.SERVICE]: '服务信息',
  [MAIL_TYPE.ACTIVITY]: '活动信息',
  [MAIL_TYPE.PRODUCT]: '产品信息',
};
export const USER_LIST_FILE = 10 * 1024 * 1024; // 用户列表上传文件最大10MB
export const USER_LIST_FILE_TEXT = '10MB';
