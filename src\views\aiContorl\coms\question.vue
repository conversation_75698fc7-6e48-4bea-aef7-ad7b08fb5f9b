<template>
  <div class="question-wrap">
    <!-- 标题 -->
    <div class="title">{{ title }}</div>
    <!-- 列表 -->
    <div class="ques-ul">
      <div class="ques-item" v-for="(item, index) in list" :key="index">
        <div class="ques-name">
          <div class="qussss">Q：</div>
          <div class="ques-title">{{ item.question || questionsListQ }}</div>
        </div>
        <div class="ans">
          <div class="qussss">A：</div>
          <div class="ques-title" style="padding: 2px">{{ item.answer || questionsListA }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'QuestionList',
  props: {
    //  标题 常见问题
    title: {
      type: String,
      default: '',
    },
    // 展示项数组
    list: {
      type: Array,
      default() {
        //当为数组或者对象时，默认值要用函数方式返回
        return [];
      },
    },
  },
  data() {
    return {
      questionsListQ: '这是一条问题描述',
      questionsListA: '这是一段问题解答这是一段问题解答这是一段问题解答。',
    };
  },
};
</script>
<style lang="less" scoped>
.question-wrap {
  width: 100%;
  padding-top: 52px;
  text-align: left;
  min-width: 1320px;
  margin-bottom: 56px;
  .title {
    color: #121f2c;
    text-align: center;
    font-size: 28px;
    line-height: 36px;
    margin-bottom: 24px;
    font-weight: 600;
  }
  .ques-ul {
    // max-width: 2000px;
    width: 1180px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .ques-item {
      width: 48%;
      flex: 1 0 48%;
      margin-bottom: 20px;
      &:nth-child(2n) {
        margin-left: 4%;
      }
      .ques-name {
        color: #121f2c;
        background: #f5f9fb;
        border-radius: 20px;
        height: 40px;
        line-height: 40px;
        padding-left: 20px;
        display: flex;
        font-weight: 500;
        font-size: 16px;
      }
      .ans {
        font-size: 14px;
        color: #606972;
        padding-left: 20px;
        margin-top: 17px;
        display: flex;
        word-break: break-all;
      }
      .img {
        width: 50px;
      }
      .desc {
        line-height: 20px;
        font-size: 16px;
        margin-top: 10px;
      }
    }
  }
  .qussss {
    color: #00b3cc;
    font-size: 16px;
  }
}
</style>
