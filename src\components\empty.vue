<template>
  <div class="empty-container">
    <div class="content" v-if="showNoDataText" :style="{ backgroundImage: 'url(' + emptyImage + ')' }">
      <p class="title">暂无{{ title }}</p>
      <p class="description">
        <slot name="description"></slot>
      </p>
    </div>
    <div class="content" v-else :style="{ backgroundImage: 'url(' + sousuokongImage + ')' }">
      <p class="title">抱歉，没有找到相关{{ title }}</p>
      <p class="description">您可以换一个关键词试试哦～</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps } from 'vue';
const props = defineProps({
  showNoDataText: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
  },
});
const sousuokongImage = require('@/assets/images/sousokong.png');
const emptyImage = require('@/assets/images/<EMAIL>');
</script>

<style lang="less" scoped>
.empty-container {
  display: flex;
  justify-content: center;
  p {
    margin-bottom: 0;
  }
  .content {
    width: 416px;
    height: 416px;
    padding-top: 300px;
    background-size: auto 400px;
    .title {
      font-size: 18px;
      font-weight: 600;
      color: #121f2c;
    }
    .description {
      font-size: 14px;
      color: #606972;
    }
  }
}
</style>
