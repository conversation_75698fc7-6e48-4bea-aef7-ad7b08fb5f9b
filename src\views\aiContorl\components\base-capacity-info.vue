<template>
  <div class="capacity-content-container base-capacity-info">
    <div class="content-header">
      <div class="content-header-title">能力基本信息</div>
    </div>
    <div class="content-box">
      <div class="content-form-box">
        <a-form :colon="false" :model="formState" ref="formRef" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <div class="form-title">前台展示信息</div>
          <div class="form-box">
            <div class="field-item select-box">
              <a-form-item label="能力类别" name="capacityCategory" help="能力类别会影响能力目录所在的位置及banner的样式，如不符合请修改">
                <a-select v-model:value="formState.capacityCategory" placeholder="请选择" @change="handleCapacityCategoryChange" :options="capacityCategoryOptions" :disabled="capacityCategorySelectDisabled" :getPopupContainer="(triggerNode) => triggerNode.parentNode" />
              </a-form-item>
            </div>
            <div class="field-item select-box">
              <a-form-item label="背景图类别" name="backGroundType">
                <a-select v-model:value="formState.backGroundType" placeholder="请选择" :options="backgroundImgTypeOptions" :getPopupContainer="(triggerNode) => triggerNode.parentNode" />
              </a-form-item>
            </div>
            <div class="field-item input-box fix-position">
              <a-form-item label="能力名称" name="capacityName">
                <a-input v-model:value="formState.capacityName" placeholder="请输入" :maxLength="20" :disabled="capacityNameInputDisabled" autoComplete="off" />
              </a-form-item>
            </div>
            <div class="field-item textarea-box">
              <a-form-item label="能力简介" name="capacityDesc" class="form-desc">
                <a-textarea v-model:value="formState.capacityDesc" :rows="4" placeholder="请输入" />
                <div v-if="descError" class="desc-tips">{{ descErrorMessage }}</div>
                <div class="desc-count">
                  <span class="desc-num" :class="descError ? 'desc-error' : ''">{{ formState.capacityDesc.length }}</span
                  >/
                  <span class="count-num">150</span>
                </div>
              </a-form-item>
            </div>
          </div>
          <div class="form-title" style="margin-top: 88px">基本信息收集</div>
          <div class="form-box">
            <div class="field-item select-box">
              <a-form-item label="能力提供方" name="capacityProvider">
                <a-select v-model:value="formState.capacityProvider" placeholder="请选择" :options="capacityProviderOptions" :getPopupContainer="(triggerNode) => triggerNode.parentNode" />
              </a-form-item>
            </div>
            <div class="field-item select-box">
              <a-form-item label="能力提供方账号" name="capacityProviderAccount">
                <a-input v-model:value="formState.capacityProviderAccount" placeholder="请输入" :maxlength="100" autoComplete="off" />
              </a-form-item>
            </div>
            <div class="field-item input-box">
              <a-form-item label="能力提供方手机号" name="capacityProviderPhone">
                <a-input v-model:value="formState.capacityProviderPhone" placeholder="请输入" :maxlength="11" autoComplete="off" />
              </a-form-item>
            </div>
            <div class="field-item input-box">
              <a-form-item label="能力提供方邮箱" name="capacityProviderEmail">
                <a-input v-model:value="formState.capacityProviderEmail" placeholder="请输入" :maxlength="200" autoComplete="off" />
              </a-form-item>
            </div>
            <div class="field-item">
              <a-form-item label="能力是否上架至公网" name="isGoOnline" help="默认选择是，平台将定期更新能力至公网版，同时公网版对能力性能要求更高，也更利于业务拓展">
                <a-radio-group v-model:value="formState.isGoOnline">
                  <a-radio :value="IS_ONLINE_ENUM.ONLINE">是</a-radio>
                  <a-radio :value="IS_ONLINE_ENUM.NOT_ONLINE">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
            <div class="field-item bAddress-box fix-position">
              <a-form-item
                v-for="(item, index) in formState.bAddress"
                :label="'能力内网B域调用地址'"
                :name="['bAddress', index, 'bAddress']"
                :key="item.key"
                :rules="{
                  required: isInnerNetWork,
                  validator: validateBAddress,
                }"
                :class="index !== 0 ? 'no-label' : ''"
              >
                <div style="display: flex">
                  <div class="b-btn-box add-btn-box" :style="{ visibility: item.addShow ? 'visible' : 'hidden' }">
                    <a-button type="text" v-show="item.addShow" :disabled="!item.bAddress" @click="handleAdd('b')"><plus-outlined :style="{ fontSize: '12px', color: !item.bAddress ? '#EAEAEA' : '#606972' }" /></a-button>
                  </div>
                  <div class="b-btn-box remove-btn-box">
                    <a-button type="text" :disabled="formState.bAddress.length === 1" @click="handleRemove(item.key, 'b')"><minus-outlined :style="{ fontSize: '12px', color: formState.bAddress.length === 1 ? '#EAEAEA' : '#606972' }" /></a-button>
                  </div>
                  <div class="input-box">
                    <a-input placeholder="请输入" v-model:value="item.bAddress" :maxlength="300" autoComplete="off" />
                  </div>
                </div>
              </a-form-item>
              <div style="display: flex">
                <div
                  style="
                     {
                      flex: 0 0 20.83333333%;
                      width: 20.83333333%;
                    }
                  "
                ></div>
                <div :class="!isBAddressError ? 'b-explain' : 'b-error'">{{ bHelpText }}</div>
              </div>
            </div>
            <div class="field-item bAddress-box fix-position">
              <a-form-item
                v-for="(item, index) in formState.oAddress"
                :label="'能力公网调用地址'"
                :name="['oAddress', index, 'oAddress']"
                :key="item.key"
                :rules="{
                  required: !isInnerNetWork,
                  validator: validateOAddress,
                }"
                :class="index !== 0 ? 'no-label' : ''"
              >
                <div style="display: flex">
                  <div class="b-btn-box add-btn-box" :style="{ visibility: item.addShow ? 'visible' : 'hidden' }">
                    <a-button type="text" v-show="item.addShow" @click="handleAdd('o')" :disabled="!item.oAddress"><plus-outlined :style="{ fontSize: '12px', color: !item.oAddress ? '#EAEAEA' : '#606972' }" /></a-button>
                  </div>
                  <div class="b-btn-box remove-btn-box">
                    <a-button type="text" :disabled="formState.oAddress.length === 1" @click="handleRemove(item.key, 'o')"><minus-outlined :style="{ fontSize: '12px', color: formState.oAddress.length === 1 ? '#EAEAEA' : '#606972' }" /></a-button>
                  </div>
                  <div class="input-box">
                    <a-input placeholder="请输入" v-model:value="item.oAddress" :maxlength="300" autoComplete="off" />
                  </div>
                </div>
              </a-form-item>
              <div style="display: flex">
                <div
                  style="
                     {
                      flex: 0 0 20.83333333%;
                      width: 20.83333333%;
                    }
                  "
                ></div>
                <div :class="!isOAddressError ? 'b-explain' : 'b-error'">{{ oHelpText }}</div>
              </div>
            </div>
          </div>
        </a-form>
        <div class="btn-box">
          <div class="block-one"></div>
          <div class="block-two">
            <a-button class="next-btn" @click="goToNextStep" type="primary">下一步</a-button>
            <a-button class="save-btn" @click="toSaveDraft" type="primary" ghost>保存</a-button>
            <a-button class="cancel-btn" @click="cancel">取消</a-button>
          </div>
        </div>
      </div>
      <div class="show-box">
        <div class="show-header-box">入驻能力页示意</div>
        <div class="show-img-box"><img :src="imageUrl" alt="示意图" /></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import type { Rule } from 'ant-design-vue/es/form';
import { defineComponent, reactive, ref, createVNode, computed, defineExpose, toRefs, watch, nextTick } from 'vue';
import { PlusOutlined, MinusOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { useStore } from 'vuex';
import { message, Modal } from 'ant-design-vue';
import request, { requestBlob } from '@/request';
import type { FormInstance } from 'ant-design-vue';
import cloneDeep from 'lodash/cloneDeep';
import { BACKGROUND_IMG_OPTIONS, BACKGROUND_IMG_ENUM, IS_ONLINE_ENUM, CAPACITY_CATEGORY_ENUM, CAPACITY_CATEGORY_OPTIONS, getPicUrl, MEDIA_ENUM, getVideoInfo, getVideoPosterInfo, MEDIA_TYPE_MAP } from './components/utils';
import { validatePhone, validateEmail } from '@/utils/index';
import { getEnvConfig } from '@/config';

export default defineComponent({
  components: { PlusOutlined, MinusOutlined },
  props: {
    isEdit: {
      // 是否为编辑和新建
      type: Boolean,
      default: false,
    },
    appInfo: {
      // 编辑的时候回填这个数据
      type: Object,
      default() {
        return {};
      },
    },
    toNext: {
      // 下一步函数
      type: Function,
      default() {
        return {};
      },
    },
    saveDraft: {
      // 保存草稿
      type: Function,
      default() {
        return {};
      },
    },
    handleConfirm: {
      type: Function,
      default() {
        return {};
      },
    },
  },
  emits: ['toNext', 'saveDraft', 'handleConfirm'],
  setup(props: any, { emit }) {
    // 能力类别select的options，从接口获取
    const capacityCategoryOptions = ref(CAPACITY_CATEGORY_OPTIONS);
    // 背景类别图选项
    const backgroundImgTypeOptions = ref(BACKGROUND_IMG_OPTIONS);
    const store = useStore();
    const formRef = ref<FormInstance>(); // form的ref
    // 能力提供方的选项options，从接口获取
    const capacityProviderOptions = ref([]);
    // 可编辑表格最多的数据条数
    const bMaxNum = ref(10);
    // 可编辑表格最多的数据条数
    const oMaxNum = ref(10);
    const bHelpText = ref('请在九天能力平台完成服务部署'); // b域网址显示文字
    const oHelpText = ref('提交上架申请后，相关人员会尽快联系您；同时也可邮件至******************************获取部署指导'); // 公网网址显示文字
    const isBAddressError = ref(false); // b域网址出现错误
    const isOAddressError = ref(false); // 公网地址出现错误
    const labelCol = ref({ span: 5 });
    const wrapperCol = ref({ span: 16 });
    const { isEdit, appInfo } = toRefs(props);
    const isDraft = ref(true);
    const capacityNameInputDisabled = ref(false); // 能力名称禁止修改
    const capacityCategorySelectDisabled = ref(false); // 能力类别禁止修改
    const isInnerNetWork = getEnvConfig('CAPABILITY_INNER_VERSION') === 'true'; // 是否为公网环境变量,true是办公网
    const lastChoose = ref(CAPACITY_CATEGORY_ENUM.FACE_RECOFNITION);
    // 默认b网地址
    const defaultBAddress = {
      key: '1',
      bAddress: '',
      addShow: true,
    };

    const defaultOAddress = {
      key: '1',
      oAddress: '',
      addShow: true,
    };

    let formState = reactive({
      capacityCategory: CAPACITY_CATEGORY_ENUM.FACE_RECOFNITION, // 能力类别
      backGroundType: BACKGROUND_IMG_ENUM.FACE, // 背景图类别
      capacityName: '', // 能力名称
      capacityDesc: '', // 能力简介
      capacityProvider: null, // 能力提供方
      capacityProviderAccount: null, // 能力提供方账号
      capacityProviderPhone: '', // 能力提供方手机号
      capacityProviderEmail: '', // 能力提供方邮箱
      isGoOnline: IS_ONLINE_ENUM.ONLINE, // 能力是否上架至公网
      bAddress: [{ ...defaultBAddress }], // 能力b网调用地址
      oAddress: [{ ...defaultOAddress }], // 能力公网调用地址
    }) as any;

    const initFormStateData = (dataOrigin) => {
      // 如果是编辑，就需要处理一下数据，主要处理b网地址
      const newObj = {
        capacityCategory: dataOrigin.type,
        capacityName: dataOrigin.name || '',
        backGroundType: dataOrigin.backGroundType || '',
        capacityDesc: dataOrigin.intro || '',
        capacityProvider: dataOrigin.providerId || null,
        capacityProviderAccount: dataOrigin.providerAccount || null,
        capacityProviderPhone: dataOrigin.providerPhone || '',
        capacityProviderEmail: dataOrigin.providerEmail || '',
        isGoOnline: dataOrigin.shelvePubNet, // 能力是否上架至公网
        bAddress: [{ ...defaultBAddress }], // 能力b网调用地址
        oAddress: [{ ...defaultOAddress }], // 能力公网调用地址
      };
      const bArr = dataOrigin?.priNetCallAddr?.split(',') || [];
      const bAddressArr = bArr.map((item, index) => {
        const obj = {
          bAddress: item,
          key: `${index + 1}`,
          addShow: false,
        };
        // 最后一条禁用按钮要放开，并且+号要显示
        if (index === bArr.length - 1) {
          obj.addShow = true;
        }
        return obj;
      });
      newObj.bAddress = bAddressArr;
      const oArr = dataOrigin?.pubNetCallAddr?.split(',') || [];
      const oAddressArr = dataOrigin?.pubNetCallAddr?.split(',').map((item, index) => {
        const obj = {
          oAddress: item,
          key: `${index + 1}`,
          addShow: false,
        };
        // 如果是第一条并且有数据的时候，是需要隐藏掉当前的数据的加号
        // 最后一条禁用按钮要放开，并且+号要显示
        if (index === oArr.length - 1) {
          obj.addShow = true;
        }
        return obj;
      });
      newObj.oAddress = oAddressArr;
      return newObj;
    };
    // b域网址生成的id
    const count = computed(() => Number(formState.bAddress[formState.bAddress.length - 1]['key']) + 1); // 用来标记id
    const ocount = computed(() => Number(formState.oAddress[formState.oAddress.length - 1]['key']) + 1); // 用来标记id
    const imageUrl = ref(''); // 修改能力类别的时候，更换右侧示意图

    // 校验能力名称输入
    const validateCapacityName = async (_rule: Rule, value: string) => {
      if (!value?.trim()) {
        return Promise.reject('请输入能力名称');
      }
      if (value?.trim().length > 20) {
        return Promise.reject('能力名称不超过20个字');
      }
      return Promise.resolve();
    };

    // 校验简介
    const descError = ref(false);
    const descErrorMessage = ref('');
    const validateCapacityDesc = async (_rule: Rule, value: string) => {
      if (value.length === 0) {
        descError.value = true;
        descErrorMessage.value = '请输入';
        return Promise.reject();
      } else if (value.length > 150) {
        descError.value = true;
        descErrorMessage.value = '字数超过限制';
        return Promise.reject();
      } else {
        descError.value = false;
        descErrorMessage.value = '';
        return Promise.resolve();
      }
    };

    // 校验能力提供方
    const validateCapacityProvider = async (_rule: Rule, value: string) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      if (!value) {
        return Promise.reject('请选择');
      }
      return Promise.resolve();
    };

    // 校验能力提供方方账号
    const validateProviderAccount = async (_rule: Rule, value: string) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      if (!value?.trim()) {
        return Promise.reject('请输入');
      }
      if (value) {
        const flag = await checkCapacityProviderAccount(value);
        if (!flag) {
          return Promise.reject('该能力提供方账号不存在');
        }
      }
      return Promise.resolve();
    };

    // 校验能力提供方手机号
    const validateProviderPhone = async (_rule: Rule, value: string) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      if (value === '' || value?.trim() === '') {
        return Promise.reject('请输入');
      }
      return validatePhone(value);
    };

    // 校验能力提供方邮箱
    const validateProviderEmail = async (_rule: Rule, value: string) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      if (value === '' || value?.trim() === '') {
        return Promise.reject('请输入');
      }
      return validateEmail(value);
    };

    // 校验b域地址
    const validateBAddress = async (_rule: Rule, value: any) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      if ((isInnerNetWork && !value) || (value && !/^(https:\/\/|http:\/\/)/.test(value))) {
        bHelpText.value = '存在不符合要求网址';
        isBAddressError.value = true;
        return Promise.reject('存在不符合要求网址');
      }
      bHelpText.value = '请在九天能力平台完成服务部署';
      isBAddressError.value = false;
      return Promise.resolve();
    };
    // 公网校验地址
    const validateOAddress = async (_rule: Rule, value: any) => {
      if (isDraft.value) {
        return Promise.resolve();
      }
      if ((!isInnerNetWork && !value) || (value && !/^(https:\/\/|http:\/\/)/.test(value))) {
        oHelpText.value = '存在不符合要求网址';
        isOAddressError.value = true;
        return Promise.reject('存在不符合要求网址');
      }
      oHelpText.value = '提交上架申请后，相关人员会尽快联系您；同时也可邮件至******************************获取部署指导';
      isOAddressError.value = false;
      return Promise.resolve();
    };

    const rules: Record<string, Rule[]> = {
      capacityCategory: [{ required: true, trigger: ['change'] }],
      backGroundType: [{ required: true, trigger: ['change'] }],
      capacityName: [{ required: true, validator: validateCapacityName, trigger: ['change'] }],
      capacityDesc: [{ required: true, message: '', validator: validateCapacityDesc, trigger: ['change'] }],
      capacityProvider: [{ required: true, validator: validateCapacityProvider, trigger: ['change'] }],
      capacityProviderAccount: [{ required: true, validator: validateProviderAccount, trigger: ['blur'] }],
      capacityProviderPhone: [{ required: true, validator: validateProviderPhone, trigger: ['change'] }],
      capacityProviderEmail: [{ required: true, validator: validateProviderEmail, trigger: ['change'] }],
    };

    // 获取能力提供方选项接口
    const getCapacityProvider = async () => {
      try {
        const res = await request('/aiipweb/om-service/capability/providers-query', { method: 'GET', data: {} });
        if (res.state === 'OK') {
          capacityProviderOptions.value = res.body;
        } else {
          capacityProviderOptions.value = [];
        }
      } catch (e) {
        capacityProviderOptions.value = [];
        console.log(e);
      }
    };

    // 检查能力提供方账号唯一性
    const checkCapacityProviderAccount = async (name: string) => {
      try {
        const res = await request('/web/admin/um/v1/user/exist', { method: 'GET', data: { userName: name } });
        if (res.code === 0) {
          return res?.data;
        } else {
          return false;
        }
      } catch (e) {
        console.log(e);
        return false;
      }
    };

    // 获取能力类别枚举选项
    const getCapTypeOptions = async (label) => {
      try {
        const res = await request('/aiipweb/om-service/dict/getDict', { method: 'GET', data: { dictName: label } });
        if (res.state === 'OK') {
          capacityCategoryOptions.value = res.body;
        }
      } catch (e) {
        capacityCategoryOptions.value = [];
        console.log(e);
      }
    };

    // 获取背景图类别媒体选项
    const getBackImgOptions = async (label) => {
      try {
        const res = await request('/aiipweb/om-service/dict/getDict', { method: 'GET', data: { dictName: label } });
        if (res.state === 'OK') {
          backgroundImgTypeOptions.value = res.body;
          // formState.backGroundType = res.body?.[0]?.value;
        }
      } catch (e) {
        backgroundImgTypeOptions.value = [];
        console.log(e);
      }
    };

    // 整理保存到vuex的参数
    const formatParams = () => {
      return {
        intro: formState.capacityDesc || '',
        name: formState.capacityName || '',
        priNetCallAddr: formState.bAddress?.map((item) => item.bAddress).join(',') || '',
        providerAccount: formState.capacityProviderAccount || '',
        providerEmail: formState.capacityProviderEmail || '',
        providerId: formState.capacityProvider || '',
        providerPhone: formState.capacityProviderPhone || '',
        pubNetCallAddr: formState.oAddress?.map((item) => item.oAddress).join(',') || '',
        shelvePubNet: formState.isGoOnline,
        type: formState.capacityCategory || '',
        backGroundType: formState.backGroundType || '',
      };
    };

    // 将数据同步到store中
    const updateToStore = () => {
      const params = formatParams();
      store.commit('UPDATE_APPINFO_BASEINFO', params);
    };

    // 处理保存草稿
    const handleSaveDraft = () => {
      if (!formState.capacityName) {
        message.error('该能力未能保存为草稿，请先补充能力名称');
        return;
      }
      updateToStore();
    };

    // 跳转到下一步
    const goToNextStep = () => {
      isDraft.value = false; // 启动校验
      formRef.value?.validate().then(
        () => {
          updateToStore();
          emit('toNext');
        },
        (e) => {
          throw e;
        }
      );
    };

    // 保存草稿
    const toSaveDraft = () => {
      isDraft.value = true; // 关闭校验
      if (!formState.capacityName) {
        message.error('该能力未能保存为草稿，请先补充能力名称');
        return;
      }
      handleSaveDraft();
      emit('saveDraft');
    };

    // 取消
    const cancel = () => {
      emit('handleConfirm', '/capacity-management');
    };

    // 点击添加b域网址和公网地址
    const handleAdd = (type) => {
      if (type === 'b') {
        const lastObj = formState.bAddress[formState.bAddress.length - 1]; // 找到最后一个数据
        // 如果是最后一个，就将最后一个添加按钮展示并且禁用
        if (formState.bAddress.length === bMaxNum.value) {
          lastObj.addShow = true;
          return;
        }
        // 如果最后一条没有填写，就不能添加新的
        if (!lastObj.bAddress) {
          return;
        }
        const newData = {
          key: `${count.value}`,
          bAddress: '',
          addShow: true,
        };
        lastObj.addShow = false;
        formState.bAddress.push(newData);
      } else {
        const lastObj = formState.oAddress[formState.oAddress.length - 1]; // 找到最后一个，将最后一条的加号隐藏并且禁用
        if (formState.oAddress.length === oMaxNum.value) {
          lastObj.addShow = true;
          return;
        }
        const newData = {
          key: `${ocount.value}`,
          oAddress: '',
          addShow: true,
        };
        lastObj.addShow = false;
        formState.oAddress.push(newData);
      }
    };

    // 点击添加移除b域网址
    const handleRemove = (key: string, type) => {
      if (type === 'b') {
        formState.bAddress = formState.bAddress.filter((item) => item.key !== key);
        const lastObj = formState.bAddress[formState.bAddress.length - 1]; // 最后一行永远都是可以添加的
        lastObj.addShow = true;
        formState.bAddress = cloneDeep(formState.bAddress);
      } else {
        formState.oAddress = formState.oAddress.filter((item) => item.key !== key);
        const lastObj = formState.oAddress[formState.oAddress.length - 1]; // 最后一行永远都是可以添加的
        lastObj.addShow = true;
        formState.oAddress = cloneDeep(formState.oAddress);
      }
    };

    // 能力类型改变的时候
    const handleCapacityCategoryChange = (value) => {
      // 编辑的时候需要对更改进行判断
      if (store.state.appInfo.material?.materials?.[0]) {
        Modal.confirm({
          title: `确定修改能力类别吗？`,
          class: 'delete-edge-serve-modal',
          content: '修改能力类别后，步骤二的功能演示素材将被清空重置，步骤三的场景配图将自动匹配替换，请谨慎操作',
          centered: true,
          onOk: () => {
            // 如果新的值与旧的值不相等，清空已经上传的媒体文件
            if (lastChoose.value !== value) {
              if (store.state.appInfo.material?.materials.length) {
                store.commit('UPDATE_APPINFO_METERIAL', {
                  ...store.state.appInfo.material,
                  type: MEDIA_TYPE_MAP.get(value)?.[0],
                  materials: [],
                });
              }
            }
          },
          onCancel: () => {
            formState.capacityCategory = lastChoose.value;
          },
          width: '400px',
          okText: '确认修改',
          cancelText: '取消',
          zIndex: 100000,
          icon: () => createVNode(ExclamationCircleOutlined),
        });
      }
    };
    // 提前获取第2步媒体文件内容
    const getPriview = async (md5IdList: string[], mediaType) => {
      try {
        const newListIds = md5IdList.filter((item) => item);
        if (!isEdit.value || !appInfo.value.material || mediaType === MEDIA_ENUM.TEXT || !newListIds?.length) {
          store.commit('UPDATE_DEFAULT_MEDIA_LIST', []);
          return;
        }
        const requestList = newListIds?.map((item, index) =>
          requestBlob('/aiipweb/om-service/os/getObject', {
            method: 'GET',
            data: {
              object: item,
              category: '3',
            },
          }).then(async (res) => {
            const fileObj = {
              url: res || '',
              name: `file${index + 1}`,
              uid: index,
              status: 'done',
              thumbUrl: res,
              response: { body: item },
            } as any;
            if (mediaType === MEDIA_ENUM.VIDEO) {
              const videoInfo = await getVideoInfo(res);
              const obj = (await getVideoPosterInfo(videoInfo)) as any;
              fileObj.thumbUrl = obj?.posterUrl;
            }
            if (mediaType === MEDIA_ENUM.AUDIO) {
              const infoArr = item?.split('_');
              fileObj.name = infoArr?.[1] || `file${index + 1}`;
              fileObj.uid = infoArr?.[0];
            }
            return fileObj;
          })
        );
        const defaultFileList = (await Promise.all(requestList)) as any;
        store.commit('UPDATE_DEFAULT_MEDIA_LIST', defaultFileList);
      } catch (error) {
        console.log(error);
      }
    };
    // 获取图片
    const getImageUrl = async () => {
      imageUrl.value = await getPicUrl(formState.capacityCategory, 'step1.png');
    };

    watch(
      () => props.isEdit,
      (newValue) => {
        if (newValue) {
          Object.assign(formState, initFormStateData(appInfo?.value?.baseInfo || {}));
          capacityNameInputDisabled.value = appInfo?.value?.abilityId ? true : false;
          capacityCategorySelectDisabled.value = appInfo?.value?.abilityId ? true : false;
          getPriview(appInfo?.value?.material?.materials, appInfo?.value?.material?.type || MEDIA_ENUM.IMG);
          nextTick(() => {
            formRef.value?.validateFields(['capacityDesc']);
          });
        }
      }
    );

    watch(
      () => formState.capacityCategory,
      (newValue, oldValue) => {
        // 能力类别发生了变化
        if (newValue !== oldValue) {
          getImageUrl(); // 需要重新变更右侧示意图
          lastChoose.value = oldValue; // 将旧的值保存下来
        }
      }
    );

    defineExpose({
      updateToStore,
    });

    // 获取运营提供方
    getCapacityProvider();
    // 获取能力类别选项
    getCapTypeOptions('category');
    getBackImgOptions('backGroundType');
    getImageUrl();
    return {
      formRef, // form的ref
      rules, // 表单校验规则
      labelCol,
      wrapperCol,
      formState, // 表单数据
      capacityProviderOptions, // 能力提供方枚举项
      capacityCategoryOptions, // 能力类别枚举项
      backgroundImgTypeOptions, // 背景图类别枚举选项
      bMaxNum,
      oMaxNum,
      goToNextStep,
      toSaveDraft,
      cancel,
      handleAdd,
      handleRemove,
      validateBAddress,
      validateOAddress,
      bHelpText,
      oHelpText,
      isBAddressError,
      isOAddressError,
      handleCapacityCategoryChange,
      IS_ONLINE_ENUM,
      updateToStore,
      capacityNameInputDisabled,
      capacityCategorySelectDisabled,
      isInnerNetWork, // 是否为办公网或者互联网
      imageUrl,
      descError,
      descErrorMessage,
    };
  },
});
</script>

<style lang="less" scoped>
.capacity-content-container {
  box-sizing: border-box;
  width: 100%;
  background-color: #fff;
  margin-top: 20px;
  padding: 20px;
}
// 头部标题
.content-header {
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 16px;
  .content-header-title {
    font-size: 16px;
    font-weight: 500;
    color: #121f2c;
    line-height: 24px;
  }
}
.content-box {
  box-sizing: border-box;
  display: flex;
  height: 100%;
  .content-form-box {
    // background-color: red;
    flex: 0 0 70.4%;
    padding-right: 20px;
    border-right: 2px solid #efefef;
    .form-title {
      font-weight: 500;
      color: #121f2c;
      line-height: 24px;
      position: relative;
      font-size: 14px;
      margin-left: 12px;
      margin-bottom: 24px;
    }
    .form-title::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: #0082ff;
      position: absolute;
      left: -12px;
      top: 4px;
    }
    .form-box {
      margin-top: 14px;
      .field-item {
        margin-bottom: 32px;
      }
      .select-box {
        /deep/ .ant-form-item-control-input {
          width: 327px;
        }
      }

      .input-box {
        /deep/ .ant-form-item-control-input {
          width: 327px;
        }
      }

      .textarea-box {
        /deep/ .ant-form-item-control-input {
          width: 528px;
        }
        .form-desc {
          position: relative;
          /deep/ textarea {
            resize: none;
          }
          .desc-tips {
            position: absolute;
            margin-top: 5px;
            height: 18px;
            font-size: 14px;
            font-weight: 400;
            color: #ff454d;
            line-height: 18px;
          }
          .desc-count {
            position: absolute;
            right: 0;
            margin-top: 5px;
            height: 18px;
            font-size: 14px;
            font-weight: 400;
            color: #a0a6ab;
            line-height: 18px;
            .desc-error {
              color: #ff454d;
            }
            .desc-num {
              margin-right: 4px;
            }
            .desc-count {
              margin-left: 2px;
            }
          }
        }
      }
      /deep/ .ant-form-item-explain {
        font-size: 12px;
        color: #a0a6ab;
      }

      .fix-position {
        margin-top: -8px;
      }
      .bAddress-box {
        /deep/ .ant-form-item-explain-connected {
          display: none !important;
          min-height: 0;
          height: 0;
        }
        .ant-form-item {
          margin-bottom: 0;
        }
        .b-btn-box {
          box-sizing: border-box;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          /deep/ .ant-btn {
            width: 32px;
            height: 32px;
            border-radius: 0;
            .anticon-plus {
              transform: translateX(-6px);
            }
            .anticon-minus {
              transform: translateX(-6px);
            }
          }
          /deep/.ant-btn[disabled] {
            background: transparent;
          }
        }
        .add-btn-box {
          border-left: 1px solid #cbcfd2;
          border-top: 1px solid #cbcfd2;
          border-bottom: 1px solid #cbcfd2;
        }

        .remove-btn-box {
          border-left: 1px solid #cbcfd2;
          border-top: 1px solid #cbcfd2;
          border-bottom: 1px solid #cbcfd2;
        }

        .input-box {
          input {
            // border-top: none;
            border-radius: 0;
            width: 327px;
          }
        }

        .b-explain {
          font-size: 12px;
          color: #a0a6ab;
        }
        .b-error {
          font-size: 12px;
          color: red;
        }
      }

      .no-label {
        /deep/ .ant-form-item-label > label {
          visibility: hidden;
        }
      }
    }
    .btn-box {
      margin-top: 60px;
      display: flex;
      margin-bottom: 200px;
      .block-one {
        flex: 0 0 20.83333333%;
        width: 20.83333333%;
      }
      .pre-btn {
        width: 32px;
        margin-right: 8px;
        .anticon-left {
          transform: translateX(-7px);
        }
      }
      .next-btn {
        width: 120px;
        margin-right: 8px;
      }
      .save-btn {
        width: 88px;
        margin-right: 8px;
      }
      .cancel-btn {
        width: 64px;
      }
    }
  }
  .show-box {
    box-sizing: border-box;
    padding-left: 20px;
    width: 100%;
    .show-header-box {
      font-weight: 500;
      color: #121f2c;
      height: 24px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }
    .show-header-box::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: #0082ff;
      margin-right: 12px;
    }
    .show-img-box {
      width: 100%;
      height: 100%;
      img {
        width: 100%;
      }
    }
  }
}
</style>
<style lang="less">
.delete-edge-serve-modal {
  background: #121f2c;
  .ant-modal-body {
    .ant-modal-confirm-body > .anticon {
      color: #ff454d !important;
    }
    padding: 30px 33px 24px 33px;
    .ant-modal-confirm-title {
      font-size: 16px;
      color: #121f2c;
    }
    .ant-modal-confirm-content {
      margin-top: 16px;
      color: #606972;
    }
    .ant-modal-confirm-btns {
      display: flex;
      flex-direction: row-reverse;
      .ant-btn-primary {
        background-color: #fff;
        width: 96px;
        height: 32px;
        border: 1px solid #ff454d;
        margin-right: 8px;
        color: #ff454d;
        text-shadow: 0 -1px 0 rgb(255 69 77 / 12%);
      }
      .ant-btn:active {
        color: #555555;
        background: #fff;
        border-color: #d9d9d9;
      }
      .ant-btn:hover {
        color: #555555;
        background: #fff;
        border-color: #d9d9d9;
      }
      .ant-btn-primary:active {
        background-color: #ff454d;
        color: #fff;
        border: 1px solid #ff454d;
      }
      .ant-btn-primary:hover {
        background-color: #ff454d;
        color: #fff;
        border: 1px solid #ff454d;
      }
    }
  }
}
</style>
