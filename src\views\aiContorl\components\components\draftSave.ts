import { message } from 'ant-design-vue';
import request from '@/request';
import getSaveOPtions from './optionsSave';
export function useDraftSaveTask(store, optionsUse) {
  //step = 1
  function draftSaveTask() {
    return new Promise((resolve, reject) => {
      const options: any = getSaveOPtions(store, optionsUse);

      if (options.baseInfo.name !== '') {
        store.commit('openGlobalLoading');
        request('/aiipweb/om-service/draft/addOrSave', {
          method: 'POST',
          data: options,
        }).then((res: any) => {
          store.commit('closeGlobalLoading');
          if (res.state === 'OK') {
            message.success('该能力已保存为草稿');
            store.commit('saveAppInfoId', res.body);
            resolve(res);
          } else if (res.state === 'ERROR') {
            if (res.errorMessage === '能力名称重复') {
              message.error('当前能力名称重复,请修改后再试');
            } else {
              message.error('该能力未能保存为草稿,请稍后再试');
            }
            reject(res);
          }
        });
      } else {
        message.error('当前能力名称未填写,请填写后重试');
        store.commit('closeSaveDraft');
      }
    });
  }
  return { draftSaveTask };
}
