<template>
  <div>
    <div class="form-title">
      <a-form class="form-title-content" :colon="false">
        <a-form-item class="form-title-desc" label="问题描述" :rules="[{ required: true }]"> </a-form-item>
        <a-form-item class="form-title-answer" label="问题解答" :rules="[{ required: true }]"> </a-form-item>
      </a-form>
    </div>
    <div class="disabled-box">
      <template v-if="resultData.length > 0">
        <div v-for="(item, index) in resultData" :key="index" class="disabled-box-content" :style="{ top: `-${index}px` }">
          <div class="sub" @click="handleSubResult(index)">
            <jt-icon type="iconsubtract-line" />
          </div>
          <a-tooltip :visible="verifyKey(item.key) && currentIndex === index">
            <template #title>请输入8-16个字符</template>
            <a-input class="form-input-key" :class="verifyKey(item.key) ? 'border-red' : ''" v-model:value="item.key" :maxlength="16" @blur="handleBlur" @focus="handleFocus" @mouseenter="handleEnter($event, index)" @mouseleave="handleLeave" @change="handleInputChange" />
          </a-tooltip>
          <a-tooltip :visible="verifyValue(item.value) && currentIndex === index">
            <template #title>请输入15-70个字符</template>
            <a-input class="form-input-value" :class="verifyValue(item.value) ? 'border-red' : ''" v-model:value="item.value" :maxlength="70" @blur="handleBlur" @focus="handleFocus" @mouseenter="handleEnter($event, index)" @mouseleave="handleLeave" @change="handleInputChange" />
          </a-tooltip>
        </div>
      </template>
    </div>

    <div class="form-box" :style="{ top: `-${resultData.length}px` }">
      <div class="add" :class="valid && resultData.length < 9 ? '' : 'add-disabled'" @click="handleAddData">
        <jt-icon type="icontianjia" />
      </div>
      <div class="sub" :class="resultData.length === 0 ? 'sub-disabled' : ''" @click="handleSubData">
        <jt-icon type="iconsubtract-line" />
      </div>
      <a-form ref="appForm" :model="formData" :layout="'vertical'" autocomplete="off" class="form-content">
        <a-form-item
          name="key"
          :rules="[
            {
              required: true,
              trigger: 'change',
              validator: validateKey,
            },
          ]"
        >
          <a-input class="form-input-key" placeholder="请输入8-16个字符" v-model:value="formData.key" :maxlength="16" />
        </a-form-item>

        <a-form-item
          name="value"
          :rules="[
            {
              required: true,
              trigger: 'change',
              validator: validateValue,
            },
          ]"
        >
          <a-input class="form-input-value" placeholder="请输入15-70个字符" v-model:value="formData.value" :maxlength="70" />
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, watchEffect, defineEmits, defineExpose, defineProps, nextTick } from 'vue';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Array,
    default() {
      return [];
    },
  },
});
const emits = defineEmits(['validChange']);
const currentIndex = ref(-1); // 设置 tooltips显隐的index
const resultValid = ref(true); // 已有数据 的 校验结果
// 数据源
interface resultType {
  key: string;
  value: string;
}
const resultData: resultType[] = reactive([]);
const formData = reactive({
  key: '',
  value: '',
});
// 最后编辑框校验状态标志位
const validKey = ref(false);
const validValue = ref(false);
const appForm = ref();
const valid = ref(false);
// 主动触发表单验证
const formValid = () => {
  appForm.value.validateFields(['key', 'value']);
};
// 通过遍历已有数据，确认 已有数据的最终校验状态
const handleInputChange = () => {
  resultValid.value = true;
  for (let i = 0; i < resultData.length; i++) {
    if (resultData[i].key.length < 8 || resultData[i].key.length > 16 || resultData[i].value.length < 15 || resultData[i].value.length > 70) {
      resultValid.value = false;
      break;
    }
  }
};
// 监听 是否是编辑状态，回填数据
watch(
  () => props.isEdit,
  (newValue) => {
    if (newValue) {
      if (props.editData.length > 0) {
        const tempData = props.editData.map((item: any) => {
          return {
            key: item.question,
            value: item.answer,
          };
        });
        if (tempData.length >= 10) {
          formData.key = tempData[9].key;
          formData.value = tempData[9].value;
          const temp = tempData.slice(0, 9);
          resultData.push(...temp);
        } else {
          formData.key = tempData[tempData.length - 1].key;
          formData.value = tempData[tempData.length - 1].value;
          const temp = tempData.slice(0, tempData.length - 1);
          resultData.push(...temp);
        }
        // 校验各种状态
        if (formData.key.length < 8) {
          validKey.value = false;
        } else {
          validKey.value = true;
        }
        if (formData.value.length < 15) {
          validValue.value = false;
        } else {
          validValue.value = true;
        }
        handleInputChange();
        nextTick(() => {
          formValid();
        });
      }
    }
  },
  {
    immediate: true,
  }
);
// 控制 tooltips显隐的策略
const handleBlur = () => {
  currentIndex.value = -1;
};
const handleFocus = () => {
  currentIndex.value = -1;
};
const handleEnter = (event, index) => {
  currentIndex.value = index;
};
const handleLeave = () => {
  currentIndex.value = -1;
};
// 已有问题框校验，用来显示红框 与 tooltips的显隐
const verifyKey = (value) => {
  if (value.length < 8 || value.length > 16) {
    return true;
  } else {
    return false;
  }
};
const verifyValue = (value) => {
  if (value.length < 15 || value.length > 70) {
    return true;
  } else {
    return false;
  }
};

// 问题框校验
const validateKey = (rule, value) => {
  if (value.length < 8 || value.length > 16) {
    validKey.value = false;
    return Promise.reject('请输入8-16个字符');
  } else {
    validKey.value = true;
    return Promise.resolve();
  }
};
// 回答框校验
const validateValue = (rule, value) => {
  if (value.length < 15 || value.length > 70) {
    validValue.value = false;
    return Promise.reject('请输入15-70个字符');
  } else {
    validValue.value = true;
    return Promise.resolve();
  }
};
// 向父组件发射 校验状态；最下面的编辑框 与 已有数据编辑框的综合校验；
watchEffect(() => {
  if (validKey.value && validValue.value) {
    valid.value = true;
    if (resultValid.value) {
      emits('validChange', true);
    } else {
      emits('validChange', false);
    }
  } else {
    valid.value = false;
    emits('validChange', false);
  }
});
// 已有数据消除；更新已有数据的 最终校验状态；
const handleSubResult = (index) => {
  resultData.splice(index, 1);
  handleInputChange();
};
// 正在编辑的数据消除；更新已有数据的 最终校验状态；
const handleSubData = () => {
  if (resultData.length === 0) return;
  const temp = resultData.pop() as resultType;
  handleInputChange();
  formData.key = temp.key;
  formData.value = temp.value;
  validKey.value = true;
  validValue.value = true;
  appForm.value.validateFields(['key', 'value']);
};
// 正在编辑的数据添加；更新已有数据的 最终校验状态；
const handleAddData = () => {
  if (!valid.value || resultData.length >= 9) return;
  resultData.push({ ...formData });
  handleInputChange();
  formData.key = '';
  formData.value = '';
  validKey.value = false;
  validValue.value = false;
};

// 后面要暴露出 resultData 与 formData，供父组件使用；用发射的事件确定下一步是否可以点击；
// 在下一步中 通过这个暴露的数据 获取最终数据；
defineExpose({
  resultData,
  formData,
  formValid,
});
</script>

<style lang="less" scoped>
.form-title {
  margin-bottom: 7px;
  padding-left: 82px;
  &-content {
    display: flex;
    align-items: center;
    height: 20px;
    .form-title-desc {
      width: 240px;
      margin-bottom: 0;
    }
    .form-title-answer {
      margin-bottom: 0;
    }
  }
}

.disabled-box {
  padding-left: 43px;
  &-content {
    position: relative;
    display: flex;
    align-items: center;
    height: 32px;
    padding-right: 26px;
    .sub {
      width: 32px;
      height: 32px;
      position: relative;
      left: -1px;
      border: 1px solid #d6d9db;
      text-align: center;
      line-height: 32px;
      color: #606972;
      cursor: pointer;
      &:hover {
        color: #0082ff;
        border-color: #0082ff;
        z-index: 10;
      }
    }
    .form-input-key {
      width: 240px;
      height: 32px;
      position: relative;
      left: -2px;
      &:hover {
        z-index: 10;
      }
      &:focus {
        z-index: 10;
      }
    }
    .form-input-value {
      flex: 1;
      height: 32px;
      position: relative;
      left: -3px;
      &:hover {
        z-index: 10;
      }
      &:focus {
        z-index: 10;
      }
    }
    .border-red {
      border-color: #ff4d4f;
      z-index: 20;
      &:focus {
        z-index: 20;
        border-color: #ff7875;
        box-shadow: 0 0 0 2px rgb(255 77 79 / 20%);
      }
    }
  }
}

.form-box {
  position: relative;
  padding-left: 11px;
  display: flex;
  padding-right: 26px;
  .add,
  .sub {
    width: 32px;
    height: 32px;
    border: 1px solid #d6d9db;
    text-align: center;
    line-height: 32px;
    color: #606972;
    cursor: pointer;
    &:hover {
      color: #0082ff;
      border-color: #0082ff;
      z-index: 10;
    }
  }
  .sub {
    position: relative;
    left: -1px;
  }
  .add-disabled,
  .sub-disabled {
    color: #eaeaea;
    border-color: #d6d9db;
    background-color: #ffffff;
    cursor: not-allowed;
    &:hover {
      color: #eaeaea;
      border-color: #d6d9db;
    }
  }

  .form-content {
    flex: 1;
    display: flex;
    align-items: center;
    position: relative;
    left: -2px;
    .form-input-key {
      width: 240px;
      height: 32px;
      &:hover {
        z-index: 10;
      }
      &:focus {
        z-index: 10;
      }
    }
    .form-input-value {
      height: 32px;
      position: relative;
      left: -1px;
      &:hover {
        z-index: 10;
      }
      &:focus {
        z-index: 10;
      }
    }
    .ant-form-item:nth-of-type(2) {
      flex: 1;
    }
    /deep/ .ant-form-item-has-error {
      z-index: 20;
    }
  }
}
</style>
