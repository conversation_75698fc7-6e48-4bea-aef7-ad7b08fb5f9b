import { ref, Ref } from 'vue';
import request, { Options } from '@/request';
import { AxiosResponse } from 'axios';

type RequestFunction = (url: string, options: Options) => Promise<AxiosResponse<any>>;

const loading = ref(false);
export const useLoadingRequest = (): [RequestFunction, Ref<boolean>] => {
  const requestFunction: RequestFunction = async (url, options) => {
    loading.value = true;
    const res = await request(url, options);
    loading.value = false;
    return res;
  };
  return [requestFunction, loading];
};
