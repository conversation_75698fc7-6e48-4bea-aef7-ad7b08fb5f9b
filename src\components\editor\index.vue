<template>
  <div>
    <div id="wang-editor"></div>
    <upload-modal :visible="modalVisible" :centerType="centerType" @ok="handleModalOk" @cancel="handleModalCancel"></upload-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, toRefs, onMounted, PropType, watch, computed } from 'vue';
import { useStore } from 'vuex';
import wangEditor from 'wangeditor';
import uploadModal from './uploadModal.vue';
import UploadMenu, { uploadMenuKey } from '@/utils/upload/wangEditorUpload.js';

// 富文本编辑器增加上传文件入口
wangEditor.registerMenu(uploadMenuKey, UploadMenu);

export default defineComponent({
  components: {
    uploadModal,
  },
  props: {
    initParams: {
      type: Object as PropType<{ uploadImgShowBase64?: boolean; uploadFileName?: string; uploadImgServer?: string; uploadImgMaxLength?: number; uploadImgTimeout?: number; uploadImgHeaders?: Record<string, unknown> }>,
    },
    editorContent: String,
    centerType: {
      type: String,
      required: true,
    },
  },
  emits: ['change'],
  setup(props, context) {
    const modalVisible = ref(false);
    let editor: any = null;
    const store = useStore();
    let pageHeadList: any[] = []; // 富文本编辑器内部标题、目录变化
    let content: any = null; // 富文本编辑器内容 html
    let pageContentIndex: any = null; // 富文本编辑器内容 text
    const refreshToken = computed(() => store.state.refreshToken);
    // 上传默认参数
    const defaultUploadParams = {
      uploadImgShowBase64: false,
      uploadFileName: 'file',
      uploadImgServer: './common_help/image/add',
      uploadImgMaxLength: 10000, // 图片上传数量
      uploadImgTimeout: 20 * 1000,
      uploadImgHeaders: {
        Authorization: 'Bearer ' + refreshToken.value,
      },
    };
    const { initParams, editorContent } = toRefs(props);
    const { uploadImgShowBase64, uploadFileName, uploadImgServer, uploadImgMaxLength, uploadImgTimeout, uploadImgHeaders } = { ...defaultUploadParams, ...initParams.value };

    // 富文本编辑器初始化
    const initWangEditor = () => {
      editor = new wangEditor('#wang-editor');
      editor.uploadBtnClick = handleUploadModalOpen;
      editor.config.uploadImgShowBase64 = uploadImgShowBase64;
      editor.config.uploadFileName = uploadFileName;
      editor.config.uploadImgServer = uploadImgServer;
      editor.config.uploadImgMaxLength = uploadImgMaxLength;
      editor.config.uploadImgTimeout = uploadImgTimeout;
      editor.config.uploadImgHeaders = uploadImgHeaders;
      // 内容变化
      editor.config.onchange = function (html) {
        content = html == ' ' ? undefined : html; // wangeditor的bug删除时会返回空字符串
        const txt = editor.txt.text();
        pageContentIndex = txt == ' ' ? undefined : txt;
        context.emit('change', {
          content,
          pageContentIndex,
          pageHeadList,
        });
      };
      // 目录变化
      editor.config.onCatalogChange = function (headList) {
        pageHeadList = headList;
        context.emit('change', {
          content,
          pageContentIndex,
          pageHeadList,
        });
      };
      editor.config.uploadImgHooks = {
        customInsert: function (insertImgFn: any, result: any) {
          insertImgFn(result.body);
        },
      };
      editor.config.zIndex = 99;
      editor.create();
    };

    // 获取编辑器内容
    const getPageContent = () => {
      if (editorContent.value !== editor.txt.html()) {
        editor.txt.html(editorContent.value);
      }
    };

    watch(
      () => editorContent,
      () => {
        getPageContent();
      },
      { deep: true }
    );
    // 由于keyclock在定时刷新，Authorization有可能失效，这里也进行更新
    watch(
      () => refreshToken,
      () => {
        editor.config.uploadImgHeaders = {
          Authorization: 'Bearer ' + refreshToken.value,
        };
      },
      { deep: true }
    );

    // 打开文件上传modal
    const handleUploadModalOpen = () => {
      modalVisible.value = true;
    };

    // 上传文件确认操作
    const handleModalOk = (uploadParams) => {
      modalVisible.value = false;
      const { downloadUrl, fileName } = uploadParams;
      editor.cmd.do('insertHTML', `<a href="${downloadUrl}" target="_blank" download>${fileName}</a>`);
    };

    // 上传文件取消操作
    const handleModalCancel = () => {
      modalVisible.value = false;
    };

    onMounted(() => {
      initWangEditor();
    });

    return {
      modalVisible,
      handleUploadModalOpen,
      handleModalOk,
      handleModalCancel,
    };
  },
});
</script>
<style lang="less" scoped>
#wang-editor {
  /deep/ .w-e-text-container {
    ul {
      list-style: disc;
    }
    ol {
      list-style: decimal;
    }
  }
}
</style>
