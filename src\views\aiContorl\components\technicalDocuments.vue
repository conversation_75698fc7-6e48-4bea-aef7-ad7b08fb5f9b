<template>
  <div class="technicalDocuments">
    <div class="technicalDocuments-left" style="width: 809px">
      <div class="technicalDocuments-title">对应技术文档</div>
      <div class="technicalDocuments-radio">
        <span style="margin-right: 20px"><span style="color: red">*</span> 是否需要技术文档</span>
        <a-radio-group v-model:value="value">
          <a-radio :value="true">是</a-radio>
          <a-radio :value="false">否</a-radio>
        </a-radio-group>
      </div>
      <template v-if="value">
        <div class="technicalDocuments-tech">
          <div class="technicalDocuments-tech-top">
            <div><span style="color: red">* </span> 技术文档</div>
            <div>请上传文件或在下方编辑</div>
          </div>
          <Upload @callBackMethod="content"></Upload>
        </div>
        <div style="margin: 20px 20px 0 196px">
          <div :class="['myEditor', { err: editorErr }]" style="border: 1px solid #ccc; z-index: 999999">
            <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
            <Editor style="height: 300px; overflow: auto" v-model="valueHtml" :defaultConfig="editorConfig" :mode="mode" @onCreated="handleCreated" />
          </div>
          <div class="errIntro" v-show="editorErr">请输入</div>
        </div>
      </template>

      <div class="technicalDocuments-btn" style="margin: 20px 0 0 196px">
        <div class="iconfanhui" @click="toPre">
          <jt-icon type="iconfanhui" style="font-size: 20px; color: #7f828f" />
        </div>
        <a-modal :visible="shelfVal" :closable="false" :width="388" :footer="false" class="modal">
          <div class="shelf">
            <div class="quitbox">
              <div class="quitbox1">
                <jt-icon type="iconerror-fill" />
              </div>

              <div class="quitbox2" v-if="capacityButton === '1' || capacityButton === undefined">
                <p>确认<span>上架</span>该能力吗？</p>
                <p>
                  请先查看 <span @click="warNing">预览</span> 效果，确保无误后再上架。<br />
                  确定上架后将实时上架到开放能力，能力状态变为“已上架”。
                </p>
              </div>
              <div class="quitbox2" v-else>
                <p>确认<span>同步</span>该能力吗？</p>
                <p>
                  请先查看 <span @click="warNing">预览</span> 效果，确保无误后再同步。<br />
                  确定同步后将实时同步到开放能力，能力状态仍为“已上架”。
                </p>
              </div>

              <div class="quitbox3">
                <template v-if="capacityButton === '1' || capacityButton === undefined">
                  <a-button key="submit" @click="cancel"> 取消 </a-button>
                  <a-button key="back" @click="handleOk"> 确定上架 </a-button>
                </template>
                <template v-else>
                  <a-button key="submit" @click="cancel"> 取消 </a-button>
                  <a-button key="back" @click="handleOk"> 确定同步 </a-button>
                </template>
              </div>
            </div>
          </div>
          <div class="shelf2"></div>
        </a-modal>
        <a-button v-if="capacityButton === '1' || capacityButton === undefined" class="next" type="primary" @click="putOn">上架</a-button>
        <a-button v-else class="next" type="primary" @click="putOn">同步</a-button>
        <a-button class="save" type="default" @click="saveDraft">保存</a-button>
      </div>
    </div>
    <div class="technicalDocuments-right">
      <div class="technicalDocuments-right-title">
        <p class="technicalDocuments-right-title-box"></p>
        <p class="technicalDocuments-right-title-text">入驻能力页示意</p>
      </div>
      <div class="abilityRecommend-right-img">
        <img style="width: 100%" src="../../../assets/images/aiControl/step6.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineProps, shallowRef, onBeforeUnmount, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import { usePutSyancTask } from './components/putSyanc';
import { message } from 'ant-design-vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import Upload from './components/upload.vue';
import '@wangeditor/editor/dist/css/style.css';
import { getEnvConfig } from '@/config';
const emit = defineEmits(['toPre', 'saveDraft']);
const value = ref<boolean>(true);
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  appInfo: {
    type: Object,
    default() {
      return {};
    },
  },
});
//返回上一步
const toPre = () => {
  emit('toPre');
};
const route = useRoute();
const shelfVal = ref<boolean>(false);
const router = useRouter();
const store = useStore();
const capacityButton = ref(route.query.button);
const optionsUse = ref(false);
const { putSyancTask } = usePutSyancTask(store, router, capacityButton, optionsUse);
const mode = 'default';

const editorErr = ref(false);
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();

// 内容 HTML
const valueHtml = ref();
const content = (res) => {
  const temp = ref();
  const config = getEnvConfig('CAPABILITY_INNER_VERSION') === 'true';
  console.log(config);

  if (config) {
    temp.value = res;
  } else {
    temp.value = res.replaceAll('img src="/aiipweb', 'img src="./aiipweb');
  }
  console.log(temp.value);

  const editor = editorRef.value;
  editor.clear();
  editor.dangerouslyInsertHtml(temp.value);
};

const toolbarConfig = {
  excludeKeys: ['italic', 'group-more-style', 'group-image', 'group-video', 'group-link', 'delIndent', 'insertLink', 'emotion'],
};
const editorConfig = { placeholder: '请输入内容...', MENU_CONF: {} };

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (!editor) {
    return;
  }
  editor.destroy();
});
const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

const warNing = () => {
  message.warning('下期开放');
};
const cancel = () => {
  shelfVal.value = false;
};
//上架或同步
const putOn = () => {
  shelfVal.value = true;
};
watch(
  () => props.isEdit,
  (newValue) => {
    if (newValue) {
      valueHtml.value = props.appInfo.techDoc.content;

      value.value = props.appInfo.techDoc.needTechDoc;
    }
  },
  {
    immediate: true,
  }
);
watch(
  () => valueHtml.value,

  (newValue) => {
    let str = newValue ? newValue.replaceAll('<p>', '').replaceAll('<br>', '').replaceAll('</p>', '') : '';
    if (str.trim() != '') {
      editorErr.value = false;
    } else {
      // editorErr.value = true;
    }
  }
);
const technicalDocuments = () => {
  return new Promise((resolve: any, reject) => {
    let str = valueHtml.value ? valueHtml.value.replaceAll('<p>', '').replaceAll('<br>', '').replaceAll('</p>', '') : '';
    if (str.trim() != '') {
      editorErr.value = false;
      resolve();
    } else {
      const editor = editorRef.value;
      if (editor == null) return;
      editor.focus();
      editorErr.value = true;
    }
  });
};
//上架或同步
const handleOk = async () => {
  if (value.value === false) {
    valueHtml.value = '';
  } else if (valueHtml.value == '' || valueHtml.value == '<p><br></p>') {
    shelfVal.value = false;
    message.error('请先填写技术文档');
    await technicalDocuments();
  }
  store.commit('technicalDocumentsContent', valueHtml.value);
  store.commit('technicalDocumentsNeedTechDoc', value.value);
  putSyancTask();
  shelfVal.value = false;
};
//保存为草稿
const saveDraft = () => {
  if (value.value === false) {
    valueHtml.value = '';
  }
  store.commit('technicalDocumentsContent', valueHtml.value);
  store.commit('technicalDocumentsNeedTechDoc', value.value);
  emit('saveDraft');
};
</script>

<style lang="less" scoped>
/deep/ .ant-modal-confirm .ant-modal-confirm-btns .ant-btn-primary {
  background: #ff7e00 !important;
  border-color: #ff7e00 !important;
}
.technicalDocuments {
  height: 1000px;
  background: #fff;
  margin-top: 20px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  &-title {
    height: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #121f2c;
    line-height: 24px;
  }
  &-radio {
    margin: 24px 0 40px 54px;
  }
  &-tech {
    width: 672px;
    display: flex;
    justify-content: space-between;
    margin-left: 110px;
    align-items: center;
    &-top {
      width: 240px;
      display: flex;
      justify-content: space-between;
    }
    .technicalDocuments-tech-upload {
      width: 100px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #cbcfd2;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
    }
  }
  &-btn {
    display: flex;
    width: 168px;
    justify-content: space-between;
    .iconfanhui {
      width: 32px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #d6d9db;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
  &-right {
    width: 435px;
    height: 900px;
    border-left: 1px solid #efefef;
    margin-top: 52px;
    padding: 0 0 0 20px;
    &-title {
      display: flex;
      align-items: center;
      &-box {
        width: 4px;
        height: 16px;
        background: #0082ff;
        margin-right: 8px;
      }
      &-text {
        height: 24px;
        font-size: 14px;
        font-weight: 500;
        color: #121f2c;
      }
    }
    &-img {
      width: 100%;
    }
  }
}
.modal {
  // position: relative;
  .shelf {
    width: 372px;
    height: 318px;
    border-radius: 2px;
    border: 1px solid #ffb766;
    position: absolute;
    left: 8px;
    top: 8px;
  }
  .shelf2 {
    width: 388px;
    height: 288px;
  }
}
/deep/.ant-modal-header {
  border: none;
}
/deep/.ant-modal-footer {
  border: none;
  padding: 0 32px 24px 0;
}
/deep/.ant-btn {
  margin-bottom: 0px;
}
.quitbox {
  .quitbox1 {
    display: flex;
    justify-content: center;
    margin-bottom: 37px;
    /deep/ svg {
      width: 68px;
      height: 64px;
      color: #ff7e00;
      margin-top: 20px;
    }
    /deep/.ant-btn:hover {
      border-color: none;
    }
  }
  .quitbox2 {
    padding: 0 32px;
    margin-bottom: 32px;
    p:nth-child(1) {
      font-size: 16px;
      font-weight: bold;
      font-weight: 500;
      color: #121f2c;
      line-height: 22px;
      margin-bottom: 16px;
      span {
        color: #ff7e00;
      }
    }
    p:nth-child(2),
    p:nth-child(3) {
      font-size: 14px;
      font-weight: 400;
      color: #606972;
      line-height: 24px;
      span {
        color: #0082ff;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }
  .quitbox3 {
    float: right;
    margin-right: 32px;
    .ant-btn:nth-child(2) {
      margin-left: 8px;
      background-color: #ff7e00;
      color: white;
      border: none;
    }
  }
}
/deep/ .myEditor {
  &.err {
    border-color: red !important;
  }
}
.errIntro {
  color: red;
}
</style>
