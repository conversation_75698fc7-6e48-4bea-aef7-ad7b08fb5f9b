<template>
  <div class="bread-crumb">
    <breadCrumb :need-confirm="true" :value="pathLink"></breadCrumb>
  </div>
  <Container>
    <container-item>
      <div class="fold">
        <div class="fold-div">
          <a-input class="search-input" placeholder="能力名称" v-model:value="data.searchText" @change="handleInput(data.searchText)">
            <template #prefix>
              <jt-icon type="iconsousuo" style="font-size: 18px" />
            </template>
          </a-input>
          <a-button @click="handleCreate" type="primary">
            <jt-icon type="icontianjia" style="font-size: 12px" />
            新建能力
          </a-button>
        </div>
        <a-tabs default-active-key="1" @change="callback">
          <a-tab-pane key="1" :tab="'能力列表' + `&nbsp;&nbsp;(${pagination.total})`">
            <a-table :loading="tableAttr(loading).loading" :locale="tableAttr(loading).locale" :columns="columns" :rowClassName="() => 'cus-row'" :data-source="dataSource" :pagination="false" @change="handleChange">
              <template #emptyText v-if="!loading">
                <empty
                  title="能力"
                  :showNoDataText="
                    showNoDataText({
                      searchText: data.searchText,
                      statusList: data.statusList,
                    })
                  "
                >
                  <template #description>
                    请立即
                    <a href="javascript:;" @click="handleCreate">新建能力</a>
                  </template>
                </empty>
              </template>
              <template #bodyCell="{ column, record }">
                <template v-if="column.key === 'name'">
                  <a-tooltip :title="record.name" placement="topLeft">
                    <div class="overflow-ellipsis">
                      <span class="user-name-text">
                        {{ record.name || '-' }}
                      </span>
                    </div>
                  </a-tooltip>
                </template>
                <template v-else-if="column.key === 'status'">
                  <div class="type">
                    <span class="circle2" :class="record.status == 1 ? 'green' : 'grey'"> </span>
                    <span class="user-name-text">
                      {{ statusTransfer(record.status) || '-' }}
                    </span>
                  </div>
                </template>
                <template v-else-if="column.key === 'description'">
                  <a-tooltip :title="record.description" placement="topLeft">
                    <div class="overflow-ellipsis">
                      <span class="user-name-text">
                        {{ record.description || '-' }}
                      </span>
                    </div>
                  </a-tooltip>
                </template>
                <template v-else-if="column.key === 'callAddress'">
                  <div class="info">
                    <jt-icon type="iconfile-copy" class="copy-hover" @click="copyContentB(record.providerInfo.providerAccount, record.providerInfo.providerPhone, record.providerInfo.providerEmail)"></jt-icon>
                    <a-tooltip placement="topLeft">
                      <template #title> {{ record.providerInfo.providerAccount || '-' }}/{{ record.providerInfo.providerPhone || '-' }}/{{ record.providerInfo.providerEmail || '-' }} </template>
                      <div style="display: flex" class="overflow-ellipsis">
                        <span class="overflow-ellipsis-pro copy-text">{{ record.providerInfo.providerAccount || '-' }}</span
                        >/ <span class="overflow-ellipsis-pro">{{ record.providerInfo.providerPhone || '-' }}</span
                        >/
                        <span class="overflow-ellipsis-pro">{{ record.providerInfo.providerEmail || '-' }}</span>
                      </div>
                    </a-tooltip>
                  </div>
                  <div class="info">
                    <jt-icon type="iconfile-copy" class="copy-hover" @click="copyContent(record.callAddress.priNetCallAddr, record.callAddress.pubNetCallAddr)"></jt-icon>
                    <a-tooltip placement="topLeft">
                      <template #title v-if="record.callAddress.priNetCallAddr || record.callAddress.pubNetCallAddr">
                        <ul v-if="record.callAddress.priNetCallAddr" style="list-style: none; padding-left: 0">
                          <li v-for="(i, index) in record.callAddress.priNetCallAddr.split(',')" :key="index">{{ i }}</li>
                        </ul>
                        <ul v-else style="list-style: none; padding-left: 0">
                          <li v-for="(i, index) in record.callAddress.pubNetCallAddr.split(',')" :key="index">{{ i }}</li>
                        </ul>
                      </template>
                      <template #title v-else> - </template>
                      <span class="overflow-ellipsis copy-text"> {{ record.callAddress.priNetCallAddr ? record.callAddress.priNetCallAddr : record.callAddress.pubNetCallAddr || '-' }} </span>
                    </a-tooltip>
                  </div>
                </template>
                <template v-else-if="column.key === 'button'">
                  <div class="active">
                    <a-tooltip>
                      <template #title v-if="statusTransfer(record.status) == '已上架' && record.url == null">能力正在研发中，暂不支持查看</template>
                      <a-button :disabled="statusTransfer(record.status) == '已上架' && record.url == null" size="large" type="link" @click="view(record)"
                        ><span :class="statusTransfer(record.status) == '已上架' && record.url == null ? 'disabled-span' : ''"><jt-icon :type="statusTransfer(record.status) == '已上架' ? 'iconeye' : 'iconbofangliang'" class="icon"></jt-icon>{{ statusTransfer(record.status) == '已上架' ? '查看' : '预览' }}</span></a-button
                      >
                    </a-tooltip>
                    <a-button size="large" type="link" @click="edit(record)"
                      ><span><jt-icon type="iconbianji" class="icon"></jt-icon>编辑</span></a-button
                    >
                    <a-button v-if="statusTransfer(record.status) == '已下架'" size="large" type="link" @click="shelve(record)"
                      ><span><jt-icon type="iconshangjia" class="icon"></jt-icon>上架</span></a-button
                    >
                    <a-button v-if="statusTransfer(record.status) == '已上架'" size="large" type="link" @click="unShelve(record)"
                      ><span><jt-icon type="iconxiajia" class="icon"></jt-icon>下架</span></a-button
                    >
                    <a-button v-if="statusTransfer(record.status) == '已下架'" size="large" type="link" @click="delEdge(record)">
                      <span><jt-icon type="iconshanchu1" class="icon"></jt-icon>删除能力</span>
                    </a-button>
                    <a-button v-if="statusTransfer(record.status) == '已上架'" size="large" type="link" @click="manage(record)">
                      <span><jt-icon type="iconbiaoqian" class="icon"></jt-icon>编辑标签</span>
                    </a-button>
                  </div>
                </template>
              </template>
            </a-table>
            <Pagination :total="pagination.total" :pageNum="pagination.pageNum" :pageSize="pagination.pageSize" :pageSizeOptions="[10, 20, 50, 100]" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
            <shelf :shelfVal="data.shelfVal" :shelfObj="data.shelfObj" @cancelModal="cancelModal"></shelf>
            <template v-if="data.manageVal"><manageModal :manageVal="data.manageVal" :manageObj="data.manageObj" @cancelModal="cancelModal"></manageModal></template>
          </a-tab-pane>

          <a-tab-pane key="2" :tab="'草稿列表' + `&nbsp;&nbsp;(${data.totalNum})`" force-render>
            <draftTab :searchText="data.searchText" :keyNum="data.key" :keyValue="data.keyValue" @totalNum="totalNum"></draftTab>
          </a-tab-pane>
        </a-tabs>
      </div>
    </container-item>
  </Container>
</template>

<script setup lang="ts">
import { reactive, ref, computed, createVNode, h } from 'vue';
import { useRouter } from 'vue-router';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import request from '@/request';
import { copyContent, copyContentB, openInNewTab, tableAttr, showNoDataText } from '@/utils/index';
import shelf from './components/shelf.vue';
import { Modal, message } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import draftTab from './components/draft.vue';
import breadCrumb from '@/components/breadCrumb.vue';
import manageModal from './components/manage.vue';
import empty from '@/components/empty.vue';

const pathLink = reactive([
  {
    name: '开放能力',
  },
  {
    name: '能力管理',
  },
]);
const data = reactive({
  searchText: '',
  shelfVal: false,
  shelfObj: {}, //上架数据
  manageVal: false,
  manageObj: {},
  // shelfOrDraft: '上架',
  totalNum: 0,
  desc: true,
  type: '', //类型
  statusList: [], //状态
  searchTextNum: true,
  priNetCallAddr: '',
  key: '1',
  keyValue: '',
  enumerationList: {}, //枚举值
});
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
});
const dataSource = ref<any[]>([]);
const loading = ref(false);
//草稿条数
const totalNum = (num) => {
  data.totalNum = num;
};
//表格数据
const getList = () => {
  data.searchTextNum = true;
  loading.value = true;
  const obj = {
    name: data.searchText,
    orderByModifyTimeDesc: data.desc,
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize,
    statusList: data.statusList,
    type: data.type,
  };
  request('/aiipweb/om-service/capability/queryList', {
    method: 'POST',
    data: obj,
  }).then((res: any) => {
    setTimeout(() => {
      loading.value = false;
    }, 500);
    if (res.state === 'OK') {
      dataSource.value = res.body.data;
      pagination.total = res.body.total;
      if (data.searchText != '' || data.statusList) {
        if (pagination.total == 0) {
          data.searchTextNum = false;
        }
      }
    }
  });
};
getList();
//枚举值
const enumeration = () => {
  request('/aiipweb/om-service/dict/getDict', {
    method: 'GET',
    data: {
      dictName: 'capabilityStatus',
    },
  }).then((res: any) => {
    res.body.forEach((item) => {
      data.enumerationList[item.value] = item.label;
    });
  });
};
enumeration();
//枚举值显示
const statusTransfer = (value) => {
  return data.enumerationList[value];
};
//表头
const columns = computed(() => {
  const columnsA = [
    {
      title: '能力名称',
      dataIndex: 'name',
      key: 'name',
      width: '13%',
    },
    {
      title: '能力状态',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: '已上架', value: 1 },
        { text: '已下架', value: 2 },
      ],
      width: '8%',
    },
    {
      title: '更新时间',
      dataIndex: 'modifiedTime',
      key: 'modifiedTime',
      sorter: {
        compare: (a, b) => a.createTime - b.createTime,
        multiple: 1,
      },
      width: '14%',
    },
    {
      title: '能力简介',
      dataIndex: 'description',
      key: 'description',
      width: '17%',
    },
    {
      title: '能力提供方信息 / 调用地址',
      dataIndex: 'callAddress',
      key: 'callAddress',
      width: '17%',
    },
    {
      title: '操作',
      dataIndex: 'button',
      key: 'button',
      width: '31%',
    },
  ];

  return columnsA;
});
//切换tabs
const callback = (key) => {
  data.key = key;
  data.searchText = '';
  getList();
  if (key == '1') {
    data.keyValue = '';
  }
};
// 模糊搜索
const handleInput = (value) => {
  if (data.key == '1') {
    data.keyValue = '';
    pagination.pageNum = 1;
    getList();
  } else {
    data.keyValue = value;
  }
};
//列表筛选
const handleChange = (paginations, filters, sorter) => {
  data.desc = sorter.order == 'ascend' ? false : true;
  data.statusList = filters.status ? filters.status : [];
  getList();
};
//新建
const router = useRouter();
const handleCreate = () => {
  Modal.confirm({
    title: createVNode('span', { style: 'fontWeight: 500;color: #121F2C;' }, '欢迎申请入驻开放能力板块！'),
    okText: '准备好了',
    content: createVNode('span', { style: 'fontWeight: 400;color:  #606972;' }, '即将进入能力上架流程，请准备好能力介绍和技术文档材料'),
    cancelText: '取消',
    icon: createVNode(ExclamationCircleFilled, { style: 'color:#0082FF;' }),
    onOk: () => {
      router.push({ path: '/capacity-management/capacity-create' });
    },
  });
};
//编辑
const edit = (val) => {
  router.push({ path: `/capacity-management/capacity-edit/${val.name}`, query: { key: 'capacity', button: val.button } });
};
//预览查看
const view = (val) => {
  if (statusTransfer(val.status) == '已上架') {
    if (val.url) {
      if (window.location.origin == 'http://jiutian.hq.cmcc') {
        openInNewTab(window.location.origin + '/' + '#' + val.url);
      } else if (window.location.origin == 'https://jiutian.10086.cn') {
        openInNewTab(window.location.origin + '/portal/#' + val.url);
      } else if (window.location.origin == 'http://172.31.192.238') {
        openInNewTab('http://172.31.192.238/#' + val.url);
      }
    }
  } else {
    let routeUrl = router.resolve({ path: '/capacity-management/preview', query: { listName: val.name } });
    const a = document.createElement('a');
    const event = new MouseEvent('click');
    console.log(routeUrl.href);

    a.href = routeUrl.href;
    a.target = '_blank';
    a.rel = 'noopener noreferrer';
    a.dispatchEvent(event);
  }
};
//操作翻页
const changePageNum = (pageNum) => {
  pagination.pageNum = pageNum;
  getList();
};
const changePageSize = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.pageNum = 1;
  getList();
};
//删除
const delEdge = (e) => {
  const capabilityId = e.id;
  Modal.confirm({
    title: createVNode('span', { style: 'fontWeight: 500;color: #121F2C;' }, '确定删除该能力吗？'),
    okText: '取消',
    okButtonProps: { type: 'ghost' },
    content: createVNode('span', { style: 'fontWeight: 400;color:  #606972;' }, '删除后将不可找回，请谨慎操作'),
    cancelText: '删除',
    icon: createVNode(ExclamationCircleFilled, { style: 'color:#FF454D;' }),
    cancelButtonProps: { type: 'primary', danger: true },
    autoFocusButton: null,
    onCancel: () => {
      request('/aiipweb/om-service/capability/delete', {
        method: 'GET',
        data: {
          capabilityId: capabilityId,
        },
      }).then((res: any) => {
        if (res.state) {
          if (res.body == 'success') {
            message.success(`【${e.name}】已删除`);
            getList();
          } else {
            message.error(`【${e.name}】未能删除，请稍后再试`);
          }
        }
      });
    },
  });
};
//下架
const unShelve = (e) => {
  const capabilityId = e.id;
  Modal.confirm({
    title: createVNode('span', { style: 'fontWeight: 500;color: #121F2C;' }, '确定下架该能力吗？'),
    okText: '取消',
    okButtonProps: { type: 'ghost' },
    // content: createVNode('span', { style: 'fontWeight: 400;color:  #606972;' }, '下架后在开放能力板块不可见。 状态变为“已下架”，如需再次上架，您可再次进行上架操作。'),
    content: () => {
      return h('div', { style: 'fontWeight: 400;color:  #606972;' }, [h('p', { style: 'margin:0' }, '下架后在开放能力板块不可见。'), h('p', { style: 'margin:0' }, '状态变为“已下架”，如需再次上架，您可再次进行上架操作。')]);
    },
    cancelText: '确定下架',
    icon: createVNode(ExclamationCircleFilled, { style: 'color:#FF454D;' }),
    cancelButtonProps: { danger: true },
    autoFocusButton: null,
    onOk: () => {
      console.log(11);
    },
    onCancel: () => {
      request('/aiipweb/om-service/capability/off-shelf', {
        method: 'GET',
        data: {
          capabilityId: capabilityId,
        },
      }).then((res: any) => {
        if (res.state) {
          if (res.body == 'success') {
            message.success(`【${e.name}】已从开放能力下架`);
            getList();
          } else {
            message.error(`【${e.name}】未能从开放能力下架，请稍后再试`);
          }
        }
      });
    },
  });
};
//打开模态
const shelve = (value) => {
  data.shelfVal = true;
  data.shelfObj = value;
};
//关闭模态
const cancelModal = (value) => {
  if (value) {
    getList();
  }
  data.shelfVal = false;
  data.manageVal = false;
};
//打开管理模态
const manage = (value) => {
  console.log(value);
  data.manageVal = true;
  data.manageObj = value;
};
//关闭模态
// const cancelModal = (value) => {
//   if (value) {
//     getList();
//   }
//   data.manage = false;
// };
</script>

<style lang="less" scoped>
.bread-crumb {
  height: 56px;
  line-height: 56px;
}
#container {
  .fold {
    position: relative;
    .fold-div {
      position: absolute;
      z-index: 4;
      height: 32px;
      border-radius: 2px;
      right: 10px;
      display: flex;
      .search-input {
        margin-right: 8px;
      }
    }
  }
  .table-empty {
    position: relative;
    top: -133px;
    .table-empty-text {
      line-height: 22px;
      color: #121f2c;
      font-size: 16px;
      font-weight: 400;
      margin-bottom: 16px;
    }
    .table-empty-but {
      color: #555555;
      font-size: 14px;
      font-weight: 400;
      span {
        margin-left: 5px;
        color: #337dff;
      }
    }
  }
}
/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}
/deep/.ant-table-filter-column {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    width: 48px;
    flex: 0;
  }
  .ant-table-filter-trigger {
    margin: 0;
  }
}
/deep/.ant-table-column-sorters {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    width: 48px;
    flex: 0;
  }
}
/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}
:deep(.cus-row) {
  cursor: pointer;
  font-size: 12px;
  .user-name-text {
    &:hover {
      color: @primary-color;
    }
  }
}

:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;
  th {
    background-color: #f6f9fc;
  }
}
/deep/.ant-tabs-top > .ant-tabs-nav::before {
  width: 468px;
}
/deep/.ant-tabs-nav-wrap .ant-tabs-nav-list .ant-tabs-tab {
  padding-top: 0;
}
.type {
  display: flex;
  align-items: center;
  .circle2 {
    width: 6px;
    height: 6px;
    background: grey;
    border-radius: 50%;
    box-shadow: 0px 2px 10px 0px #d7dddd;
    display: flex;
    margin-right: 8px;
  }
  .green {
    background: #1dca94;
  }
  .grey {
    background: #c2c5cf;
  }
}
.overflow-ellipsis {
  width: 150px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.overflow-ellipsis-pro {
  max-width: 50px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.info {
  display: flex;
  align-items: center;
  .copy-hover {
    font-size: 16px;
    color: #7f828f;
    &:hover {
      color: #0082ff;
    }
  }
  .copy-text {
    margin-left: 6px;
  }
}

.active {
  display: flex;
  justify-content: flex-start;
  align-content: center;
  /deep/.ant-tooltip-disabled-compatible-wrapper {
    width: 25%;
  }
  /deep/.ant-btn {
    font-size: 12px;
    width: 25%;
    display: flex;
    padding-left: 0;
    padding-right: 0;
  }
  span {
    display: flex;
    align-items: center;
    color: #606972;
    .icon {
      font-size: 18px;
      margin-right: 4px;
      color: #0082ff;
    }
  }
  .disabled-span {
    color: #a0a6ab;
    .icon {
      margin-right: 4px;

      color: #a0a6ab;
    }
  }
}
</style>
