<template>
  <div class="user-edit">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '动态管理', query: $route.query }"> 动态管理 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>{{ isCreate ? '新建' : '编辑' }}动态</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="main">
      <div>
        <div class="form-title">
          <span>{{ isCreate ? '新建' : '编辑' }}动态</span>
        </div>
        <a-button class="form-title-button" type="primary" ghost @click="handleCreatePreview">预览</a-button>
      </div>
      <a-spin :spinning="editLoading">
        <a-form :colon="false" ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <div class="mail-info">
            <a-form-item class="sub-item" label="标题" name="dynamicTitle" :wrapper-col="subWrapperCol">
              <a-input v-model:value="form.dynamicTitle" placeholder="请输入" style="width: 320px" />
            </a-form-item>
            <a-form-item class="sub-item" label="摘要" name="dynamicAbstract" :wrapper-col="subWrapperCol">
              <a-textarea v-model:value="form.dynamicAbstract" placeholder="请输入摘要" style="width: 320px" />
            </a-form-item>
            <a-form-item class="sub-item sub-item-radiogroup" label="内容" name="type" required :wrapper-col="subWrapperCol">
              <a-radio-group v-model:value="contentType" name="radioGroup" @change="handleContentType">
                <a-radio value="0">自定义</a-radio>
                <a-radio value="1">URL链接</a-radio>
              </a-radio-group>
            </a-form-item>
            <a-form-item v-show="contentType == 0" label=" " class="sub-item" name="dynamicContent" :wrapper-col="subWrapperCol">
              <div class="mail-content">
                <text-editor
                  :initParams="{
                    uploadImgShowBase64: false,
                    uploadFileName: 'file',
                    uploadImgServer: './homepage/web/dynamic/image/add',
                    uploadImgMaxLength: 1,
                    uploadImgTimeout: 20 * 1000,
                    uploadImgHeaders: {
                      Authorization: 'Bearer ' + refreshToken,
                    },
                  }"
                  centerType="DYNAMIC"
                  :editorContent="form.dynamicContentTags"
                  @change="handleEditorChange"
                />
                <div class="content-num">
                  <span :class="{ exceed: form.dynamicContent?.length > 1000 }">{{ form.dynamicContent?.length || 0 }}</span>
                  /1000
                </div>
              </div>
            </a-form-item>
            <a-form-item v-if="contentType == 1" label=" " class="sub-item" name="dynamicUrl" :wrapper-col="subWrapperCol">
              <a-textarea class="sub-item-url" v-model:value="form.dynamicUrl" placeholder="请输入" @change="handleContentUrl" />
              <div class="content-num">
                <span :class="{ exceed: form.dynamicUrl?.length > 200 }">{{ form.dynamicUrl?.length || 0 }}</span
                >/200
              </div>
            </a-form-item>

            <a-form-item class="sub-item button-row" label=" " :wrapper-col="subWrapperCol">
              <a-space>
                <a-button @click="preHandleSaveAndSend(false, 0)" type="primary" class="send-btn">保存并发布</a-button>
                <a-button @click="preHandleSaveAndSend(true, 1)" type="primary" ghost class="confirm-btn">保存</a-button>
                <a-button @click="goBack" class="cancel-btn">取消</a-button>
              </a-space>
            </a-form-item>
          </div>
        </a-form>
        <confirm-modal :visible="sendModalVisible" title="确定发布动态吗？" :content-title="form.dynamicTitle" content="发送后可进行编辑并再次发布" confirm-btn="立即发布" @cancel="sendModalVisible = false" @confirm="saveAndSend(false, 0)"></confirm-modal>
      </a-spin>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { mapState } from 'vuex';
import { message } from 'ant-design-vue';
import TextEditor from '@/components/editor/index.vue';
import ConfirmModal from './components/confirmModal.vue';
import { DynamicManagement } from '@/apis';
const { createDynamic, updateDynamic, getDetail } = DynamicManagement;

let editor = null as any;

export default defineComponent({
  components: { TextEditor, ConfirmModal },
  data() {
    return {
      isCreate: this.$route.name === '动态管理新建',
      contentType: '0',
      dynamicUrl: '',
      editLoading: false,
      uploadLoading: false,
      dynamicId: this.$route.query.id,
      labelCol: { span: 4 },
      wrapperCol: { span: 10 },
      subWrapperCol: { span: 14 },
      form: {
        dynamicTitle: '',
        dynamicAbstract: '',
        type: 0,
        dynamicUrl: '',
        dynamicContent: '',
        dynamicContentTags: '',
      },
      sendModalVisible: false,
    };
  },
  computed: {
    ...mapState(['refreshToken']),
    rules(this: any) {
      return {
        dynamicTitle: [
          { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
          { min: 0, max: 20, message: '20个字符以内', trigger: ['blur', 'change'] },
        ],
        dynamicAbstract: [
          { required: true, message: '请输入摘要', trigger: ['blur', 'change'] },
          { min: 0, max: 120, message: '120个字符以内', trigger: ['blur', 'change'] },
        ],
        dynamicContent: [
          { required: false, validator: this.validateDynamicContent, message: '请输入内容', trigger: ['blur', 'change'] },
          { max: 1000, message: '字数超过限制', trigger: ['blur', 'change'] },
        ],
        dynamicUrl: [
          { required: false, validator: this.validateUrlContent, message: '请输入内容', trigger: ['blur', 'change'] },
          { max: 200, message: '字数超过限制', trigger: ['blur', 'change'] },
        ],
      };
    },
  },
  created() {
    if (editor) {
      editor.txt.html('');
      editor.destroy();
      editor = null as any;
    }
    this.getDynamicDetail();
  },
  methods: {
    async getDynamicDetail(this: any) {
      if (this.isCreate) {
        return;
      } else {
        // 编辑页面获取初始数据
        this.editLoading = true;
        const res: any = await getDetail({ data: { id: this.dynamicId } });
        if (res.state == 'OK') {
          const { dynamicTitle, dynamicAbstract, type, dynamicUrl, dynamicContent, id } = res.body;
          this.form = {
            ...this.form,
            ...{
              dynamicTitle,
              dynamicAbstract,
              type: JSON.stringify(type), //TODO: 这个操作是数据格式不统一造成的，后期优化
              dynamicUrl,
              dynamicContent,
              dynamicContentTags: dynamicContent,
            },
          };
          this.contentType = this.form.type;
          this.editLoading = false;
        }
      }
    },
    handleCreatePreview() {
      const formDataUrl = this.form.dynamicUrl;
      const formDataContent = this.form.dynamicContentTags;
      const a = document.createElement('a');
      const event = new MouseEvent('click');

      const routeUrl: any = this.contentType === '0' ? this.$router.resolve({ path: '/dynamic-manage/dynamicManage-preview', query: { iscreate: 1 } }) : formDataUrl;

      switch (this.contentType) {
        case '0':
          localStorage.setItem('previewData', JSON.stringify(this.form));
          if (formDataContent && formDataContent.length > 0) {
            a.href = routeUrl.href;
            a.target = '_blank';
            a.rel = 'noopener noreferrer';
            a.dispatchEvent(event);
          }
          break;
        case '1':
          if (formDataUrl && formDataUrl.length > 0) {
            a.href = formDataUrl;
            a.target = '_blank';
            a.rel = 'noopener noreferrer';
            a.dispatchEvent(event);
          }
          break;
        default:
          break;
      }
    },
    // 内容输入类型
    handleContentType(e) {
      this.contentType = e.target.value;
    },
    // 自定义内容相关
    handleEditorChange(v) {
      if (v) {
        this.form.dynamicContentTags = v?.content;
        this.form.dynamicContent = v?.pageContentIndex;
        (this as any).$refs.ruleForm.validateFields('dynamicContent'); // 需要手动触发一次校验
      }
    },
    validateDynamicContent(rule, value, callback) {
      if (value || (!value && this.form.dynamicContent) || this.contentType == '1') {
        return Promise.resolve();
      } else {
        return Promise.reject('请输入内容');
      }
    },

    // url链接相关
    handleContentUrl(e) {
      this.form.dynamicUrl = e.target.value;
      (this as any).$refs.ruleForm.validateFields('dynamicUrl'); // 需要手动触发一次校验
    },
    validateUrlContent(rule, value, callback) {
      if (value || (!value && this.form.dynamicUrl) || this.contentType == '0') {
        return Promise.resolve();
      } else {
        return Promise.reject('请输入内容');
      }
    },

    // 保存、发布、返回
    preHandleSaveAndSend(this: any, save: boolean, type: number) {
      this.$refs.ruleForm
        .validate()
        .then(() => {
          save ? this.saveAndSend(save, type) : (this.sendModalVisible = true);
        })
        .catch((err) => {
          this.editLoading = false;
          throw err;
        });
    },
    async saveAndSend(this: any, save: boolean, saveType: 0) {
      this.sendModalVisible = false;
      this.editLoading = true;

      const { dynamicTitle, dynamicAbstract, dynamicUrl, dynamicContentTags } = this.form;

      let data = {
        dynamicTitle,
        dynamicAbstract,
        dynamicUrl,
        dynamicContent: dynamicContentTags,
        type: Number(this.contentType) || 0,
      } as any;

      if (!this.isCreate) {
        data.id = this.dynamicId;
        data.updateType = saveType;
      } else if (this.isCreate) {
        data.saveType = saveType;
      }

      let res: any;
      if (!this.isCreate) {
        res = await updateDynamic({ type: 'POST', data });
      } else {
        res = await createDynamic({ type: 'POST', data });
      }

      try {
        if (res.state == 'OK' && save) {
          message.success(`动态【${dynamicTitle}】保存成功！`);
          this.goBack();
        } else if (res.state == 'OK' && !save) {
          message.success(`动态【${dynamicTitle}】发布成功！`);
          this.goBack();
        } else {
          save ? message.error(`动态【${dynamicTitle}】保存失败，请稍后再试！`) : message.error(`动态【${dynamicTitle}】发布失败，请稍后再试！`);
          this.editLoading = false;
        }
      } catch (error) {
        this.editLoading = false;
      }
      this.editLoading = false;
    },
    goBack(this: any) {
      this.$router.push({ path: '/dynamic-manage', query: { ...this.$route.query, confirmLeave: undefined } });
    },
  },
});
</script>
<style lang="less" scoped>
.user-edit {
  .nav {
    height: 58px;
    line-height: 58px;
    padding-left: 20px;
    background: #ffffff;
    font-weight: 500;
    color: #121f2c;
  }
  .main {
    // min-width: 1254px;
    padding: 20px 20px 34px;
    margin: 20px;
    background: #ffffff;
    position: relative;
    .form-title {
      display: flex;
      justify-content: space-between;
      span {
        font-size: 16px;
        font-weight: 500;
        color: #121f2c;
        margin-bottom: 24px;
      }
    }
    .form-title-button {
      position: absolute;
      right: 20px;
      top: 20px;
      width: 80px;
    }
    .sub-item {
      position: relative;
      width: 800px;
      margin-bottom: 30px;
      .sub-item-url {
        height: 114px;
      }
      &.button-row {
        margin-top: 48px;
      }
    }
    .sub-item-radiogroup {
      margin-bottom: 14px;
    }
    .content-num {
      font-size: 12px;
      color: #cbcfd2;
      position: absolute;
      right: 0;
      span {
        color: #0082ff;
      }
      .exceed {
        color: #ff454d;
      }
    }
    .mail-content {
      width: 720px;
      position: relative;
    }
    .send-btn {
      width: 120px;
    }
    .confirm-btn,
    .cancel-btn {
      width: 88px;
    }
  }
}
/deep/ label.ant-form-item-required[for='form_item_dynamicContent']:not(.ant-form-item-required-mark-optional)::before,
/deep/ label.ant-form-item-required[for='form_item_dynamicUrl']:not(.ant-form-item-required-mark-optional)::before {
  color: transparent;
}
/deep/ .ant-spin-nested-loading {
  display: inline-block;
}
.upload-num {
  margin-top: 10px;
  .icon-delete {
    margin-left: 10px;
  }
}
.mail-content {
  @borderColor: #d9d9d9;
  @borderRadius: 2px;
  &:deep(.w-e-text-container) {
    border-color: @borderColor !important;
    border-radius: @borderRadius;
  }
  &:deep(.w-e-toolbar) {
    border-radius: @borderRadius;
    border-top-color: @borderColor !important;
    border-left-color: @borderColor !important;
    border-right-color: @borderColor !important;
  }
}
</style>
