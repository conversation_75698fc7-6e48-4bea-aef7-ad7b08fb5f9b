<template>
  <div class="demand-manage">
    <div :class="['search-input', !FEATURE_SMS && 'input-gap']">
      <a-input v-model:value="keyword" placeholder="工单号/问题描述/用户名/最后处理人" @change="handleChange" style="min-width: 280px; height: 32px">
        <template #prefix>
          <jt-icon type="iconsousuo" style="color: #bec2c5"></jt-icon>
        </template>
      </a-input>
    </div>
    <a-table :loading="loading" :columns="columns" :data-source="dataSource" :pagination="false" @change="handleTableChange" :customRow="customRow">
      <template #emptyText>
        <empty v-if="!loading" title="工单"> </empty>
      </template>
      <template #numberSlot="{ record }">
        <span class="order-number">{{ record.ticketNum }}</span>
      </template>
      <template #statusSlot="{ record }">
        <a-tag size="small" :color="record.status === 0 ? 'red' : record.status === 1 ? 'blue' : 'green'">{{ record.status === 0 ? '待处理' : record.status === 1 ? '处理中' : '已关闭' }}</a-tag>
      </template>
      <template #statusTitleSlot>
        <span>状态</span>
        <span class="filter-group">
          <jt-icon class="filter-icon" ref="statusFilterVisibleIconref" :class="((status !== undefined && status !== null && status !== '') || statusFilterVisible) && 'filter-icon-select'" type="iconshaixuan" @click="handleFilterIcon('statusFilterVisible')" />
          <teleport to="body">
            <div class="filter-item-list" v-show="statusFilterVisible" :style="filterPosition">
              <div v-for="item in tabsData" :key="item.id" class="filter-item" :class="item.id === status ? 'catageory-select' : ''">
                <div @click="() => handleFilter(item.id, 'status')">{{ item.name }}</div>
              </div>
            </div>
          </teleport>
        </span>
      </template>
      <template #lmCatogerySlot>
        <span>大模型分类</span>
        <span class="filter-group">
          <jt-icon class="filter-icon" ref="lmCatogeryFilterVisibleIconref" :class="(currentCatageoryId || lmCatogeryFilterVisible) && 'filter-icon-select'" type="iconshaixuan" @click="handleFilterIcon('lmCatogeryFilterVisible')" />
          <teleport to="body">
            <div class="filter-item-list" v-show="lmCatogeryFilterVisible" :style="filterPosition">
              <div v-for="item in lmCatogery" :key="item.largeModelTypeId" class="filter-item" :class="item.largeModelTypeId === currentCatageoryId && 'catageory-select'">
                <div @click="() => handleFilter(item.largeModelTypeId, 'currentCatageoryId')">{{ item.largeModelType }}</div>
              </div>
            </div>
          </teleport>
        </span>
      </template>
    </a-table>
    <Pagination :total="pagination.total" :pageNum="pagination.pageNum" :pageSize="pagination.pageSize" :pageSizeOptions="[10, 20, 50, 100]" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
  </div>
</template>

<script>
import Pagination from '@/components/pagination.vue';
import Empty from '@/components/empty.vue';
import request from '@/request';
import { onClickOutside } from '@vueuse/core';
import { defineComponent, ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import queryParamsMixin from '@/mixins/queryParamsMixin.js';
import { getEnvConfig } from '@/config';
import { debounce } from 'lodash';

export default defineComponent({
  components: {
    Pagination,
    Empty,
  },
  mixins: [queryParamsMixin],
  setup(props, context) {
    const router = useRouter();
    const route = useRoute();
    const query = route.query;
    const loading = ref(false);
    const columns = ref([
      {
        dataIndex: 'ticketNum',
        key: 'ticketNum',
        title: '工单号',
        slots: {
          customRender: 'numberSlot',
        },
        width: '180px',
      },
      {
        dataIndex: 'description',
        key: 'description',
        title: '需求描述',
        width: '20%',
        ellipsis: true,
      },
      {
        dataIndex: 'userName',
        key: 'userName',
        title: '用户名',
      },
      {
        dataIndex: 'largeModelType',
        key: 'largeModelType',
        slots: {
          title: 'lmCatogerySlot',
        },
      },
      {
        dataIndex: 'status',
        key: 'status',
        slots: {
          title: 'statusTitleSlot',
          customRender: 'statusSlot',
        },
        width: '100px',
      },
      {
        dataIndex: 'createTime',
        key: 'createTime',
        title: '创建时间',
        sorter: true,
        sortOrder: false,
        width: '180px',
        customRender({ text }) {
          return text || '--';
        },
      },
      {
        dataIndex: 'updateTime',
        key: 'updateTime',
        title: '最近处理时间',
        sorter: true,
        sortOrder: false,
        width: '180px',
        customRender({ text }) {
          return text || '--';
        },
      },
      {
        dataIndex: 'handler',
        key: 'handler',
        title: '最后处理人',
        ellipsis: true,
        customRender({ text }) {
          return text || '--';
        },
      },
    ]);
    const dataSource = ref([]);
    const keyword = ref(query.keyword || '');
    let status = ref(query.status || null);
    const largeModelTypeMap = {};
    const pagination = ref({
      pageNum: query.pageNum ? Number(query.pageNum) : 1,
      pageSize: query.pageSize ? Number(query.pageSize) : 10,
      total: 0,
    });
    const orderColumn = ref(query.orderColumn || '');

    const isAsc = ref(query.isAsc === 'true' ? true : false);

    const instance = getCurrentInstance();
    const tabsData = ref([
      {
        id: null,
        name: '全部',
      },
      {
        id: 0,
        name: '待处理',
      },
      {
        id: 1,
        name: '处理中',
      },
      {
        id: 2,
        name: '已关闭',
      },
    ]);
    const currentCatageoryId = ref(query.currentCatageoryId || '');
    const FEATURE_SMS = getEnvConfig('FEATURE_SMS') === '1';

    onMounted(async () => {
      await getLmCatogery();
      getTableData();
    });

    const handleChange = debounce(() => {
      instance.proxy.rewriteUrlParamsMixin({ keyword: keyword.value, pageNum: 1 });
      pagination.value.pageNum = 1;
      getTableData();
    }, 500);

    const lmCatogery = ref([]);
    const lmCatogeryFilterVisibleIconref = ref(null);
    const lmCatogeryFilterVisible = ref(false);
    const statusFilterVisibleIconref = ref(null);
    const statusFilterVisible = ref(false);
    const filterPosition = reactive({
      left: '0px',
      top: '0px',
    });
    onClickOutside(lmCatogeryFilterVisibleIconref, (event) => {
      lmCatogeryFilterVisible.value = false;
    });
    onClickOutside(statusFilterVisibleIconref, (event) => {
      statusFilterVisible.value = false;
    });
    const handleFilter = (id, key) => {
      eval(key).value = id;
      pagination.value.pageNum = 1;
      instance.proxy.rewriteUrlParamsMixin({ pageNum: 1, [key]: id });
      getTableData();
    };
    const calcutateFilterPosition = (ref) => {
      filterPosition.left = ref.value.getBoundingClientRect().x + 5 + 'px';
      filterPosition.top = ref.value.getBoundingClientRect().y + 20 + 'px';
    };

    const handleFilterIcon = (key) => {
      !eval(key).value && calcutateFilterPosition(eval(key + 'Iconref'));
      eval(key).value = !eval(key).value;
    };

    const getTableData = async () => {
      loading.value = true;
      const reqData = {
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        keyword: keyword.value,
        largeModelType: currentCatageoryId.value === '' ? undefined : largeModelTypeMap[currentCatageoryId.value],
        status: status.value === null ? undefined : Number(status.value),
      };
      if (orderColumn.value) {
        reqData[orderColumn.value + 'Sort'] = isAsc.value ? 'asc' : 'desc';
      }
      const res = await request('/ticket/web/demand/list', { method: 'POST', data: reqData });
      if (res.state === 'OK') {
        dataSource.value = res.body.list ?? [];
        pagination.value.total = res.body.total;
      }
      loading.value = false;
    };
    // 获取大模型的分类
    const getLmCatogery = async () => {
      const res = await request('/ticket/web/demand/lmt/get', { method: 'GET' });
      if (res.state === 'OK') {
        context.emit('updateLmCatogery', res.body);
        res.body.forEach((x) => {
          largeModelTypeMap[x.largeModelTypeId] = x.largeModelType;
        });
        lmCatogery.value = [{ largeModelTypeId: '', largeModelType: '全部' }, ...res.body];
      }
    };

    const toWorkorderDetail = (record) => {
      router.push({ path: `/workorder-manage/detail/${record.ticketNum}`, query: route.query });
    };

    // 点击排序操作
    const handleTableChange = (page, filter, sort) => {
      instance.proxy.changeOrderColumn(sort.field, sort.order);
      orderColumn.value = sort.field;
      isAsc.value = sort.order == 'ascend';
      if (!sort.order) {
        orderColumn.value = '';
        isAsc.value = false;
        instance.proxy.rewriteUrlParamsMixin({ orderColumn: undefined, isAsc: undefined });
      } else {
        instance.proxy.rewriteUrlParamsMixin({ orderColumn: orderColumn.value, isAsc: isAsc.value });
      }
      getTableData();
    };

    const changePageNum = (pageNum) => {
      pagination.value.pageNum = pageNum;
      instance.proxy.rewriteUrlParamsMixin({ pageNum });
      getTableData();
    };

    const changePageSize = (pageSize) => {
      pagination.value.pageSize = pageSize;
      pagination.value.pageNum = 1;
      instance.proxy.rewriteUrlParamsMixin({ pageNum: 1, pageSize });
      getTableData();
    };

    const customRow = (record) => {
      return {
        style: { cursor: 'pointer' },
        onClick: () => {
          toWorkorderDetail(record);
        },
      };
    };

    return {
      status,
      loading,
      columns,
      FEATURE_SMS,
      dataSource,
      pagination,
      getTableData,
      toWorkorderDetail,
      changePageNum,
      changePageSize,
      tabsData,
      currentCatageoryId,
      lmCatogery,
      lmCatogeryFilterVisible,
      lmCatogeryFilterVisibleIconref,
      statusFilterVisible,
      statusFilterVisibleIconref,
      handleFilter,
      handleFilterIcon,
      filterPosition,
      handleChange,
      keyword,
      handleTableChange,
      customRow,
    };
  },
});
</script>

<style lang="less" scoped>
@import '@/assets/styles/index.less';
.demand-manage {
  .search-input {
    position: absolute;
    right: 226px;
    top: 20px;
    margin-bottom: 20px;
    z-index: 1;
  }
  .input-gap {
    right: 116px;
  }
  .order-number {
    cursor: pointer;
    &:hover {
      color: @jt-primary-color;
    }
  }
  .filter-group {
    position: relative;
    margin-left: 5px;
    .filter-icon {
      cursor: pointer;
      color: #bfbfbf;
    }
    .filter-icon-select {
      color: @jt-primary-color;
    }
  }
}
.filter-item-list {
  background: #fff;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  position: absolute;
  left: 0px;
  top: 16px;
  display: flex;
  flex-direction: column;
  min-width: 90px;
  z-index: 1;
  .filter-item {
    cursor: pointer;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 12px;
    color: #606972;
    padding: 0px 8px;
    &:hover {
      background-color: #f0f8ff;
      color: @jt-primary-color;
    }
  }
  .filter-item.catageory-select {
    background-color: #f0f8ff;
    color: @jt-primary-color;
  }
}
</style>
