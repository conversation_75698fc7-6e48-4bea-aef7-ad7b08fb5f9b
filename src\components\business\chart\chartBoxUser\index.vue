<template>
  <div class="chart-wrap">
    <v-chart class="chart" :option="option" />
  </div>
</template>

<script lang="ts">
import { ref, defineComponent, onMounted } from 'vue';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Pie<PERSON>hart } from 'echarts/charts';
import { TitleComponent, TooltipComponent, LegendComponent, GridComponent, DataZoomComponent } from 'echarts/components';
import VChart from 'vue-echarts';
import { getDataSeries } from './chart-data';
import { getFormatString, getTableTime } from '@/utils/echarts';
import { ChartData } from '@/apis/interface';
import { User } from '@/apis';

const { getCommonSummary } = User;

use([CanvasRenderer, LineChart, BarChart, PieChart, TitleComponent, TooltipComponent, LegendComponent, GridComponent, DataZoomComponent]);

export default defineComponent({
  components: { VChart },
  setup() {
    //echart初始化
    const option = ref({});
    // 获取chart数据
    const getChartData = async () => {
      const timeItem = getTableTime();
      option.value = {};
      const params = {
        beginTime: getFormatString(timeItem.beginTime as number),
        endTime: getFormatString(timeItem.endTime as number),
      };
      const res = await getCommonSummary({ data: params });
      if (res && res?.state === 'OK') {
        option.value = getDataSeries(res.body as ChartData[]);
      }
    };
    onMounted(getChartData);
    return {
      option,
    };
  },
});
</script>

<style lang="less" scoped>
.chart-box {
  position: relative;
  span {
    display: block;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    max-width: 130px;
    height: 24px;
    text-align: center;
  }
}
.chart {
  height: 400px;
}
.storage-content {
  display: flex;
  h2 {
    margin-bottom: 0;
    font-size: 14px;
  }
}
.chart-wrap {
  width: 100%;
}
.storage-table {
  width: 50%;
  flex-shrink: 0;
}
:deep(.tooltip-wrap) {
  min-width: 200px;
  .tooltip-content {
    margin-bottom: 12px;
  }
  .dot-common {
    width: 8px;
    height: 8px;
    background: #568af1;
    border-radius: 100%;
    margin-right: 8px;
    display: block;
  }
  .row {
    display: flex;
    align-items: center;
    // justify-content: space-between;
    margin-bottom: 8px;
    p {
      margin-right: 60px;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #121f2c;
      line-height: 20px;
    }
    span {
      font-size: 12px;
      font-weight: 400;
      color: #606972;
      line-height: 18px;
    }
  }
  .tooltip-footer {
    padding-top: 8px;
    font-size: 12px;
    font-weight: 400;
    color: #606972;
    line-height: 18px;
    border-top: 1px solid #e0e1e1;
  }
}
/deep/.jt-pagination {
  .left {
    div {
      display: none;
    }
  }
}
</style>
