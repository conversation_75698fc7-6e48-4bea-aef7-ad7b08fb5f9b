export function encryptStr(str, jtPK) {
  if (!str) return '';
  if (!jtPK) {
    return '';
  }
  const forge = window.forge;
  const publicKeyPem = `-----BEGIN PUBLIC KEY-----
        ${jtPK}
        -----<PERSON>ND PUBLIC KEY-----`;
  const rsaEncryptedBytes = forge.pki.publicKeyFromPem(publicKeyPem).encrypt(str, 'RSA-OAEP', {
    md: forge.md.sha256.create(),
    mgf1: forge.mgf.mgf1.create(forge.md.sha1.create()),
  });
  const value = forge.util.encode64(rsaEncryptedBytes);
  return value;
}
