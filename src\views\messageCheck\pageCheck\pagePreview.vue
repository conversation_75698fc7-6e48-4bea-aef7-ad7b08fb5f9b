<template>
  <div class="user-control-container">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '站内信审核' }"> 站内信审核 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>查看站内信</a-breadcrumb-item>
    </a-breadcrumb>
    <Container>
      <container-item>
        <div class="top-bar">
          <head-title :title="'页面详情'" />
          <div class="page-state">
            <a-space size="large">
              <a-button v-if="+pageInfo.result === 2" @click="checkMail('1')" type="primary">通过</a-button>
              <a-button v-if="+pageInfo.result === 2" @click="checkMail('0')" danger>驳回</a-button>
              <a-tag v-if="+pageInfo.result === 1" color="blue">已通过</a-tag>
              <a-tag v-if="+pageInfo.result === 0" color="red">已驳回</a-tag>
            </a-space>
          </div>
        </div>
        <a-row>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">创建人：</a-col>
              <a-col :span="colSpan.content" class="row-content">{{ checkData(pageInfo.creator) }}</a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">发送人：</a-col>
              <a-col :span="colSpan.content" class="row-content">{{ checkData(pageInfo.submitOperator) }}</a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">创建时间：</a-col>
              <a-col :span="colSpan.content" class="row-content">{{ checkData(pageInfo.creatTime) }}</a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">提交时间：</a-col>
              <a-col :span="colSpan.content" class="row-content">{{ checkData(pageInfo.submitTime) }}</a-col>
            </a-row>
          </a-col>
          <a-col :span="12">
            <a-row>
              <a-col :span="colSpan.title" class="row-title">发送对象：</a-col>
              <a-col :span="colSpan.content" class="row-content" v-if="pageInfo.isGlobal !== undefined && pageInfo.isGlobal !== null">
                {{ pageInfo.isGlobal === '0' ? `指定用户（${pageInfo.successNum}）` : '全部用户' }}
                <a-button type="link" @click="downloadRecord" class="downloadRecord" v-if="pageInfo.isGlobal === '0'">导出用户列表</a-button>
              </a-col>
              <a-col :span="colSpan.content" class="row-content" v-else>-</a-col>
            </a-row>
          </a-col>
        </a-row>
      </container-item>
      <container-item>
        <div class="top-bar">
          <head-title title="内容预览" />
        </div>
        <div class="content-preview">
          <a-row>
            <a-col :span="24">
              <span>标题：</span><span>{{ checkData(pageInfo.mailTitle) }}</span>
            </a-col>
            <a-col :span="24">
              <span>类型：</span><span>{{ checkData(allMailType?.find((x) => x.mailTypeId === pageInfo.mailType)?.mailTypeName) }}</span>
            </a-col>
            <a-col class="mail-content" :span="24">
              <span>内容：</span>
              <div v-html="pageInfo.mailContent" class="document-content markdown-body"></div>
            </a-col>
          </a-row>
        </div>
      </container-item>
    </Container>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { message } from 'ant-design-vue';
import HeadTitle from '@/components/headTitle.vue';
import request from '@/request';
import { downloadFile } from '@/utils/file';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
export default defineComponent({
  components: { HeadTitle, Container, ContainerItem },
  data() {
    return {
      mailId: '',
      pageInfo: {} as any,
      colSpan: {
        title: 6,
        content: 16,
      },
      allMailType: [] as any,
    };
  },
  created(this: any) {
    this.mailId = this.$route.query.mailId;
    if (!this.mailId) {
      this.router.push('/no-auth');
      return;
    }
    this.getAllMailType();
    this.getpageInfo();
  },
  computed: {},
  methods: {
    checkData(val) {
      return val ? val : '-';
    },
    async getAllMailType(this: any) {
      const getAllMailType = await request('/messagecenter/web/getAllMailType', { method: 'GET' });
      if (getAllMailType.state === 'OK') {
        this.allMailType = getAllMailType.body;
      }
    },
    getpageInfo() {
      request('/messagecenter/web/manage/getApproveMailDetail', { method: 'GET', data: { id: this.mailId } }).then((res: any) => {
        if (res.state === 'OK') {
          this.pageInfo = res.body;
        }
      });
    },
    /**
     * 审核站内信
     * @param type 1通过 0驳回
     */
    checkMail(type) {
      request('/messagecenter/web/manage/approveMail', { method: 'GET', data: { id: this.pageInfo.id, approveResult: type } })
        .then((res: any) => {
          if (res.state === 'OK') {
            message.success(`站内信【${this.pageInfo.mailTitle}】审核成功`);
            this.getpageInfo();
          } else {
            message.error(`站内信【${this.pageInfo.mailTitle}】审核失败`);
          }
        })
        .catch(() => {
          message.error(`站内信【${this.pageInfo.mailTitle}】审核失败`);
        });
    },
    downloadRecord() {
      const url = `/messagecenter/web/receiver/downloadRecord?mailId=${this.pageInfo.mailId}`;
      downloadFile({ url })?.catch(() => {
        message.error(`导出失败，请重试`);
      });
    },
  },
});
</script>
<style lang="less" scoped>
.user-control-container {
  line-height: 1.5715;
}
#document-container {
  overflow: auto;
  height: calc(100vh - 160px);
  &::-webkit-scrollbar {
    display: none;
  }
}
.nav {
  height: 58px;
  line-height: 58px;
  padding-left: 20px;
  background: #ffffff;
  font-weight: 500;
  color: #121f2c;
}
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .page-state {
    span {
      width: 70px;
      height: 32px;
      line-height: 30px;
      text-align: center;
      border-radius: 5px;
      font-size: 14px;
    }
  }
}
.ant-row {
  padding-bottom: 16px;
  .row-title {
    text-align: right;
    color: #606972;
  }
  .row-content {
    padding-left: 8px;
    color: #121f2c;
  }
}
.downloadRecord {
  padding: 0;
  height: auto;
}

.mail-content {
  position: relative;
  .document-content {
    display: inline-block;
    word-wrap: break-word;
    width: 704px;
    flex-wrap: wrap;
    vertical-align: top;
    line-height: 1.5715;
  }
}
.content-preview {
  .ant-row {
    padding-left: 64px;
    .ant-col {
      margin-bottom: 24px;
    }
  }
}
</style>
