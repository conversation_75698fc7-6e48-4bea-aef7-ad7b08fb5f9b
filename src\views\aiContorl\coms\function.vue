<template>
  <div class="function-wrap">
    <!-- 标题 -->
    <div class="tech-title">{{ title }}</div>
    <!-- 列表 -->
    <div class="tech-ul">
      <div class="tech-item" v-for="(item, index) in list" :key="index">
        <img class="img" :src="item.img || functionListImg" alt="" />
        <div class="nameStyle">{{ item.intro || functionListIntro }}</div>
        <div class="desc">{{ item.description || functionListDescription }}</div>
      </div>
    </div>
  </div>
</template>
<script>
// import { defineProps } from 'vue';
// const props = defineProps({
//   //  标题 功能介绍
//   title: {
//     type: String,
//     default: '',
//   },
//   // 展示项数组
//   list: {
//     type: Array,
//     default: () => {
//       [];
//     },
//   },
// });
export default {
  name: 'Function',
  props: {
    //  标题 功能介绍
    title: {
      type: String,
      default: '',
    },
    // 展示项数组
    list: {
      type: Array,
      default() {
        return [];
      },
    },
    icons: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      listData: [],
      functionListImg: require('../../../assets/images/preview/<EMAIL>'),
      functionListIntro: '功能介绍',
      functionListDescription: '这是一段功能简介这是一段功能简介这是一段功能简介这是一段功能简介这是一段功能简介这是一段功能简介这是一段功能简介',
    };
  },
  // created() {
  //   this.list.forEach((item) => {
  //     if (item.intro) {
  //       this.listData.push(item);
  //       console.log(this.listData);
  //     }
  //   });
  // },
};
</script>
<style lang="less" scoped>
.function-wrap {
  width: 100%;
  background-size: 100% 100%;
  padding: 64px 0 0 0;
  text-align: left;
  .tech-title {
    text-align: center;
    font-size: 28px;
    margin-bottom: 30px;
    font-weight: 600;
    color: #121f2c;
    line-height: 40px;
  }
  .tech-ul {
    max-width: 1180px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    text-align: center;
    .tech-item {
      min-height: 181px;
      position: relative;
      margin: 0 8px;
      border-radius: 4px;
      background: linear-gradient(360deg, #ffffff 0%, #dbfafc 100%);
      width: 15%;
      flex: 1;
      .img {
        top: -22px;
        left: (50%);
        margin-left: -40px;
        position: absolute;
        width: 72px;
        height: 74px;
      }
      .nameStyle {
        font-weight: 600;
        font-size: 18px;
        color: #121f2c;
        line-height: 24px;
        margin-top: 57px;
        margin-bottom: 12px;
      }
      .desc {
        font-weight: 400;
        color: #606972;
        line-height: 22px;
        font-size: 14px;
        margin: 0 24px;
        text-align: justify;
        word-wrap: break-word;
      }
    }
    .tech-item:nth-child(1) {
      margin-left: 0;
    }
    .tech-item:last-child {
      margin-right: 0;
    }
  }
}
</style>
