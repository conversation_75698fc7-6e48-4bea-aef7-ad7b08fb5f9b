export function phoneHider(str = '') {
  return (str || '').replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

export function checkImgUrl(url, types = '.jpg,.jpeg,.gif,.png,.bmp,.JPG,.JPEG,.GIF,.PNG,.BMP') {
  const typeCollections = types.split(',').map((item) => item.slice(1));
  const suffix = (url || '').split('.').slice(-1)[0];
  if (typeCollections.includes(suffix)) {
    return true;
  }
  return false;
}

export function checkFileType(types, fileName) {
  const allowedExtensions = types
    .toLowerCase()
    .split(',')
    .map((ext) => ext.trim());
  const fileExtension = fileName.split('.').pop().toLowerCase();
  return allowedExtensions.includes(`.${fileExtension}`);
}

export function openInNewTab(url) {
  const newwin = window.open(url);
  newwin.opener = null;
}

// 电话号码
export const telephoneNumberRegex = /^1[3456789]\d{9}$/;
// 6位验证码
export const vetifyCodeRegex = /^\d{6}$/;
// 中英文或空格
export const chineseOrLetterOrBlankRegex = /^[\u4e00-\u9fa5 a-zA-Z]+$/;
// 邮件
export const emailRegex = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
// 数字字母或下划线
export const numberOrLetterOrLineRegex = /^\w+$/;
// 密码规则
export const jtpwRegex = /^(?=.*[0-9].*)(?=.*[A-Z].*)(?=.*[a-z].*).{8,20}$/;
// 学校格式的校验
export const schoolInputRegex = /^[\u4e00-\u9fa5a-zA-Z\s()（）]*$/;
