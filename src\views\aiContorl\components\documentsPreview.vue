<template>
  <div id="app">
    <documentsHeader></documentsHeader>
    <div class="container">
      <div class="left">
        <h1 class="dir-title">开放能力</h1>
        <a-space>
          <a-select show-search placeholder="搜索本产品文档" style="width: 200px" disabled>
            <template #suffixIcon><jt-icon type="iconsousuo" style="font-size: 18px" /></template> </a-select
        ></a-space>
        <h1 class="dir-title" style="margin: 30px 0 0 0">更新日志</h1>
        <a-menu style="width: 256px" mode="inline" v-model:selectedKeys="selectedKeys" v-model:openKeys="openKeys">
          <a-sub-menu key="sub1">
            <template #title> 开放能力 </template>
            <a-sub-menu key="sub3" :title="`${typeNameValue}`">
              <a-menu-item key="1">{{ abilityTypeName }}</a-menu-item>
            </a-sub-menu>
          </a-sub-menu>
        </a-menu>
      </div>
      <div class="right" id="document-container">
        <a-breadcrumb>
          <a-breadcrumb-item>开放能力</a-breadcrumb-item>
          <a-breadcrumb-item>api文档</a-breadcrumb-item>
          <a-breadcrumb-item>{{ `${typeNameValue}` }}</a-breadcrumb-item>
          <a-breadcrumb-item>{{ abilityTypeName }}</a-breadcrumb-item>
        </a-breadcrumb>
        <div class="right-content">
          <h1 class="title">{{ abilityTypeName }}</h1>
          <div style="width: 867px; height: 1px; background: #dfe3e7"></div>
        </div>
        <div class="w-e-text" style="margin-top: 16px" v-if="htmlValue !== ''">
          <div class="document-content markdown-body" v-html="htmlValue"></div>
        </div>
        <div class="w-e-text" style="margin-top: 16px" v-else>
          <div class="document-content markdown-body">尚未录入技术文档~</div>
        </div>
        <div class="anchor-wrap">
          <!-- <a-anchor class="anchor" @click="handleClick" :getContainer="getContainer">
            <a-anchor-link v-for="item of titles" :key="item.id" :href="`#${item.id}`" :title="item.text" />
          </a-anchor> -->
        </div>
      </div>
    </div>
    <documentsFooter></documentsFooter>
  </div>
</template>

<script setup lang="ts">
import documentsHeader from '../components/components/documentsHeader.vue';
import documentsFooter from '../components/components/documentsFooter.vue';
import { reactive, ref, computed } from 'vue';
import { useRoute } from 'vue-router';
import request from '@/request';

const route = useRoute();
const abilityName = route.query.res;
const selectedKeys = ref<string[]>(['1']);
const openKeys = ref<string[]>(['sub1', 'sub3']);
const abilityType = reactive([
  { value: '2', title: '人脸识别' },
  { value: '3', title: '人体与行为识别' },
  { value: '4', title: '图像识别' },
  { value: '7', title: '图像理解' },
  { value: '5', title: '卡证文字识别' },
  { value: '6', title: '通用文字识别' },
  { value: '11', title: '内容审核' },
  { value: '9', title: '语音技术' },
  { value: '10', title: '语言理解' },
  { value: '8', title: '网络智能化' },
  { value: '195', title: '大模型' },
]);
const ability = ref();
const abilityTypeName = ref();
const typeNameValue = ref();
const htmlValue = ref();
const htmlBloon = ref();
const getAbility = () => {
  request('/aiipweb/om-service/capability/edit-show', {
    method: 'GET',
    data: {
      name: abilityName,
    },
  }).then((res: any) => {
    ability.value = res.body.baseInfo.type;
    htmlBloon.value = res.body.techDoc.needTechDoc;
    htmlValue.value = res.body.techDoc.content;
    typeNameValue.value = abilityType
      .filter((item) => {
        return item.value === ability.value;
      })
      .map((item) => {
        return item.title;
      });
    abilityTypeName.value = res.body.baseInfo.name;
  });
};
getAbility();

const handleClick = (e) => {
  e.preventDefault();
};

// ...
window.document.body.style.overflow = 'auto';

const getContainer = () => {
  return document.getElementById('document-container');
};
</script>

<style lang="less" scoped>
/deep/ .ant-menu {
  background: transparent;
  border: none;
}
/deep/.ant-select-arrow {
  top: 40%;
}

.container {
  position: relative;
  margin-bottom: 0;
  display: flex;
  height: calc(100vh);
  overflow: auto;
  .left {
    width: 280px;
    height: calc(100vh - 120px);
    height: 100%;
    overflow: auto;
    padding: 24px;
    background: #f4f8fa;
    border-radius: 2px;

    .directory-tree {
      height: 100%;
    }
    .dir-title {
      font-size: 18px;
      font-weight: 500;
      color: #121f2c;
      line-height: 32px;
    }
  }
  .right {
    padding: 24px 268px 0 32px;
    flex: 1;
    height: calc(100vh - 120px);
    height: 100%;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    .document-viewer {
      height: 100%;
    }
    .title {
      margin: 24px 0 8px 0;
      font-size: 30px;
      font-weight: 500;
      color: #121f2c;
    }
    .update-time {
      margin-bottom: 16px;
      font-size: 14px;
      font-weight: 400;
      color: #a0a6ab;
    }
  }
}
.document-content {
  min-height: calc(100vh - 460px);
  width: auto;
  margin-bottom: 80px;
}
.anchor-wrap {
  .anchor {
    // position: fixed;
    // right: 60px;
    position: absolute;
    top: 62px;
    right: 60px;
    width: 200px;
    max-height: calc(100vh - 520px);
    overflow: auto;
    padding: 4px;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
/deep/ .ant-anchor-wrapper {
  background: transparent;
}
</style>
