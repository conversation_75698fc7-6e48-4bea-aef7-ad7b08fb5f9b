<template>
  <div class="demo" v-if="props.isDemo">
    <div class="title">功能演示</div>
    <div class="img"><img :src="props.path" alt="" /></div>
  </div>
</template>
<script setup lang="ts">
import { defineProps, ref, watch } from 'vue';
const props = defineProps({
  path: {
    type: String,
    default: '',
  },
  isDemo: {
    type: Boolean,
    default: true,
  },
});
// const img = ref('');
// console.log(props.path);
// watch(
//   () => props.path,
//   () => {
// img.value = require('../../../assets/images/preview/result/' + `${props.path}`);
// }
// );
// console.log(img.value);
</script>
<style lang="less" scoped>
.demo {
  .title {
    color: #121f2c;
    text-align: center;
    font-size: 28px;
    line-height: 36px;
    margin-bottom: 24px;
    padding-top: 48px;
    font-weight: 600;
  }
  .img {
    display: flex;
    justify-content: center;
  }
}
</style>
