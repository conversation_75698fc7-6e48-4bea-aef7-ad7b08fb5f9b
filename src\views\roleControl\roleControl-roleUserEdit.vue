<template>
  <div class="user-edit">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '角色管理', query: { platform: $route.query.platform } }"> 角色管理 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>
        <router-link :to="{ name: '角色详情', query: $route.query }">角色详情</router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>编辑角色用户</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="main">
      <a-form :colon="false" class="form-content" ref="ruleForm" :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
        <div class="form-title">编辑角色用户</div>
        <HeadTitle :title="infoTitle[0]" />
        <div class="base-info">
          <a-form-item class="sub-item platform-item" label="平台" required :wrapper-col="subWrapperCol">
            <a-select :disabled="disabled" :value="form.platform1" />
          </a-form-item>
          <a-form-item class="sub-item role-item" label="角色" required :wrapper-col="subWrapperCol">
            <a-input :disabled="disabled" v-model:value="form.roleName" />
          </a-form-item>
          <a-form-item class="sub-item" label="用户" :wrapper-col="subWrapperCol">
            <div class="user-transfer">
              <div class="transfer-left">
                <a-input v-model:value="leftSearchValue" @change="getLeftList" placeholder="用户名" class="transfer-left-top">
                  <template #prefix>
                    <jt-icon type="iconsousuo" style="font-size: 18px" />
                  </template>
                </a-input>
                <a-checkbox-group v-model:value="leftKeys" class="transfer-left-bottom">
                  <a-row>
                    <a-col :span="24" v-for="v in leftValue" :key="v">
                      <a-checkbox :value="v.id" class="left-item">{{ v.name }}</a-checkbox>
                    </a-col>
                  </a-row>
                </a-checkbox-group>
                <div v-if="transferLoading" class="transfer-loading"><a-spin /></div>
              </div>
              <div class="transfer-center">
                <div>
                  <a-button @click="addToRight" :disabled="leftKeys.length < 1" type="primary" size="small">
                    <template #icon><RightOutlined /></template>
                  </a-button>
                </div>
                <div>
                  <a-button @click="addToLeft" :disabled="rightKeys.length < 1" type="primary" size="small">
                    <template #icon><LeftOutlined /></template>
                  </a-button>
                </div>
              </div>
              <div class="transfer-right">
                <div class="transfer-right-top">
                  <span class="top-select">
                    已选择<span>({{ rightValue.length }}人)</span>
                  </span>
                  <span class="transfer-clear" @click="clearAll">清空</span>
                </div>
                <a-checkbox-group class="transfer-right-bottom" v-model:value="rightKeys">
                  <a-row>
                    <a-col :span="24" v-for="v in rightValue" :key="v">
                      <a-checkbox class="right-item" :value="v.id">{{ v.name }}</a-checkbox>
                    </a-col>
                  </a-row>
                </a-checkbox-group>
              </div>
            </div>
            <!-- <p v-if="rightValue.length == 0" class="transfer-tip">至少选择一个用户</p> -->
          </a-form-item>
        </div>
      </a-form>
      <a-button @click="formFinish" type="primary" class="confirm-btn">确定</a-button>
      <a-button @click="goBack" class="cancel-btn">取消</a-button>
    </div>
    <div v-show="editLoading" class="edit-loading"><a-spin /></div>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref } from 'vue';
import { message } from 'ant-design-vue';
import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
import _ from 'lodash';

import { Role } from '@/apis';
import request from '@/request';

import HeadTitle from '@/components/headTitle.vue';

const leftKeys = ref([]);
const rightKeys = ref([]);
export default defineComponent({
  components: {
    HeadTitle,
    LeftOutlined,
    RightOutlined,
  },
  data() {
    return {
      roleId: this.$route.query.id,
      platformName: this.$route.query.platform,
      platformCode: this.$route.query.platformCode,
      editLoading: false,
      baseInfo: {} as any,
      disabled: true,
      infoTitle: ['基本信息'],
      labelCol: { span: 4 },
      wrapperCol: { span: 10 },
      subWrapperCol: { span: 14 },
      form: {
        platform1: this.$route.query.platform,
        roleName: '',
        roleDescription: '',
      },
      transferLoading: false,
      leftKeys,
      rightKeys,
      leftValue: [] as any,
      rightValue: [] as any,
      leftSearchValue: '',
    };
  },
  created() {
    this.getBaseInfo();
    this.getRightList();
  },
  unmounted() {
    this.leftKeys = [];
    this.rightKeys = [];
    this.leftValue = [];
    this.rightValue = [];
  },
  methods: {
    async getBaseInfo() {
      const data = {
        platformCode: this.platformCode,
        roleId: this.roleId,
      };
      const res = await Role.getRoleBasicInfo({ data });
      if (res.code === 0) {
        this.form.platform1 = this.platformName;
        this.form.roleName = res.data.name;
      }
    },
    getLeftList: _.debounce(async function (this: any, e) {
      this.transferLoading = true;
      const res = await Role.getIdByName({ data: { userName: e.target.value } });
      if (res.data) {
        if (!this.rightValue.find((x) => x.id === res.data)) {
          this.leftValue = [{ name: e.target.value, id: res.data }];
        }
      } else {
        this.leftValue = [];
      }
      this.leftKeys = [];
      this.transferLoading = false;
    }, 500),
    async getRightList(this: any) {
      const data = {
        platformCode: this.platformCode,
        roleId: this.roleId,
      };
      const res = await Role.getUserAll({ data });
      if (res.code === 0) {
        const rightArr: { name: string; id: string }[] = [];
        for (const i in res.data) {
          const temp = res.data[i];
          rightArr.push({ name: temp.userName, id: temp.userId });
        }
        this.rightValue = rightArr;
      }
    },
    addToRight(this: any) {
      for (let i = 0; i < this.leftKeys.length; i++) {
        const tempKey = this.leftKeys[i];
        const temp = this.leftValue.find((x) => x.id === tempKey);
        this.rightValue.unshift({ name: temp.name, id: temp.id });
      }
      for (let j = this.leftKeys.length - 1; j >= 0; j--) {
        for (let i = this.leftValue.length - 1; i >= 0; i--) {
          if (this.leftValue[i].id === this.leftKeys[j]) {
            this.leftValue.splice(i, 1);
          }
        }
      }
      // this.leftValue = [];
      this.leftKeys = [];
    },
    addToLeft(this: any) {
      for (let i = 0; i < this.rightKeys.length; i++) {
        const tempKey = this.rightKeys[i];
        const temp = this.rightValue.find((x) => x.id === tempKey);
        if (this.leftSearchValue === temp.name) {
          this.leftValue.unshift({ name: temp.name, id: temp.id });
        }
      }
      for (let j = this.rightKeys.length - 1; j >= 0; j--) {
        for (let i = this.rightValue.length - 1; i >= 0; i--) {
          if (this.rightValue[i].id == this.rightKeys[j]) {
            this.rightValue.splice(i, 1);
          }
        }
      }
      // this.rightValue = [];
      this.rightKeys = [];
    },
    clearAll() {
      this.rightValue = [];
      this.rightKeys = [];
    },
    formFinish(this: any) {
      this.editLoading = true;
      this.$refs.ruleForm
        .validate()
        // 表单校验成功
        .then(async () => {
          const data = {
            platformCode: this.platformCode,
            roleId: this.roleId,
            userIds: this.rightValue.map((x) => x.id),
          };
          const res = await Role.userUpdate({ data });
          this.editLoading = false;
          if (res.code === 0) {
            message.success('编辑角色用户成功');
            this.goBack();
          } else {
            message.error('编辑角色用户失败，请稍后重试');
          }
        })
        .catch((err) => {
          this.editLoading = false;
        });
    },
    goBack(this: any) {
      this.$router.push({ name: '角色详情', query: this.$route.query });
    },
  },
});
</script>
<style lang="less" scoped>
.user-edit {
  .nav {
    height: 58px;
    line-height: 58px;
    padding-left: 20px;
    background: #ffffff;
    font-weight: 500;
    color: #121f2c;
  }
  .main {
    min-width: 1254px;
    padding: 20px 20px 80px;
    margin: 20px;
    background: #ffffff;
    position: relative;
    .confirm-btn {
      width: 120px;
      margin: 28px 10px 0 150px;
    }
    .cancel-btn {
      width: 88px;
    }
    .form-content {
      position: relative;
      .form-title {
        font-size: 16px;
        font-weight: 500;
        color: #121f2c;
        margin-bottom: 24px;
      }
    }
    /deep/.ant-col-4 {
      min-width: 136px;
      max-width: 148px;
    }
    /deep/.ant-space-align-center {
      align-items: unset;
      vertical-align: top;
    }
  }
  .base-info {
    max-width: 1300px;
    margin: 20px 0 0;
    display: flex;
    flex-wrap: wrap;
    padding-left: 14px;
    position: relative;
    .sub-item {
      position: relative;
      width: 800px;
    }
    .role-item {
      position: relative;
      p {
        font-size: 12px;
        color: #ff454d;
        position: absolute;
        top: 32px;
        left: 0;
      }
      /deep/.ant-input {
        width: 480px;
        height: 32px;
      }
    }
    .platform-item {
      position: relative;
      /deep/.ant-select {
        width: 480px;
      }
      /deep/.ant-input {
        width: 480px;
        height: 32px;
      }
      p {
        font-size: 12px;
        color: #f5a623;
        position: absolute;
        top: 32px;
        left: 0;
      }
    }
    .constraint-item {
      /deep/.ant-switch {
        vertical-align: top;
      }
      span {
        width: 315px;
        font-size: 12px;
        color: #ff8b00;
        position: absolute;
        top: 7px;
        left: 63px;
      }
    }
    .school-item {
      margin-right: 480px;
      margin-bottom: 0;
      /deep/ .ant-form-item-children {
        display: flex;
      }
      /deep/ .ant-form-item-label {
        line-height: 32px;
      }
    }
    .introduction-item {
      width: 800px;
      position: relative;
      /deep/.ant-form-item-control-wrapper {
        width: 480px;
      }
      /deep/.ant-input {
        width: 480px;
        height: 88px;
        resize: none;
      }
      /deep/.ant-col-10 {
        min-width: 480px;
      }
      .count-area {
        position: absolute;
        bottom: -21px;
        right: 0px;
        color: #606972;
      }
    }
    .img-uploader {
      position: absolute;
      top: 10px;
      right: -140px;
    }
  }
  .tip-item {
    color: #606972;
    padding-left: 150px;
  }
  .tip-item1 {
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #efefef;
    margin: 0 0 40px;
  }
  .user-info {
    position: relative;
    left: -18px;
    .transfer-loading {
      width: 247px;
      height: 232px;
      text-align: center;
      padding-top: 100px;
      position: absolute;
      top: 98px;
      left: 0px;
      z-index: 2;
    }
    .transfer-right-title {
      width: 38px;
      background: #ffffff;
      font-size: 12px;
      color: #333333;
      z-index: 1;
      position: absolute;
      top: 19px;
      right: -18px;
      span:nth-child(2) {
        display: inline-block;
        width: 130px;
        min-width: 26px;
        color: #bbbbbb;
        margin-left: 8px;
        position: absolute;
        top: 0;
        left: 34px;
      }
    }
    .iconsousuo1 {
      background: #ffffff;
      position: absolute;
      top: 26px;
      left: 43px;
      z-index: 2;
    }
    /deep/.anticon svg {
      color: #bec2c5;
      width: 18px;
      height: 18px;
    }
    .user-clear {
      width: 54px;
      height: 54px;
      line-height: 54px;
      text-align: center;
      color: #0082ff;
      font-size: 12px;
      cursor: pointer;
      position: absolute;
      top: 0;
      right: -220px;
      z-index: 2;
    }
    /deep/.ant-transfer-list {
      width: 248px;
      height: 355px;
      max-width: 248px;
      max-height: 331px;
      .ant-tree,
      .ant-transfer-list-content {
        font-size: 12px !important;
        color: #555555;
      }
    }
    /deep/.ant-transfer {
      & > :first-child > :first-child {
        font-size: 0;
        position: absolute;
        top: 10px;
        border: none;
        overflow: hidden;
      }
      & > div:nth-child(3) > div:nth-child(1) {
        height: 54px;
        font-size: 0;
      }
      & > div:nth-child(3) > div:nth-child(2) > ul {
        position: relative;
        top: -12px;
        min-height: 262px;
      }
      & > div:nth-child(3) > div:nth-child(2) > :first-child {
        span {
          border: none;
        }
        height: 0;
        border: none;
        overflow: hidden;
      }
    }
  }
  .edit-loading {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.45);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.transfer-tip {
  font-size: 12px;
  color: #ff454d;
  position: absolute;
  left: 0;
  bottom: -31px;
}
.user-transfer {
  min-width: 570px;
  display: flex;
  align-items: center;
  .transfer-left {
    width: 248px;
    height: 355px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    position: relative;
    .transfer-left-top {
      margin: 8px;
      width: 230px;
      height: 28px;
    }
    .transfer-left-bottom {
      width: 100%;
      height: 304px;
      overflow: auto;
      padding-left: 8px;
      .left-item {
        min-width: 230px;
        margin-bottom: 12px;
      }
    }
    .transfer-loading {
      position: absolute;
      top: 37px;
      left: 0;
      width: 100%;
      height: 316px;
      text-align: center;
      line-height: 316px;
    }
  }
  .transfer-center {
    margin: 0 20px;
    div {
      margin-bottom: 8px;
    }
  }
  .transfer-right {
    width: 248px;
    height: 355px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #d9d9d9;
    .transfer-right-top {
      display: flex;
      justify-content: space-between;
      padding: 8px;
      margin-bottom: 8px;
      border-bottom: 1px solid #d9d9d9;
      .top-select {
        font-size: 12px;
        color: #333333;
        span {
          font-size: 12px;
          color: #bbbbbb;
          margin-left: 5px;
        }
      }
      .transfer-clear {
        font-size: 12px;
        color: #0082ff;
        cursor: pointer;
      }
    }
    .transfer-right-bottom {
      width: 100%;
      height: 304px;
      overflow: auto;
      padding-left: 8px;
      .right-item {
        width: 230px;
        margin-bottom: 12px;
      }
    }
  }
}
/deep/.ant-form-item-explain {
  font-size: 12px;
}
/deep/.ant-breadcrumb-link {
  font-weight: 400;
}
/deep/.ant-form-item-label > label::after {
  margin-right: 16px;
}
</style>
