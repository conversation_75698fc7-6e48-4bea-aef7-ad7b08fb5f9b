import { axiosWithNoToken, axios } from '../request';

// 通过url下载文件
export function downloadFile({ url, fileName = 'new_File', needToken = true, autoGetFileName = true, method = 'get', data = {} }) {
  if (!url) return;
  if (!fileName) return;
  const request = needToken ? axios : axiosWithNoToken;
  let innerFileName = fileName;
  const formData = {
    url,
    method,
    responseType: 'blob',
    data: {},
  };
  if (method.toLocaleLowerCase() === 'post') {
    formData.data = data;
  }
  return request(formData as any)
    .then((res) => {
      if (autoGetFileName) {
        innerFileName = res.headers['content-disposition'].replace(/\w+;filename=(.*)/, '$1');
        innerFileName = decodeURIComponent(innerFileName);
      }
      return res.data;
    })
    .then((res) => {
      const blob = new Blob([res], { type: 'application/octet-stream' });
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = innerFileName;
      link.click();
    });
}
