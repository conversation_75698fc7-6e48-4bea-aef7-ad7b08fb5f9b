<template>
  <div class="user-control-container">
    <sub-header title="平台文档管理"></sub-header>
    <a-select :value="activeTab" @change="tabChange" class="platform-select">
      <a-select-option v-for="v in listTab" :key="v" :value="v.platformName"> {{ v.platformName }} </a-select-option>
    </a-select>
    <a-spin :spinning="isLoding">
      <Container>
        <container-item>
          <div class="top-bar">
            <h3>{{ activeTab }}文档</h3>
            <div style="display: flex">
              <a-space>
                <a-button @click="createDirectory">
                  <template #icon><PlusOutlined /></template>
                  新建目录
                </a-button>
                <a-button @click="editPage" type="primary">
                  <template #icon><PlusOutlined /></template>
                  新建页面
                </a-button>
              </a-space>
            </div>
          </div>
          <table-tree :showEmptyDescription="true" type="HELP" :loading="tableLoading" @createDirectory="createDirectory" @editPage="editPage" :dataSource="dataSource" :expandKeys="expandKeys" :columns="detailColumsForTableTree('helpDocument')" @editDirectory="editDirectory" @refreshData="initTableData" :platformCode="platformCode" />
          <Pagination :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
        </container-item>
      </Container>
    </a-spin>
    <a-modal :visible="visible" :title="editTitle" @cancel="handleCancel" @ok="handleOk" :maskClosable="false">
      <a-form :model="formState" ref="formRef" :rules="rules" v-bind="layout" :colon="false">
        <a-form-item label="平台名称" required>
          <a-input v-model:value="activeTab" disabled />
        </a-form-item>
        <a-form-item label="目录名称" name="catalogName">
          <a-input v-model:value="formState.catalogName" @blur="catalogNameCheck($event)" placeholder="请输入" />
        </a-form-item>
        <a-form-item label="父目录" name="parentId">
          <a-select v-model:value="formState.parentId" placeholder="请选择父目录，父目录至多一级">
            <a-select-option v-for="item in parentCatalogList" :key="item.catalogId" :value="item.catalogId">{{ item.catalogName }}</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import tableTree from '@/components/business/tableTree.vue';
import { detailColumsForTableTree } from '@/constants';
import { findParentIdById, formateDataSource } from '@/utils/format';
import { HelpCenter } from '@/apis';
const { getList, selectUserAccess, isCatalogExist, getCatalogList, updateCatalog, addCatalog } = HelpCenter;
const layout = {
  labelCol: {
    span: 5,
  },
  wrapperCol: {
    span: 18,
  },
};
export default defineComponent({
  components: {
    PlusOutlined,
    subHeader,
    tableTree,
    Container,
    ContainerItem,
    Pagination,
  },
  data() {
    return {
      tableLoading: true,
      dataSource: [] as any,
      expandKeys: [] as any,
      activeTab: '',
      listTab: [] as any,
      catalogId: 0,
      platformCode: '',
      visible: false,
      editTitle: '',
      formState: {
        catalogName: undefined,
        parentId: undefined,
      },
      catalogNameRepetition: false,
      parentCatalogList: [] as any,
      layout,
      doubleCatalog: false,
      isLoding: false,
      detailColumsForTableTree,
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
    };
  },
  created() {
    this.getTabList();
  },
  computed: {
    rules(this: any) {
      return {
        catalogName: [
          { required: true, whitespace: true, trigger: 'change', message: '请输入目录名称' },
          {
            message: '不超过20个字符',
            max: 20,
          },
        ],
        parentId: [{ required: true, message: '请选择父目录' }],
      };
    },
  },
  methods: {
    async getTabList(this: any) {
      const platform = await selectUserAccess({ name: 'HELP', data: { accessType: 'platform' } });
      const others = await selectUserAccess({ name: 'HELP', data: { accessType: 'other' } });
      const models = await selectUserAccess({ name: 'HELP', data: { accessType: 'bg_model' } });
      if (platform.state === 'OK' && others.state === 'OK' && models.state === 'OK') {
        for (let i = 0; i < models.body.length; i++) {
          platform.body.push(models.body[i]);
        }
        for (let i = 0; i < others.body.length; i++) {
          platform.body.push(others.body[i]);
        }
        for (let i = 0; i < platform.body.length; i++) {
          if (platform.body[i].clickable == 1) {
            this.listTab.push({
              platformName: platform.body[i].platformName,
              platformCode: platform.body[i].platformCode,
            });
          }
        }
        if (this.$route.query.platformName) {
          this.activeTab = this.$route.query.platformName;
          this.platformCode = this.$route.query.platformCode;
        } else {
          this.platformCode = this.listTab[0].platformCode;
          this.activeTab = this.listTab[0].platformName;
        }
        this.initTableData();
      }
    },
    tabChange(e) {
      this.activeTab = e;
      for (let i = 0; i < this.listTab.length; i++) {
        if (this.listTab[i].platformName == e) {
          this.platformCode = this.listTab[i].platformCode;
        }
      }
      this.getTableList();
    },
    // 查询目录名称是否已存在
    catalogNameCheck(this: any, e) {
      if (this.oldCatalogName === e.target.value) {
        this.catalogNameRepetition = false;
        return;
      }
      isCatalogExist({
        name: 'HELP',
        type: 'POST',
        data: { accessPlatformCode: this.platformCode, accessPlatformName: this.activeTab, catalogName: e.target.value, id: 0, parentId: 0 },
      }).then((res: any) => {
        if (res.state === 'OK') {
          if (res.body) message.error('目录名称重复');
          this.catalogNameRepetition = res.body;
        }
      });
    },
    createDirectory(this: any) {
      this.doubleCatalog = false;
      this.editTitle = '新建目录';
      this.visible = true;
      this.getParentCatalogList();
    },
    editDirectory(this: any, record) {
      this.doubleCatalog = false;
      if (record.type === 'catalog') {
        if (record.children && record.children.length != 0) {
          for (let i = 0; i < record.children.length; i++) {
            if (record.children[i].type === 'catalog') {
              this.doubleCatalog = true;
            }
          }
        }
        this.editTitle = '编辑目录';
        this.visible = true;
        this.getParentCatalogList();
        this.formState.catalogName = record.name;
        this.formState.parentId = record.pid;
        this.oldCatalogName = record.name;
        this.catalogId = record.id;
      } else {
        // 跳转到编辑文章页面的路由
        this.editPage(record);
      }
    },
    async getParentCatalogList(this: any) {
      getCatalogList({
        name: 'HELP',
        data: { flag: '1', accessPlatform: this.platformCode }, // 只获取1级目录
      }).then((res: any) => {
        if (res.state === 'OK') {
          res.body.unshift({ catalogId: 0, catalogName: '无' });
          for (let i = 0; i < res.body.length; i++) {
            if (res.body[i].catalogName === this.formState.catalogName) {
              res.body.splice(i, 1);
            }
          }
          this.parentCatalogList = res.body;
        }
      });
    },
    handleOk(this: any) {
      if (this.formState.parentId != 0 && this.doubleCatalog) {
        message.error('当前目录下已存在子目录，编辑失败');
        return;
      }
      this.$refs.formRef.validate().then(async () => {
        if (this.catalogNameRepetition) {
          message.error('目录名称重复');
          return;
        }
        this.isLoding = true;
        let isEdit = this.editTitle === '编辑目录';
        const params = {
          name: 'HELP',
          type: 'POST',
          data: { accessPlatformCode: this.platformCode, accessPlatformName: this.activeTab, catalogName: this.formState.catalogName, parentId: this.formState.parentId, id: this.catalogId },
        };
        const res: any = isEdit ? await updateCatalog(params) : await addCatalog(params);
        if (res.state === 'OK') {
          message.success(`目录"${this.formState.catalogName}"${isEdit ? '编辑' : '新建'}成功`);
          this.handleCancel();
          this.initTableData();
        } else {
          this.isLoding = false;
        }
      });
    },
    handleCancel(this: any) {
      this.$refs.formRef.resetFields();
      this.formState.catalogName = undefined;
      this.formState.parentId = undefined;
      this.catalogNameRepetition = false;
      this.visible = false;
      this.isLoding = false;
    },
    getTableList(this: any) {
      this.page = { ...this.page, ...{ pageNum: 1, pageSize: 10 } };
      this.initTableData();
    },
    editPage(this: any, record) {
      this.$router.push({ path: '/platform-document/helpcenterControl-createPage', query: { pageId: record?.id, platformName: this.activeTab, platformCode: this.platformCode } });
    },
    // 初始化table数据
    async initTableData(expandcatalogId: number | undefined = undefined) {
      this.tableLoading = true;
      const { pageNum, pageSize } = this.page;
      await getList({ name: 'HELP', type: 'POST', data: { accessPlatform: this.platformCode, pageNum, pageSize } }).then((res: any) => {
        if (res.state === 'OK') {
          // 格式化 添加是否可排序 及发布 删除的显隐控制
          this.dataSource = formateDataSource(res.body.list || []);
          // 新建和编辑页面跳转过来时需要展开对应的父项
          if (expandcatalogId) {
            const parentId: any = findParentIdById(this.dataSource, expandcatalogId);
            parentId && this.expandKeys.push(parentId);
            this.expandKeys.push(expandcatalogId);
          }
          this.page.total = res.body.total;
        } else {
          this.dataSource = [];
        }
      });
      this.tableLoading = false;
    },
    // 分页操作
    changePageNum(pageNum) {
      this.page.pageNum = pageNum;
      this.initTableData();
    },
    changePageSize(pageSize) {
      this.page.pageNum = 1;
      this.page.pageSize = pageSize;
      this.initTableData();
    },
  },
});
</script>
<style lang="less" scoped>
.user-control-container {
  position: relative;
  .platform-select {
    width: 210px;
    position: absolute;
    top: 15px;
    right: 40px;
  }
  .jt-pagination {
    display: flex;
    justify-content: space-between;
    padding: 16px 0px;
    padding-bottom: 0;
  }
}
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /deep/.ant-tabs-nav {
    margin: 0;
  }
  /deep/.ant-tabs-nav::before {
    border: none;
  }
  /deep/.ant-tabs-tab {
    width: 136px;
    height: 32px;
    display: flex;
    justify-content: center;
    margin: 0;
    font-size: 14px;
    color: #606972;
    line-height: 20px;
  }
  /deep/.ant-tabs-tab-active {
    font-weight: 500;
    color: #0082ff;
  }
}
.spin {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}
</style>
