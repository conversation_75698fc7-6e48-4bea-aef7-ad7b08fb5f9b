import { requestBlob } from '@/request';
// 是否需要功能演示枚举-step2
export const NEED_FUNCTION_DEMONSTRATION_ENUM = {
  NEED: true, // 是
  NOT_NEED: false, // 否
};

// 上传媒体类型
export const MEDIA_ENUM = {
  IMG: '0', // 图片
  VIDEO: '1', // 视频
  AUDIO: '2', // 音频
  TEXT: '3', // 文本
};

// 转化成base64
export function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}

export const getVideoInfo = async (fileUrl) => {
  return new Promise(function (resolve, reject) {
    try {
      const oVideo = document.createElement('video');
      oVideo.preload = 'auto';
      oVideo.onloadedmetadata = function (e) {
        // 视频总长度，秒为单位
        resolve({
          oVideo,
          duration: oVideo.duration,
          width: oVideo.videoWidth,
          height: oVideo.videoHeight,
        });
      };
      oVideo.src = fileUrl;
    } catch (error) {
      reject(null);
    }
  });
};

export const getVideoPosterInfo = (videoInfo) => {
  return new Promise((resolve) => {
    const { oVideo, width, height } = videoInfo;
    oVideo.addEventListener('canplay', () => {
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      // 将视频对象直接绘入canvas
      ctx?.drawImage(oVideo, 0, 0, width, height);
      const posterUrl = canvas.toDataURL('image/jpg');
      resolve({ posterUrl });
    });
  });
};

// 是否上架到官网枚举
export const IS_ONLINE_ENUM = {
  ONLINE: true, // 是
  NOT_ONLINE: false, // 否
};

// 能力类别的枚举
export const CAPACITY_CATEGORY_ENUM = {
  FACE_RECOFNITION: '2', // 人脸识别
  BODY_RECOFNITION: '3', // 人体与行为识别
  IMAGE_UNDERSTANDING: '7', // 图像理解
  IMAGE_RECOGNITION: '4', // 图像识别
  CARD_CHARACTER_RECOGNITION: '5', // 卡证文字识别
  UNIVERSAL_CHARACTER_RECOGNITION: '6', // 通用文字识别
  CONTENT_REVIEW: '11', // 内容审核
  VOICE_TECHNOLOGY: '9', // 语音技术
  LANGUAGE_UNDERSTANDING: '10', // 语言理解
  NETWORK_INTELLIGENCE: '8', // 网络智能化
  FOUNDATION_MODELS: '195', // 大模型
};

// 能力类别的枚举，其实暂时只是用来是备用的提供一个默认值
export const CAPACITY_CATEGORY_OPTIONS = [
  { value: CAPACITY_CATEGORY_ENUM.FACE_RECOFNITION, label: '人脸识别' },
  { value: CAPACITY_CATEGORY_ENUM.BODY_RECOFNITION, label: '人体与行为识别' },
  { value: CAPACITY_CATEGORY_ENUM.IMAGE_UNDERSTANDING, label: '图像理解' },
  { value: CAPACITY_CATEGORY_ENUM.IMAGE_RECOGNITION, label: '图像识别' },
  { value: CAPACITY_CATEGORY_ENUM.CARD_CHARACTER_RECOGNITION, label: '卡证文字识别' },
  { value: CAPACITY_CATEGORY_ENUM.UNIVERSAL_CHARACTER_RECOGNITION, label: '通用文字识别' },
  { value: CAPACITY_CATEGORY_ENUM.CONTENT_REVIEW, label: '内容审核' },
  { value: CAPACITY_CATEGORY_ENUM.VOICE_TECHNOLOGY, label: '语音技术' },
  { value: CAPACITY_CATEGORY_ENUM.LANGUAGE_UNDERSTANDING, label: '语言理解' },
  { value: CAPACITY_CATEGORY_ENUM.NETWORK_INTELLIGENCE, label: '网络智能化' },
  { value: CAPACITY_CATEGORY_ENUM.FOUNDATION_MODELS, label: '大模型' },
];

// 背景图枚举，提供一个默认值
export const BACKGROUND_IMG_ENUM = {
  FACE: '0', // 人脸类
  IMG: '1', // 图片类
  VOICE: '2', // 语音类
  TEXT_R: '3', // 文字识别类
  TEXT: '4', // 文本类
};

// 背景图类别的枚举，其实暂时只是用来是备用的提供一个默认值
export const BACKGROUND_IMG_OPTIONS = [
  { value: BACKGROUND_IMG_ENUM.FACE, label: '人脸类' },
  { value: BACKGROUND_IMG_ENUM.IMG, label: '图片类' },
  { value: BACKGROUND_IMG_ENUM.VOICE, label: '语音类' },
  { value: BACKGROUND_IMG_ENUM.TEXT_R, label: '文字识别类' },
  { value: BACKGROUND_IMG_ENUM.TEXT, label: '文本类' },
];

// 功能演示模块媒体类型的枚举选项，用来备用，仅仅提供一个默认值
export const MEDIA_TYPE_OPTIONS = [
  { value: MEDIA_ENUM.IMG, label: '图片类' },
  { value: MEDIA_ENUM.VIDEO, label: '视频类' },
  { value: MEDIA_ENUM.AUDIO, label: '音频类' },
  { value: MEDIA_ENUM.TEXT, label: '文本类' },
];

// 生成能力类别图片链接
export const getPicUrl = async (type, name) => {
  try {
    const res = (await requestBlob('/aiipweb/om-service/os/getObject', { method: 'GET', data: { capabilityCategory: type, object: name, category: 5 } })) as string;
    return res || '';
  } catch (e) {
    console.log(e);
    return require('@/assets/images/aiControl/step1.png');
  }
};

// 生成能力类别与媒体类型对应，有用的
export const MEDIA_TYPE_MAP = new Map([
  [CAPACITY_CATEGORY_ENUM.FACE_RECOFNITION, [MEDIA_ENUM.IMG]], // 人脸识别-图片类
  [CAPACITY_CATEGORY_ENUM.BODY_RECOFNITION, [MEDIA_ENUM.IMG, MEDIA_ENUM.VIDEO]], // 人体与行为分析-图片类，视频类
  [CAPACITY_CATEGORY_ENUM.IMAGE_RECOGNITION, [MEDIA_ENUM.IMG, MEDIA_ENUM.VIDEO]], // 图像识别-图片类，视频类
  [CAPACITY_CATEGORY_ENUM.NETWORK_INTELLIGENCE, [MEDIA_ENUM.IMG]], // 网络智能化-图片类
  [CAPACITY_CATEGORY_ENUM.VOICE_TECHNOLOGY, [MEDIA_ENUM.AUDIO]], // 语音技术-语音类
  [CAPACITY_CATEGORY_ENUM.CARD_CHARACTER_RECOGNITION, [MEDIA_ENUM.IMG]], // 卡证文字识别-图片类
  [CAPACITY_CATEGORY_ENUM.UNIVERSAL_CHARACTER_RECOGNITION, [MEDIA_ENUM.IMG]], // 通用文字识别-图片类
  [CAPACITY_CATEGORY_ENUM.IMAGE_UNDERSTANDING, [MEDIA_ENUM.IMG, MEDIA_ENUM.VIDEO]], // 图像理解-图片类
  [CAPACITY_CATEGORY_ENUM.LANGUAGE_UNDERSTANDING, [MEDIA_ENUM.TEXT]], // 语言理解-文本类
  [CAPACITY_CATEGORY_ENUM.CONTENT_REVIEW, [MEDIA_ENUM.TEXT]], // 内容审核-文本类
  [CAPACITY_CATEGORY_ENUM.FOUNDATION_MODELS, [MEDIA_ENUM.IMG, MEDIA_ENUM.VIDEO, MEDIA_ENUM.AUDIO, MEDIA_ENUM.TEXT]], // 大模型-图片类、视频类、语音类、文本类
]);
