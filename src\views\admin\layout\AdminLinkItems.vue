<template>
  <ul class="link-list">
    <li @click="openHelpCenter"><question-circle-outlined style="font-size: 16px; margin-right: 4px" /><span>帮助</span></li>
    <li><admin-message-alert appName="adminMessageAlert" /></li>
    <li class="user-item">
      <a-dropdown v-model:visible="visible" placement="bottom">
        <div class="user-box">
          <img class="avatar" :src="userInfo.image || defaultAvatar" alt="" />
          <p :title="userInfo.userName">{{ userInfo.userName }}</p>
          <span :class="visible ? 'drop-icon down' : 'drop-icon'">
            <caret-down-outlined />
          </span>
        </div>

        <template #overlay>
          <a-menu class="jt-login-menu" style="width: 128px">
            <a-menu-item @click="handleNavToAdmin">
              <div class="flex-vertical">
                <jt-icon style="font-size: 16px; padding-right: 6px" type="iconsetting-outlined" />
                <span>账号管理</span>
              </div>
            </a-menu-item>
            <a-menu-item @click="handleLogout">
              <div class="flex-vertical">
                <jt-icon style="font-size: 16px; padding-right: 6px" type="iconexport" />
                <span>退出登录</span>
              </div>
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </li>
  </ul>
</template>
<script>
import { logout } from '@/keycloak';
import AdminMessageAlert from './AdminMessageAlert.vue';
import { openInNewTab } from '../utils';
import { getEnvConfig } from '@/config';
import { QuestionCircleOutlined, CaretDownOutlined } from '@ant-design/icons-vue';

export default {
  name: 'LinkItems',
  components: {
    AdminMessageAlert,
    QuestionCircleOutlined,
    CaretDownOutlined,
  },
  computed: {
    userInfo() {
      return this.$store.state.userInfo;
    },
  },
  data() {
    return {
      visible: false,
      defaultAvatar: require('@/assets/images/avatar_admin.png'),
      showAccountManagementEnv: getEnvConfig('SHOW_ACCOUNT_MANAGEMENT') === '1',
    };
  },

  methods: {
    openHelpCenter() {
      openInNewTab('./common-helpcenter');
    },
    handleLogout() {
      let redirectUrl = location.href;
      if (this.$route.path === '/404' || this.$route.path === '/401') {
        redirectUrl = window.location.origin + window.location.pathname;
      }
      logout(redirectUrl);
    },
    handleNavToAdmin() {
      if (this.$route.path === '/admin') {
        location.reload();
      } else {
        openInNewTab('./#/admin');
      }
    },
  },
};
</script>
<style lang="less" scoped>
.link-list {
  display: flex;
  align-items: center;
  height: 50px;
  font-size: 12px;
  li {
    text-align: center;
    cursor: pointer;
    color: rgba(0, 20, 26, 0.7);
    transition: 0.3s all ease;
  }
  li:not(:last-child) {
    margin-right: 32px;
  }
  .user-item {
    width: auto;
    height: 100%;
    max-width: 120px;
    margin-right: 32px;
  }
  li:hover:not(:last-child) {
    color: #00b3cc;
  }
  .user-box {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 100%;
    .avatar {
      width: 28px;
      height: 28px;
      margin-right: 8px;
      border-radius: 50%;
    }
    p {
      line-height: 20px;
      max-width: 85px;
      text-overflow: ellipsis;
      overflow: hidden;
      color: rgba(0, 20, 26, 0.7);
      margin-right: 4px;
    }
    .drop-icon {
      color: rgba(0, 20, 26, 0.25);
      transition: transform 0.3s ease;
    }
    .drop-icon.down {
      transform: rotate(180deg);
    }
  }
}
</style>
<style lang="less">
.flex-vertical {
  display: flex;
  align-items: center;
}
.jt-login-menu.ant-dropdown-menu .ant-dropdown-menu-item {
  color: #606972;
  background-color: transparent;
  &:hover {
    color: #00b3cc;
    background: #e6f7fa;
  }
}
</style>
