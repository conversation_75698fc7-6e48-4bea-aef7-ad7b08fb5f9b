<template>
  <div class="import-tip">
    <div class="import-tip-header">
      <span>正在处理{{ totalProcess }}项</span>
      <div class="operation-btns">
        <UpOutlined v-if="importTooltipFold" @click="handleSwitch" />
        <DownOutlined v-else @click="handleSwitch" />
        <CloseOutlined @click="handleClose" style="margin-left: 30px; cursor: pointer" />
      </div>
    </div>
    <div class="import-tip-content" :style="`height:${importTooltipFold ? 0 : 240}px;overflow-y:${importTooltipFold ? 'hidden' : 'auto'};`">
      <div v-for="item in importList" :key="item.id" class="import-tip-item">
        <div class="import-item-description">
          <div style="width: 225px; display: flex">
            <jt-icon type="iconfile-excel-fill" class="excel-icon"></jt-icon> <span :title="item.fileName" class="import-file-name">{{ item.fileName }}</span>
          </div>
          <div style="width: 70px; display: flex; align-items: center; margin-right: 5px">
            <jt-icon :type="!item.status ? 'icondengdaizhong' : 'iconwancheng-tianchong'" :class="item.status === 2 ? 'success-icon' : 'wait-icon'"></jt-icon><span>{{ item.status === 2 ? '处理完成' : '处理中' }}</span>
          </div>
          <span
            v-if="item.status === 2"
            @click="
              () => {
                handledetail(item.taskId);
              }
            "
            class="operation-txt"
            style="padding-right: 6px"
            >详情</span
          >
          <span
            v-if="item.status"
            @click="
              () => {
                handleDeleteTaskById(item.taskId);
              }
            "
            class="operation-txt"
            >关闭</span
          >
        </div>
        <div v-if="Number(item.processRate) != 1" :style="`width:${item.processRate * 100}%`" class="percent-progress"></div>
      </div>
    </div>
  </div>
</template>

<script lang="jsx">
import { computed, defineComponent, ref, onMounted, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import { DownOutlined, UpOutlined, CloseOutlined, ExclamationCircleFilled, CheckCircleFilled } from '@ant-design/icons-vue';
import { Modal } from 'ant-design-vue';
import request from '@/request';
import { useLoadingRequest } from './loading-request';
import { downloadFile } from '@/utils/file';

let intervalId = null;

export default defineComponent({
  components: {
    DownOutlined,
    UpOutlined,
    CloseOutlined,
  },
  setup(props, { emit }) {
    let importList = ref([]);
    const userCount = ref(0);
    const infoStatus = ref(false);
    const store = useStore();
    const importTooltipFold = computed(() => store.state.importTooltipFold);
    const importTooltipVisible = computed(() => store.state.importTooltipVisible);

    const handleSwitch = () => {
      store.commit('UPDATE_TOOLTIPFOLD');
    };

    const totalProcess = computed(() => {
      return importList.value.filter((item) => {
        return !item.status;
      }).length;
    });

    const handleDownload = (id) => {
      request('/examination/exportFailedStudent', { data: { taskId: id } }).then((res) => {
        if (res.state === 'OK' && res.body) {
          window.open(res.body);
        }
      });
    };
    // 关闭提示信息
    const handleClose = () => {
      Modal.confirm({
        icon: <ExclamationCircleFilled style={{ color: '#FF454D', fontSize: '16px' }} />,
        title: (
          <div style={{ display: 'flex', marginBottom: '16px' }}>
            <span style={{ fontSize: '14px', color: '#121F2C', fontWeight: 500 }}>确认关闭吗？</span>
          </div>
        ),
        content: <p style={{ color: '#606972', fontSize: '12px' }}>关闭后不可再次查看已处理完成项，后台批量导入任务的执行不受影响</p>,
        onOk() {
          const ids = importList.value.map((item) => {
            if (item.status) {
              return item.taskId;
            }
          });
          if (ids.length > 0) {
            handleDeleteTaskById(ids);
          }
          closeInterval();
          emit('toggleVisible', false);
        },
      });
    };
    const handleDownloadFile = (id) => {
      const url = `/web/admin/um/v1/user/import-fail-user-download?taskId=${id}`;
      downloadFile({ url });
    };

    // 根据id获取任务详情
    const handledetail = async (id) => {
      const [loadingRequest, loading] = useLoadingRequest();
      if (loading.value) {
        return;
      }
      //  /keycloak/web/admin/user/getTask
      const res = await loadingRequest('/web/admin/um/v1/user/import-task-detail', { data: { taskId: id } });
      if (res.msg === 'OK') {
        const { failCount, userCount } = res.data;
        let successNum = userCount - failCount;
        let importStatus = '导入成功'; // 导入成功 ,全部失败 ,全部成功
        let msg = `您当前导入文件中包含${userCount}个用户\n，其中${successNum}个导入成功，${failCount}个导入失败`;
        if (failCount === userCount && successNum === 0) {
          // 全部失败
          msg = `您当前导入文件中包含${userCount}个用户，全部导入失败`;
          importStatus = '全部失败';
        }
        if (failCount === 0 && successNum === userCount) {
          // 全部成功
          msg = `您当前导入文件中包含${userCount}个用户，全部导入成功`;
          importStatus = '全部成功';
        }

        Modal.info({
          icon: importStatus === '全部失败' ? <ExclamationCircleFilled style={{ color: '#FF454D', fontSize: '16px' }} /> : importStatus === '全部成功' ? <CheckCircleFilled style={{ color: '#10c038', fontSize: '16px' }} /> : <ExclamationCircleFilled style={{ color: '#FA8014', fontSize: '16px' }} />,
          title: (
            <div style={{ display: 'flex', marginBottom: '16px' }}>
              <span style={{ fontSize: '14px', color: '#121F2C', fontWeight: 500 }}>{`导入${importStatus === '全部失败' ? '失败' : importStatus === '全部成功' ? '成功' : '结果'}提示`}</span>
            </div>
          ),
          content: () => (
            <div v-if={failCount > 0}>
              <p style={{ color: '#606972', fontSize: '12px', marginBottom: '8px' }}>{msg}</p>
              {importStatus !== '全部成功' && (
                <a href="javascript:;" onClick={() => handleDownloadFile(id)} style={{ fontSize: '12px' }}>
                  下载导入失败列表
                </a>
              )}
            </div>
          ),
          okText: '确定',
        });
      }
    };

    const compareTaskStatus = (oldValue, newValue) => {
      if (oldValue.length === 0) {
        for (let i = 0; i < newValue.length; i++) {
          if (newValue[i].status) {
            return true;
          }
        }
      } else {
        for (let i = 0; i < newValue.length; i++) {
          let taskId = newValue[i].taskId;
          let status = newValue[i].status;
          for (let j = 0; j < oldValue.length; j++) {
            if (oldValue[j].taskId === taskId && oldValue[j].status !== status && status) {
              return true;
            }
          }
        }
      }
    };

    // 获取当前导入的进度列表
    const getImportList = async () => {
      // /keycloak/web/admin/user/getTasks
      const res = await request('/web/admin/um/v1/user/import-task-list');
      if (res.msg === 'OK') {
        // 有任务完成，触发刷新列表
        if (compareTaskStatus(importList.value, res.data)) {
          emit('refreshTable');
        }
        importList.value = res.data;
        // 检查 totalProcess，如果为 0，则停止轮询
        if (totalProcess.value === 0) {
          closeInterval(); // 停止轮询
        }
        // 获取导入数组的最后一项
        const lastItem = importList.value[importList.value.length - 1];
        userCount.value = lastItem.userCount;
        infoStatus.value = lastItem.status === 2 ? '失败' : '成功';
      }
    };

    // 根据id值删除任务
    const handleDeleteTaskById = async (id) => {
      const taskIds = Array.isArray(id) ? id : [id];
      const iscloseTooltip = importList.value.length === 1;
      // /keycloak/web/admin/user/deleteTasks
      const res = await request('/web/admin/um/v1/user/import-task-close', { method: 'POST', data: taskIds });
      if (res.msg === 'OK') {
        // 如果剩一个任务已完成，那就直接关闭掉定时器和提示框
        if (iscloseTooltip) {
          closeInterval();
          emit('toggleVisible', false);
        } else {
          getImportList();
        }
      }
    };

    const startInterval = () => {
      getImportList();
      if (!intervalId) {
        intervalId = setInterval(getImportList, 2 * 1000);
      }
    };

    // 清除定时器
    const closeInterval = () => {
      if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
      }
    };

    // 如果刚进来的时候提示框已经打开，则启动定时器
    onMounted(() => {
      if (importTooltipVisible.value) {
        startInterval();
      }
    });

    onBeforeUnmount(() => {
      closeInterval();
    });

    return {
      importList,
      importTooltipFold,
      totalProcess,
      userCount,
      infoStatus,
      handleSwitch,
      handleClose,
      getImportList,
      handledetail,
      handleDeleteTaskById,
      startInterval,
      handleDownload,
    };
  },
});
</script>

<style lang="less" scoped>
.import-tip {
  width: 400px;
  box-shadow: 0px -2px 20px 0px rgba(0, 0, 0, 0.15);
  border-radius: 2px 2px 0px 0px;
  position: fixed;
  right: 0px;
  bottom: 0px;
  z-index: 2;
  .import-tip-header {
    height: 40px;
    background: rgb(21, 54, 84);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 20px 0px 16px;
    color: white;
  }
  .import-tip-content {
    background-color: white;
    transition: height 0.6s;
    overflow-y: auto;
    .import-tip-item {
      height: 40px;
      border-bottom: 1px solid #e9ebef;
      position: relative;
      .import-item-description {
        position: absolute;
        left: 0px;
        top: 0px;
        height: 100%;
        width: 100%;
        padding: 0px 20px;
        color: #555555;
        font-size: 12px;
        display: flex;
        align-items: center;
        .import-file-name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .excel-icon {
          color: #51ad6c;
          padding-right: 12px;
          font-size: 18px;
        }
        .wait-icon {
          padding-right: 6px;
          color: #c2c5cf;
          font-size: 14px;
        }
        .process-icon {
          padding-right: 6px;
          color: #337dff;
          font-size: 14px;
        }
        .success-icon {
          padding-right: 6px;
          color: #10c038;
          font-size: 14px;
        }
        .operation-txt {
          cursor: pointer;
          color: #337dff;
          font-size: 12px;
        }
      }
      .percent-progress {
        height: 39px;
        background-color: #e6f4ff;
      }
    }
  }
  .content-hidden {
    height: 0px;
  }
}
</style>
