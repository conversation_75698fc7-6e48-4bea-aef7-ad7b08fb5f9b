<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>九天 · 毕昇</title>
    <style>
        * {
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol' !important;
            box-sizing: border-box;
        }

        html,
        body,
        #app {
            height: 100%;
        }

        body {
            font-size: 14px;
            font-weight: 400;
        }

        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background-color: rgba(0, 0, 0, 0.4);
        }

        ::-webkit-scrollbar-track {
            box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
            border-radius: 6px;
        }

        a {
            color: inherit;
            text-decoration: none;

        }

        a:hover {
            color: inherit;
        }

        a[disabled] {
            cursor: not-allowed;
            pointer-events: all;
        }

        ul,
        ol {
            list-style: none;
        }

        p,
        ul,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            margin-bottom: 0;
        }
    </style>

    <style>
        .content {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 211px;
            flex-wrap: wrap;
            position: relative;
        }

        img {
            width: 416px;
        }

        .img-text {
            position: absolute;
            top: 300px;
        }

        p {
            width: 100%;
            text-align: center;
        }

        p:nth-of-type(1) {
            font-size: 28px;
            line-height: 34px;
            font-weight: 600;
        }

        p:nth-of-type(2) {
            margin-top: 16px;
            font-size: 18px;
            color: #606972;
            line-height: 30px;
        }

        p:nth-of-type(2) span {
            color: #178cf9;
        }
    </style>
</head>

<body>
    <div class="content">
        <img src="./src/assets/image/系统维护@2x.png" alt="" />
        <div class="img-text">
            <p>平台维护升级中</p>
            <p>升级时间为： <span>2021年5月21日 0:00-4:00</span><br>
                等会再来看看吧</p>
        </div>
    </div>
</body>

</html>