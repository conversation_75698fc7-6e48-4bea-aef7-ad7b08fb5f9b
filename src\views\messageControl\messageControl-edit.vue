<template>
  <div class="user-edit">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '站内信发布', query: $route.query }"> 站内信发布 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item v-if="!isCreate">
        <router-link :to="{ name: '站内信详情', query: { ...$route.query, mailId } }">站内信详情</router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>{{ isCreate ? '新建' : '编辑' }}站内信</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="main">
      <a-spin :spinning="editLoading">
        <a-form :colon="false" ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
          <div class="form-title">
            <span>{{ isCreate ? '新建' : '编辑' }}站内信</span>
          </div>
          <div class="mail-info">
            <a-form-item class="sub-item" label="发送至" :wrapper-col="subWrapperCol">
              <a-select v-model:value="form.isGlobal" placeholder="请选择" style="width: 160px">
                <a-select-option v-for="v in mailSenderOptions" :value="v" :key="v">{{ v }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="sub-item" label="用户列表" v-if="form.isGlobal === SENDER_TYPE_MSG[SENDER_TYPE.ASSIGN]">
              <a-spin :spinning="uploadLoading" size="small">
                <a-upload name="file" :showUploadList="false" :headers="{ Authorization: 'Bearer ' + refreshToken }" :beforeUpload="beforeUpload" accept=".xls, .xlsx">
                  <a-button><jt-icon type="iconshangchuanwenjian" />上传文件</a-button>
                </a-upload>
              </a-spin>
              <a-button type="link" @click="download()">下载模板</a-button>
              <a-button type="link" danger style="padding-left: 0" @click="download('user')" v-if="(!isCreate && uploadRes.taskId) || uploadRes.invalidNum">查看导入结果</a-button>
              <div class="upload-num" v-if="uploadRes.taskId">已导入用户({{ uploadRes.successNum }})<jt-icon type="iconshanchu1" class="icon-delete" @click="deleteFile" /></div>
            </a-form-item>
            <a-form-item class="sub-item" label="标题" name="mailTitle" :wrapper-col="subWrapperCol">
              <a-input v-model:value="form.mailTitle" placeholder="请输入" style="width: 320px" />
            </a-form-item>
            <a-form-item class="sub-item" label="类型" name="mailType" required :wrapper-col="subWrapperCol">
              <a-select v-model:value="form.mailType" placeholder="请选择" style="width: 160px">
                <a-select-option v-for="v in mailTypeOptions" :value="v" :key="v">{{ v }}</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item class="sub-item" label="内容" name="mailsContent" required :wrapper-col="subWrapperCol">
              <div class="mail-content">
                <text-editor
                  :initParams="{
                    uploadImgShowBase64: false,
                    uploadFileName: 'file',
                    uploadImgServer: './messagecenter/web/image/add',
                    uploadImgMaxLength: 1,
                    uploadImgTimeout: 20 * 1000,
                    uploadImgHeaders: {
                      Authorization: 'Bearer ' + refreshToken,
                    },
                  }"
                  centerType="MESSAGE"
                  :editorContent="form.mailContent"
                  @change="handleEditorChange"
                />
                <div class="content-num">
                  <span :class="{ exceed: form.mailsContent?.length > 10000 }">{{ form.mailsContent?.length || 0 }}</span
                  >/10000
                </div>
              </div>
            </a-form-item>
            <a-form-item class="sub-item" label=" " :wrapper-col="subWrapperCol">
              <a-space>
                <a-button @click="preHandleSaveAndSend(false)" type="primary" class="send-btn">保存并发送</a-button>
                <a-button @click="preHandleSaveAndSend(true)" type="primary" ghost class="confirm-btn">保存</a-button>
                <a-button @click="goBack" class="cancel-btn">取消</a-button>
              </a-space>
            </a-form-item>
          </div>
        </a-form>
        <confirm-dlg :visible="sendModalVisible" title="确定发送站内信吗？" content="发送后将无法撤回，请谨慎操作" confirm-btn="立即发送" @cancel="sendModalVisible = false" @confirm="saveAndSend(false)"></confirm-dlg>
        <confirm-dlg type="danger" :visible="uploadModalVisible" title="确定再次上传用户列表吗？" content="再次上传文件，已导入用户将被覆盖，请谨慎操作" @cancel="uploadModalVisible = false" @confirm="uploadFile()"></confirm-dlg>
      </a-spin>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { mapState } from 'vuex';
import { message } from 'ant-design-vue';
import request from '@/request';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import TextEditor from '@/components/editor/index.vue';
import ConfirmDlg from '@/components/confirmDlg.vue';
import { MAIL_TYPE_MSG, SENDER_TYPE_MSG, SENDER_TYPE, USER_LIST_FILE, USER_LIST_FILE_TEXT } from '@/constants';
import { MessageCenter } from '@/apis';
import { downloadFile } from '@/utils/file';
const { uploadSenderUser, download, exportUser, getDetail, createMail, updateMail, sendMail } = MessageCenter;

let mailTypeOptions = Object.values(MAIL_TYPE_MSG)
  .map((item) => item)
  .reverse();
let editor = null as any;
const mailSenderOptions = Object.values(SENDER_TYPE_MSG)
  .map((item) => item)
  .reverse();

export default defineComponent({
  components: { TextEditor, ConfirmDlg },
  data() {
    return {
      isCreate: this.$route.name === '站内信创建',
      editLoading: false,
      uploadLoading: false,
      mailId: this.$route.query.mailId,
      mailInfo: {} as any,
      disabled: true,
      infoTitle: ['基本信息'],
      labelCol: { span: 4 },
      wrapperCol: { span: 10 },
      subWrapperCol: { span: 14 },
      mailTypeOptions,
      mailSenderOptions,
      SENDER_TYPE,
      SENDER_TYPE_MSG,
      form: {
        mailTitle: '',
        isGlobal: SENDER_TYPE_MSG[SENDER_TYPE.GLOBAL], //发送对象
        mailType: this.$route.query.mailType,
        mailContent: '',
        mailsContent: '',
      },
      sendModalVisible: false,
      uploadRes: {
        invalidNum: 0,
        repeatNum: 0,
        successNum: 0,
        taskId: '',
      },
      uploadModalVisible: false,
      fileList: [], //用户列表文件
    };
  },
  computed: {
    ...mapState(['refreshToken']),
    rules(this: any) {
      return {
        mailTitle: [
          { required: true, message: '请输入标题', trigger: ['blur', 'change'] },
          { min: 0, max: 20, message: '20个字符以内', trigger: ['blur', 'change'] },
        ],
        mailType: [{ required: true, message: '请选择类型', trigger: ['blur', 'change'] }],
        mailsContent: [
          { required: true, validator: this.validateMailsContent, message: '请输入内容', trigger: ['blur', 'change'] },
          { max: 10000, message: '字数超过限制', trigger: ['blur', 'change'] },
        ],
      };
    },
  },
  created() {
    if (editor) {
      editor.txt.html('');
      editor.destroy();
      editor = null as any;
    }
    this.getMailInfo();
  },
  methods: {
    beforeUpload(file, fileList) {
      const { name, size } = file || {};
      const REG = /^(xls|xlsx)$/;
      let fileNameArr = name.split('.');
      const fileNameSuffix = fileNameArr[fileNameArr.length - 1];
      if (!REG.test(fileNameSuffix)) {
        message.error('仅支持上传xlsx格式文件');
        return false;
      }
      this.fileList = fileList;
      if (size > USER_LIST_FILE) {
        message.error(`文件大小限制为${USER_LIST_FILE_TEXT}`);
        return false;
      }
      // 二次上传确认
      if (this.uploadRes.taskId) {
        this.uploadModalVisible = true;
        return false;
      }
      this.uploadFile(fileList);
      return false;
    },
    async uploadFile(fileList = []) {
      fileList = fileList.length ? fileList : this.fileList;
      if (!fileList.length) return;
      const params: any = new FormData();
      params.append('file', fileList[0]);
      this.uploadLoading = true;
      try {
        const res = await uploadSenderUser({ type: 'POST', data: params });
        const { state, body, errorMessage } = res || {};
        this.uploadModalVisible = false;
        this.uploadLoading = false;
        if (state === 'OK') {
          this.uploadRes = body || {};
          const { invalidNum, successNum, repeatNum } = body || {};
          message.success(`用户列表导入成功，成功导入${successNum}人，重复${repeatNum}人，失败${invalidNum}人`);
        } else {
          message.error(errorMessage);
        }
      } catch (error) {
        this.uploadLoading = false;
        console.log(error, 'error');
      }
    },
    download(type = 'template') {
      const url = type === 'template' ? download : `${exportUser}?taskId=${this.uploadRes.taskId}`;
      downloadFile({ url });
    },
    deleteFile() {
      this.uploadRes = { invalidNum: 0, successNum: 0, repeatNum: 0, taskId: '' };
    },
    validateMailsContent(rule, value, callback) {
      if (value || (!value && this.form.mailContent)) {
        return Promise.resolve();
      } else {
        return Promise.reject('请输入内容');
      }
    },
    // 富文本编辑器变更回调
    handleEditorChange(v) {
      if (v) {
        this.form.mailContent = v?.content;
        this.form.mailsContent = v?.pageContentIndex;
        (this as any).$refs.ruleForm.validateFields('mailsContent'); // 需要手动触发一次校验
      }
    },
    handleMenuClick(e) {},
    async getMailInfo(this: any) {
      if (this.isCreate) return;
      const res: any = await getDetail({ data: { mailId: this.mailId } });
      if (res.state == 'OK') {
        this.mailInfo = res.body;
        const { mailTitle, mailType, mailContent, isGlobal, taskId, userTotal } = res.body;
        this.form = {
          ...this.form,
          ...{
            mailTitle,
            mailType: MAIL_TYPE_MSG[mailType],
            isGlobal: SENDER_TYPE_MSG[isGlobal],
            mailContent,
            mailsContent: mailContent,
          },
        };
        this.uploadRes = { ...this.uploadRes, ...{ taskId, successNum: isGlobal === SENDER_TYPE.ASSIGN ? userTotal : 0 } };
      }
    },
    preHandleSaveAndSend(this: any, save: boolean) {
      if (!this.uploadSaveValidate()) {
        this.editLoading = false;
        return;
      }
      this.$refs.ruleForm
        .validate()
        .then(() => {
          save ? this.saveAndSend(save) : (this.sendModalVisible = true);
        })
        .catch((err) => {
          this.editLoading = false;
          throw err;
        });
    },
    uploadSaveValidate() {
      if (this.form.isGlobal === SENDER_TYPE_MSG[SENDER_TYPE.GLOBAL]) return true;
      const { taskId, successNum } = this.uploadRes;
      if (!taskId || !successNum) {
        message.error(`请上传用户列表`);
        return false;
      }
      return true;
    },
    async saveAndSend(this: any, save: boolean) {
      this.sendModalVisible = false;
      this.editLoading = true;
      let { mailTitle, mailContent, mailsContent, mailType, isGlobal } = this.form;
      // if (mailTitle.length > 8) mailTitle = mailTitle.substring(0, 4) + '...' + mailTitle.slice(-4);
      // 保存新建站内信
      let data = {
        mailTitle,
        content: mailsContent,
        mailContent,
        mailType,
        taskId: isGlobal === SENDER_TYPE_MSG[SENDER_TYPE.GLOBAL] ? '' : this.uploadRes.taskId,
        isGlobal: isGlobal === SENDER_TYPE_MSG[SENDER_TYPE.GLOBAL] ? SENDER_TYPE.GLOBAL : SENDER_TYPE.ASSIGN,
      } as any;
      if (!this.isCreate) data.mailId = this.mailId;
      for (const key in MAIL_TYPE_MSG) {
        if (mailType === MAIL_TYPE_MSG[key]) data.mailType = key;
      }
      let res: any;
      if (!this.isCreate) {
        res = await updateMail({ type: 'POST', data });
      } else {
        res = await createMail({ type: 'POST', data });
      }
      try {
        if (res.state == 'OK') {
          if (this.isCreate) this.mailId = res.body;
          if (save) {
            message.success(`站内信【${mailTitle}】保存成功！`);
            this.goDetail();
          } else {
            // 发送站内信
            const data: any = await sendMail({ type: 'POST', data: this.mailId });
            if (data.state == 'OK') {
              this.goDetail();
              message.success(`站内信【${mailTitle}】提交审核成功！`);
            }
          }
        } else {
          this.editLoading = false;
          save ? message.error(`站内信【${mailTitle}】保存失败，请稍后再试！`) : message.error(`站内信【${mailTitle}】提交审核失败，请稍后再试！`);
        }
      } catch (error) {
        this.editLoading = false;
      }
    },
    goBack(this: any) {
      if (this.isCreate) {
        this.$router.push({ path: '/message-management', query: { ...this.$route.query, confirmLeave: undefined } });
      } else {
        this.goDetail(true);
      }
    },
    goDetail(this: any, isGoBack) {
      if (!this.mailId) return;
      let path = '/message-management/messageControl-detail' as any;
      this.$router.push({ path, query: { ...this.$route.query, mailId: this.mailId, confirmLeave: isGoBack ? undefined : 1 } });
    },
  },
});
</script>
<style lang="less" scoped>
.user-edit {
  .nav {
    height: 58px;
    line-height: 58px;
    padding-left: 20px;
    background: #ffffff;
    font-weight: 500;
    color: #121f2c;
  }
  .main {
    min-width: 1254px;
    padding: 20px 20px 80px;
    margin: 20px;
    background: #ffffff;
    position: relative;
    .form-title {
      display: flex;
      justify-content: space-between;
      span {
        font-size: 16px;
        font-weight: 500;
        color: #121f2c;
        margin-bottom: 24px;
      }
    }
    .sub-item {
      position: relative;
      width: 800px;
    }
    .mail-content {
      width: 720px;
      position: relative;
      .content-num {
        font-size: 12px;
        color: #cbcfd2;
        position: absolute;
        right: 0;
        span {
          color: #0082ff;
        }
        .exceed {
          color: #ff454d;
        }
      }
    }
    .send-btn {
      width: 120px;
      margin-top: 24px;
    }
    .confirm-btn,
    .cancel-btn {
      width: 88px;
      margin-top: 24px;
    }
  }
}
/deep/ .ant-spin-nested-loading {
  display: inline-block;
}
.upload-num {
  margin-top: 10px;
  .icon-delete {
    margin-left: 10px;
  }
}
</style>
