<template>
  <micro-components :appName="appName" moduleName="unread-message" :data="{ requester, config, titleStyle }" />
</template>

<script>
import { GET, POST } from '@/request/index';
import { getEnvConfig } from '@/config';

export default {
  name: 'unread-message-remote',
  props: {
    showIcon: {
      type: Boolean,
      default: false,
    },
    text: {
      type: String,
    },
    titleStyle: {
      type: Object,
      default: () => {},
    },
    appName: {
      type: String,
    },
    reload: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      requester: { GET, POST },
      config: {
        messageUrl: `/${getEnvConfig('MESSAGE_URL_PATH')}`,
        showIcon: this.showIcon,
        text: this.text,
        reload: this.reload,
      },
    };
  },
};
</script>
