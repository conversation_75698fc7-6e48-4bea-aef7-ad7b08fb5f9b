<template>
  <a-table :loading="tableAttr(loading).loading" :locale="tableAttr(loading).locale" :columns="columns" :rowClassName="() => 'cus-row'" :data-source="dataSource" :pagination="false" @change="handleChange">
    <template #emptyText v-if="!loading">
      <empty
        title="能力"
        :showNoDataText="
          showNoDataText({
            searchText: data.searchText,
          })
        "
      >
        <template #description>
          请立即
          <a href="javascript:;" @click="handleCreate">新建能力</a>
        </template>
      </empty>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'name'">
        <a-tooltip :title="record.name" placement="topLeft">
          <div class="overflow-ellipsis">
            <span class="user-name-text">
              {{ record.name || '-' }}
            </span>
          </div>
        </a-tooltip>
      </template>
      <template v-else-if="column.key === 'status'">
        <div class="type">
          <span class="circle2"> </span>
          <span class="user-name-text">
            {{ statusTransfer(record.status) || '-' }}
          </span>
        </div>
      </template>
      <template v-else-if="column.key === 'description'">
        <a-tooltip :title="record.description" placement="topLeft">
          <div class="overflow-ellipsis">
            <span class="user-name-text">
              {{ record.description || '-' }}
            </span>
          </div>
        </a-tooltip>
      </template>
      <template v-else-if="column.key === 'callAddress'">
        <div class="info">
          <a-button type="text" class="button-style" @click="copyContentB(record.providerInfo.providerAccount, record.providerInfo.providerPhone, record.providerInfo.providerEmail)" :disabled="record.providerInfo.providerAccount ? false : true && record.providerInfo.providerPhone ? false : true && record.providerInfo.providerEmail ? false : true">
            <jt-icon type="iconfile-copy" :class="record.providerInfo.providerAccount + record.providerInfo.providerPhone + record.providerInfo.providerEmail ? 'copy-hover' : 'copy'"></jt-icon>
          </a-button>

          <a-tooltip placement="topLeft">
            <template #title> {{ record.providerInfo.providerAccount || '-' }}/{{ record.providerInfo.providerPhone || '-' }}/{{ record.providerInfo.providerEmail || '-' }} </template>
            <div style="display: flex" class="overflow-ellipsis">
              <span class="overflow-ellipsis-pro">{{ record.providerInfo.providerAccount || '-' }}</span
              >/ <span class="overflow-ellipsis-pro">{{ record.providerInfo.providerPhone || '-' }}</span
              >/
              <span class="overflow-ellipsis-pro">{{ record.providerInfo.providerEmail || '-' }}</span>
            </div>
          </a-tooltip>
        </div>
        <div class="info">
          <a-button type="text" class="button-style" @click="copyContent(record.callAddress.priNetCallAddr, record.callAddress.pubNetCallAddr)" :disabled="record.callAddress.priNetCallAddr ? false : true && record.callAddress.pubNetCallAddr ? false : true">
            <jt-icon type="iconfile-copy" :class="record.callAddress.priNetCallAddr + record.callAddress.pubNetCallAddr ? 'copy-hover' : 'copy'"></jt-icon>
          </a-button>
          <a-tooltip placement="topLeft">
            <template #title v-if="record.callAddress.priNetCallAddr || record.callAddress.pubNetCallAddr">
              <ul v-if="record.callAddress.priNetCallAddr" style="list-style: none; padding-left: 0">
                <li v-for="(i, index) in record.callAddress.priNetCallAddr.split(',')" :key="index">{{ i }}</li>
              </ul>
              <ul v-else style="list-style: none; padding-left: 0">
                <li v-for="(i, index) in record.callAddress.pubNetCallAddr.split(',')" :key="index">{{ i }}</li>
              </ul>
            </template>
            <template #title v-else> - </template>
            <span class="overflow-ellipsis"> {{ record.callAddress.priNetCallAddr ? record.callAddress.priNetCallAddr : record.callAddress.pubNetCallAddr || '-' }} </span>
          </a-tooltip>
        </div>
      </template>
      <template v-else-if="column.key === 'button'">
        <div class="active">
          <a-button type="link" @click="view(record)"
            ><span><jt-icon type="iconbofangliang" class="icon"></jt-icon>预览</span></a-button
          >
          <a-button type="link" @click="edit(record)"
            ><span><jt-icon type="iconbianji" class="icon"></jt-icon>编辑</span></a-button
          >
          <a-button type="link" @click="shelve(record)"
            ><span><jt-icon :type="buttonTransfer(record.button) == '同步' ? 'icontongbu' : 'iconshangjia'" class="icon"></jt-icon>{{ buttonTransfer(record.button) }}</span></a-button
          >
          <a-button type="link" @click="delEdge(record)">
            <span><jt-icon type="iconshanchu1" class="icon"></jt-icon>删除草稿</span>
          </a-button>
        </div>
      </template>
    </template>
  </a-table>
  <Pagination :total="pagination.total" :pageNum="pagination.pageNum" :pageSize="pagination.pageSize" :pageSizeOptions="[10, 20, 50, 100]" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
  <shelf v-if="data.shelfVal" :shelfVal="data.shelfVal" @cancelModal="cancelModal" :shelfOrDraft="data.shelfOrDraft" :keyDraft="data.keyDraft" :draftObj="data.draftObj"></shelf>
</template>
<script setup lang="ts">
import { reactive, ref, computed, createVNode, defineProps, defineEmits, watch } from 'vue';
import { useRouter } from 'vue-router';
import Pagination from '@/components/pagination.vue';
import request from '@/request';
import { copyContent, copyContentB, showNoDataText, tableAttr } from '@/utils/index';
import shelf from './shelf.vue';
import { Modal, message } from 'ant-design-vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import empty from '@/components/empty.vue';

const props = defineProps({
  keyValue: {
    type: String,
    default: '',
  },
  keyNum: {
    type: String,
    default: '',
  },
  searchText: {
    type: String,
    default: '',
  },
});
//监听搜索请求的列表数据
watch(
  () => props.keyValue,
  (newValue, oldValue) => {
    data.searchText = props.keyValue;
    pagination.pageNum = 1;
    getDraftList();
  }
);
watch(
  () => props.keyNum,
  () => {
    if (props.keyNum == '1') {
      data.searchText = '';
      getDraftList();
    }
    if (props.keyNum == '2') {
      getDraftList();
    }
  }
);
const loading = ref(false);
const emits = defineEmits(['totalNum']);
const data = reactive({
  searchText: '',
  shelfVal: false,
  draftObj: {},
  desc: true,
  type: '', //类型
  statusList: [], //状态
  searchTextNum: true,
  keyDraft: true,
  enumerationList: {}, //枚举值
  buttonEnumerationList: {}, //button 枚举值
  shelfOrDraft: '',
});
const dataSource = ref<any[]>([]);
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0,
});
//请求列表数据
const getDraftList = () => {
  data.searchTextNum = true;
  loading.value = true;
  const obj = {
    name: data.searchText,
    orderByModifyTimeDesc: data.desc,
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize,
    statusList: data.statusList,
    type: data.type,
  };
  request('/aiipweb/om-service/draft/queryList', {
    method: 'POST',
    data: obj,
  }).then((res: any) => {
    setTimeout(() => {
      loading.value = false;
    }, 500);
    if (res.state === 'OK') {
      dataSource.value = res.body.data;
      pagination.total = res.body.total;
      if (data.searchText != '') {
        if (pagination.total == 0) {
          data.searchTextNum = false;
        }
      }
      emits('totalNum', pagination.total);
    }
  });
};
getDraftList();
//枚举值
const enumeration = () => {
  request('/aiipweb/om-service/dict/getDict', {
    method: 'GET',
    data: {
      dictName: 'capabilityStatus',
    },
  }).then((res: any) => {
    res.body.forEach((item) => {
      data.enumerationList[item.value] = item.label;
    });
  });
};
enumeration();
//优化枚举显示
const statusTransfer = (value) => {
  return data.enumerationList[value];
};
//button枚举
const buttonList = () => {
  request('/aiipweb/om-service/dict/getDict', {
    method: 'GET',
    data: {
      dictName: 'button',
    },
  }).then((res: any) => {
    res.body.forEach((item) => {
      data.buttonEnumerationList[item.value] = item.label;
    });
  });
};
buttonList();
const buttonTransfer = (value) => {
  return data.buttonEnumerationList[value];
};
//预览查看
const view = (val) => {
  console.log(val.name, '----------------');
  // route(path) {
  // if (path) {
  let routeUrl = router.resolve({ path: '/capacity-management/preview', query: { listName: val.name } });
  const a = document.createElement('a');
  const event = new MouseEvent('click');
  console.log(routeUrl.href);

  a.href = routeUrl.href;
  a.target = '_blank';
  a.rel = 'noopener noreferrer';
  a.dispatchEvent(event);
  // }
  // },

  // router.push({ path: '/preview' });
};
//编辑
const edit = (val) => {
  router.push({
    path: `/capacity-management/capacity-edit/${val.name}`,
    query: { key: 'draft', button: val.button },
  });
};
//表头
const columns = computed(() => {
  const columnsData = [
    {
      title: '能力名称',
      dataIndex: 'name',
      key: 'name',
      width: '13%',
    },
    {
      title: '能力状态',
      dataIndex: 'status',
      key: 'status',
      width: '8%',
    },
    {
      title: '更新时间',
      dataIndex: 'modifiedTime',
      key: 'modifiedTime',
      sorter: {
        compare: (a, b) => a.createTime - b.createTime,
        multiple: 1,
      },
      width: '14%',
    },
    {
      title: '能力简介',
      dataIndex: 'description',
      key: 'description',
      width: '17%',
    },
    {
      title: '能力提供方信息 / 调用地址',
      dataIndex: 'callAddress',
      key: 'callAddress',
      width: '17%',
    },
    {
      title: '操作',
      dataIndex: 'button',
      key: 'button',
      width: '31%',
    },
  ];

  return columnsData;
});
//表格的过滤筛选
const handleChange = (paginationc, filters, sorter) => {
  data.desc = sorter.order == 'ascend' ? false : true;
  getDraftList();
};
//跳转新建
const router = useRouter();
const handleCreate = () => {
  Modal.confirm({
    title: createVNode('span', { style: 'fontWeight: 500;color: #121F2C;' }, '欢迎申请入驻开放能力板块！'),
    okText: '准备好了',
    content: createVNode('span', { style: 'fontWeight: 400;color:  #606972;' }, '请确保已准备好能力介绍和技术文档材料，如需模板请点击相应蓝色文字进行下载'),
    cancelText: '取消',
    icon: createVNode(ExclamationCircleFilled, { style: 'color:#0082FF;' }),
    onOk: () => {
      router.push({ path: '/capacity-management/capacity-create' });
    },
  });
};
//翻页
const changePageNum = (pageNum) => {
  pagination.pageNum = pageNum;
  getDraftList();
};
const changePageSize = (pageSize) => {
  pagination.pageSize = pageSize;
  pagination.pageNum = 1;
  getDraftList();
};
//删除
const delEdge = (e) => {
  const draftId = e.id;
  Modal.confirm({
    title: createVNode('span', { style: 'fontWeight: 500;color: #121F2C;' }, '确定删除草稿吗？'),
    okText: '取消',
    okButtonProps: { type: 'ghost' },
    content: createVNode('span', { style: 'fontWeight: 400;color:  #606972;' }, '删除草稿后将不可找回，请谨慎操作'),
    cancelText: '删除',
    icon: createVNode(ExclamationCircleFilled, { style: 'color:#FF454D;' }),
    cancelButtonProps: { type: 'primary', danger: true },
    autoFocusButton: null,
    onCancel: () => {
      request('/aiipweb/om-service/draft/delete', {
        method: 'GET',
        data: {
          draftId: draftId,
        },
      }).then((res: any) => {
        if (res.state) {
          if (res.body == 'success') {
            message.success(`【${e.name}】草稿已删除`);
            getDraftList();
          } else {
            message.error(`【${e.name}】草稿未能删除，请稍后再试`);
          }
        }
      });
    },
  });
};
//打开模态
const shelve = (value) => {
  data.shelfOrDraft = buttonTransfer(value.button) == '同步' ? '同步' : '上架';
  data.shelfVal = true;
  data.draftObj = value;
};
//关闭模态
const cancelModal = (value) => {
  if (value) {
    getDraftList();
  }
  data.shelfVal = false;
};
</script>

<style lang="less" scoped>
.table-empty {
  position: relative;
  top: -133px;
  .table-empty-text {
    line-height: 22px;
    color: #121f2c;
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 16px;
  }
  .table-empty-but {
    color: #555555;
    font-size: 14px;
    font-weight: 400;
    span {
      margin-left: 5px;
      color: #337dff;
    }
  }
}
/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}
/deep/.ant-table-filter-column {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    width: 48px;
    flex: 0;
  }
  .ant-table-filter-trigger {
    margin: 0;
  }
}
/deep/.ant-table-column-sorters {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    width: 48px;
    flex: 0;
  }
}
/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}
:deep(.cus-row) {
  cursor: pointer;
  font-size: 12px;
  .user-name-text {
    &:hover {
      color: @primary-color;
    }
  }
}

:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;
  th {
    background-color: #f6f9fc;
  }
}
.type {
  display: flex;
  align-items: center;
  .circle2 {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    box-shadow: 0px 2px 10px 0px #d7dddd;
    display: flex;
    margin-right: 8px;
    background: #68dcff;
  }
}
.overflow-ellipsis {
  width: 150px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.overflow-ellipsis-pro {
  max-width: 50px;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.info {
  display: flex;
  align-items: center;
  .button-style {
    font-size: 16px;
    padding: 0;
    height: 16px;
    display: flex;
    align-items: center;
    .copy {
      color: #7f828f;
      margin-right: 6px;
    }
    .copy-hover {
      color: #7f828f;
      margin-right: 6px;
      &:hover {
        color: #0082ff;
      }
    }
  }
}
.active {
  display: flex;
  justify-content: space-between;
  /deep/.ant-btn {
    font-size: 12px;
    padding-left: 0;
    width: 25%;
  }
  span {
    display: flex;
    align-items: center;
    color: #606972;
    .icon {
      font-size: 18px;
      margin-right: 4px;
      color: #0082ff;
    }
  }
  .disabled-span {
    color: #a0a6ab;
    .icon {
      margin-right: 4px;

      color: #a0a6ab;
    }
  }
}
/deep/.ant-empty-description {
  margin-bottom: 0;
  div {
    height: 0;
  }
}
</style>
