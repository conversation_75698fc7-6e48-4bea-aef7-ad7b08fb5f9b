function getSaveOPtions(store, optionsUse) {
  return {
    abilityId: optionsUse.value === true ? store.state.appInfo.abilityId : null,
    id: optionsUse.value === true ? store.state.appInfo.id : store.state.appInfo.abilityId,
    //setp 1 能力基本信息
    baseInfo: store.state.appInfo.baseInfo,
    //step 2 功能演示
    material: store.state.appInfo.material,
    //step 3 功能介绍
    intro: {
      //功能介绍
      funcIntros: JSON.parse(JSON.stringify(store.state.appInfo.intro.funcIntros || [])),
      //使用场景
      useCases: JSON.parse(JSON.stringify(store.state.appInfo.intro.useCases || [])),
    },
    //step 4 技术特色
    techFeatures: {
      //技术特色
      features: JSON.parse(JSON.stringify(store.state.appInfo.techFeatures.features || [])),
      questions: JSON.parse(JSON.stringify(store.state.appInfo.techFeatures.questions || [])),
      recommends: store.state.appInfo.techFeatures.recommends, //相关推荐
    },
    //step 5 能力推荐
    recCard: {
      capabilityName: '', //本期不用
      capabilityIntro: store.state.appInfo.recCard.capabilityIntro, //能力简介
      theFirstFuncIntro: store.state.appInfo.recCard.theFirstFuncIntro, //功能标签一
      theSecondFuncIntro: store.state.appInfo.recCard.theSecondFuncIntro, //功能标签二
    },
    //step 6 技术文档
    techDoc: {
      content: store.state.appInfo.techDoc.content, //本期不用
      fileUrl: store.state.appInfo.techDoc.fileUrl, //本期不用
      needTechDoc: store.state.appInfo.techDoc.needTechDoc, //true false
    },
  };
}
export default getSaveOPtions;
