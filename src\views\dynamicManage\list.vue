<template>
  <div class="user-control-container">
    <sub-header title="动态管理"></sub-header>
    <Container>
      <container-item>
        <div class="top-bar">
          <div>
            <h1>动态列表</h1>
          </div>
          <div style="display: flex">
            <a-input v-model:value="postData.keyword" @change="handlerSearch" allow-clear placeholder="动态标题" style="margin-right: 12px">
              <template #prefix>
                <jt-icon type="iconsousuo" style="font-size: 18px" />
              </template>
            </a-input>
            <a-button @click="goCreate" type="primary">
              <template #icon><PlusOutlined /></template>
              新建动态
            </a-button>
          </div>
        </div>
        <a-table :loading="tableAttr(isLoading).loading" :getPopupContainer="getPopupContainer" :columns="columns" :data-source="dynamicList" @change="tableChange" :rowClassName="() => 'cus-row'" :pagination="false" rowKey="rowKey">
          <template #emptyText>
            <empty
              v-if="!isLoading"
              title="动态"
              :showNoDataText="
                showNoDataText({
                  mailType: postData.mailType,
                  mailStatus: postData.mailStatus,
                  keyword: postData.keyword,
                })
              "
            >
              <template #description>
                请立即
                <a href="javascript:;" @click="goCreate">新建动态</a>
              </template>
            </empty>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'dynamicTitle'">
              <span class="user-name-text" :title="record.dynamicTitle">{{ record.dynamicTitle }}</span>
            </template>
            <template v-if="column.key === 'pubState'">
              <dynamic-status-tag :dynamicStatus="record.pubState" />
            </template>
            <template v-if="column.key === 'firstPubTime'">
              <div class="creat-time">{{ record.firstPubTime || '-' }}</div>
            </template>
            <template v-if="column.key === 'updateTime'">
              <div class="creat-time">{{ record.updateTime || '-' }}</div>
            </template>
            <template v-if="column.key === 'updatePubTime'">
              <div class="creat-time">{{ record.updatePubTime || '-' }}</div>
            </template>
            <template v-if="column.key === 'operation'">
              <a-space size="middle">
                <a-button type="link" @click="preview(record)" :disabled="record.pubState == 1"> <jt-icon type="iconbofangliang" />预览 </a-button>
                <a-button type="link" @click="goCreate('edit', record)"> <jt-icon type="iconbianji" />编辑</a-button>
                <a-button type="link" @click="showPublicModal(record)" :disabled="record.pubState == 1">
                  <icons iconType="publish" />
                  发布
                </a-button>
                <a-button type="link" @click="showDeleteModal(record)"> <jt-icon type="iconshanchu1" />删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
        <confirm-modal :visible="publicModalVisible" title="确定发布动态吗？" :content-title="currentData.dynamicTitle" content="发送后可进行编辑并再次发布" confirm-btn="立即发布" @cancel="publicModalVisible = false" @confirm="handlePublish(currentData)"></confirm-modal>
        <confirm-modal type="danger" :visible="deleteModalVisible" title="确定删除动态吗？" :content-title="currentData.dynamicTitle" content="删除后将不可恢复，请谨慎操作。" confirm-btn="删除" @cancel="deleteModalVisible = false" @confirm="handleDynamicDelete(currentData)"></confirm-modal>
        <Pagination :total="total" :pageNum="postData.pageNum" :pageSize="postData.pageSize" :pageSizeOptions="[10, 20, 50, 100]" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
      </container-item>
    </Container>
  </div>
</template>

<script>
import { defineComponent, h } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import ConfirmModal from './components/confirmModal.vue';

import dynamicStatusTag from './components/dynamicStatusTag.vue';
import Icons from './components/icons.vue';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import { DYNAMIC_STATUS_MSG } from './constants';

import _ from 'lodash';
import { tableAttr, showNoDataText } from '@/utils';
import empty from '@/components/empty.vue';
import { DynamicManagement } from '@/apis';
import { useRouter } from 'vue-router';
const { getList, deleteDynamic, publicDynamic, getDetail } = DynamicManagement;

let dynamicStatusFilters = [];
for (const key in DYNAMIC_STATUS_MSG) {
  dynamicStatusFilters.push({
    text: DYNAMIC_STATUS_MSG[key],
    value: key,
  });
}
export default defineComponent({
  components: {
    ConfirmModal,
    PlusOutlined,
    subHeader,
    Container,
    ContainerItem,
    Pagination,
    dynamicStatusTag,
    Icons,
    empty,
  },
  data() {
    const query = this.$route.query;
    const defaultDynamicStatus = query.pubStateList ? query.pubStateList.toString().split(',') : [];
    return {
      isLoading: true,
      publicModalVisible: false,
      deleteModalVisible: false,
      dynamicTitle: '',
      currentData: {},
      dynamicList: [],
      total: 0,
      postData: {
        pubStateList: defaultDynamicStatus,
        orderColumn: query.orderColumn || null,
        isAsc: query.isAsc || false, //默认传false，降序
        keyword: query.keyword || undefined,
        pageNum: Number(query.pageNum) || 1,
        pageSize: Number(query.pageSize) || 10,
      },
    };
  },
  computed: {
    columns() {
      const query = this.$route.query;
      const defaultDynamicStatus = query.pubStateList ? query.pubStateList.toString().split(',') : [];
      return [
        {
          title: '动态标题',
          key: 'dynamicTitle',
          width: 200,
          ellipsis: true,
          scopedSlots: { customRender: 'dynamicTitle' },
        },
        {
          title: '状态',
          dataIndex: 'pubState',
          key: 'pubState',
          width: 110,
          scopedSlots: { customRender: 'pubState' },
          filters: dynamicStatusFilters,
          defaultFilteredValue: defaultDynamicStatus,
        },
        {
          title: '首次发布时间',
          key: 'firstPubTime',
          sorter: true,
          sortOrder: this.postData.orderColumn === 'firstPubTime' && this.postData.isAsc,
          width: 180,
          scopedSlots: { customRender: 'firstPubTime' },
        },
        {
          title: '最新发布时间',
          key: 'updatePubTime',
          width: 180,
          sorter: true,
          sortOrder: this.postData.orderColumn === 'updatePubTime' && this.postData.isAsc,
          scopedSlots: { customRender: 'updatePubTime' },
        },
        {
          title: '最后编辑时间',
          key: 'updateTime',
          sorter: true,
          width: 180,
          sortOrder: this.postData.orderColumn === 'updateTime' && this.postData.isAsc,
          scopedSlots: { customRender: 'updateTime' },
        },
        {
          title: '操作',
          key: 'operation',
          width: 316,
          dataIndex: 'operation',
        },
      ];
    },
  },
  created() {
    this.getDynamicList();
  },
  methods: {
    showNoDataText,
    tableAttr,
    async getDynamicList(needRewriteUrl = true) {
      this.isLoading = true;
      needRewriteUrl && this.rewriteUrlByParams();
      const data = {
        ...this.postData,
        isAsc: this.postData.isAsc === 'ascend',
      };
      const res = await getList({ type: 'POST', data });
      if (res.state === 'OK') {
        this.dynamicList = res.body.list || [];
        this.isLoading = false;
        this.total = res.body.total;
      } else {
        this.isLoading = false;
      }
    },
    getPopupContainer(node) {
      if (node && this.dynamicList.length > 1) {
        return node.parentNode;
      }
      return document.body;
    },
    rewriteUrlByParams() {
      const { pubStateList, keyword, pageSize, pageNum, orderColumn, isAsc } = this.postData;
      const rewriteQuery = {
        pageNum,
        pageSize,
        orderColumn,
        isAsc: isAsc,
        pubStateList: pubStateList && pubStateList.length > 0 ? [pubStateList].join(',') : null,
        keyword: keyword || undefined,
      };
      this.$router.replace({ query: { ...this.$route.query, ...rewriteQuery } });
    },

    tableChange(pagination, filters, sorter, extra) {
      const sorterOrder = sorter.order;
      const sorterColumn = sorter.columnKey;

      if (sorterOrder && sorterColumn) {
        this.postData['orderColumn'] = sorterColumn;
        this.postData['isAsc'] = sorterOrder;
      } else {
        this.postData['orderColumn'] = null;
        this.postData['isAsc'] = null;
      }

      for (const key in filters) {
        this.postData['pubStateList'] = filters[key] || [];
      }
      this.postData.pageNum = 1;
      this.postData.pageSize = 10;
      this.getDynamicList();
    },
    changePageNum(pageNum) {
      this.postData.pageNum = pageNum;
      this.getDynamicList();
    },
    changePageSize(pageSize) {
      this.postData.pageSize = pageSize;
      this.postData.pageNum = 1;
      this.getDynamicList();
    },
    handlerSearch(e) {
      this.isLoading = true;
      this.searchInfo();
    },
    searchInfo: _.debounce(function () {
      this.postData.pageNum = 1;
      this.postData.pageSize = 10;
      this.getDynamicList();
    }, 500),

    // 新建页面、编辑页面
    goCreate(type = 'create', data) {
      if (type === 'edit') {
        this.$router.push({ path: '/dynamic-manage/dynamicManage-edit', query: { ...this.$route.query, id: data.id } });
      } else {
        this.$router.push({ path: '/dynamic-manage/dynamicManage-create', query: { ...this.$route.query } });
      }
    },
    //预览动态
    preview(info) {
      this.isLoading = true;
      getDetail({ data: { id: info.id } }).then((res) => {
        if (res.state === 'OK') {
          const previewData = res.body;
          const showType = previewData.type;
          const routeUrl = showType === 0 ? this.$router.resolve({ path: '/dynamic-manage/dynamicManage-preview', query: { id: info.id } }) : previewData.dynamicUrl;
          const a = document.createElement('a');
          const event = new MouseEvent('click');
          a.href = showType === 0 ? routeUrl.href : routeUrl;
          a.target = '_blank';
          a.rel = 'noopener noreferrer';
          a.dispatchEvent(event);
          this.isLoading = false;
        }
      });
    },

    // 发布动态
    showPublicModal(data) {
      this.currentData = data;
      this.publicModalVisible = true;
    },
    handlePublish(data) {
      const id = data.id;
      const dynamicTitle = data.dynamicTitle;
      publicDynamic({ data: { id } }).then((res) => {
        if (res.state === 'OK') {
          message.success(`动态【${dynamicTitle}】发布成功！`);
          this.publicModalVisible = false;
          this.getDynamicList(false);
        } else {
          message.error(`动态【${dynamicTitle}】发布失败，请稍后再试！`);
          this.publicModalVisible = false;
        }
      });
    },

    // 删除动态
    showDeleteModal(data) {
      this.currentData = data;
      this.deleteModalVisible = true;
    },
    handleDynamicDelete(record) {
      const id = record.id;
      const dynamicTitle = record.dynamicTitle;
      deleteDynamic({ data: { id } }).then((res) => {
        if (res.state === 'OK') {
          message.success(`动态【${dynamicTitle}】删除成功！`);
          this.postData.pageNum = 1; //规避一个删除完成后仅剩下pageSize条数据的bug
          this.getDynamicList();
          this.deleteModalVisible = false;
        } else {
          message.error(`动态【${dynamicTitle}】删除失败，请稍后再试！`);
          this.deleteModalVisible = false;
        }
      });
    },
  },
});
</script>
<style lang="less" scoped>
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
:deep(.cus-row) {
  cursor: default;
  font-size: 12px;
  color: #606972;
  .user-name-text {
    color: #121f2c;
    &:hover {
      color: @primary-color;
    }
  }
}
:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;
  th {
    background-color: #f6f9fc;
  }
}
/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}
/deep/.ant-table-filter-column {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    // width: 24px;
    flex: 0;
  }
}
/deep/ .ant-space-item > .ant-btn {
  padding: 0px 10px 0px 0px;
}

/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}

.creat-time {
  display: flex;
  align-items: center;
  img {
    margin-right: 4px;
  }
}
.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}
.table-empty {
  color: #606972;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  top: -120px;
}
</style>
