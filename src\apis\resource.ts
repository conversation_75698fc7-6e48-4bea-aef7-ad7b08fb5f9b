/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-20 14:45:58
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-08 11:07:39
 */
import request from '@/request';

const RESOURCE_API = {
  getImage: '/portal/manage/web/getImage',
  getPlatformAccess: '/portal/manage/web/getPlatformAccess',
  getResourceSummary: '/portal/manage/web/getResourceSummary',
};

const Resource = {
  // 首页-快捷入口：获取图片流
  getImage: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(RESOURCE_API.getImage, {
      method: type || 'GET',
      data,
    });
  },
  // 首页：获取子平台入口
  getPlatformAccess: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(RESOURCE_API.getPlatformAccess, {
      method: type || 'GET',
      data,
    });
  },
  // 首页：获取平台资源信息
  getResourceSummary: (params: any = {}) => {
    const { type, data = {} } = params;
    return request(RESOURCE_API.getResourceSummary, {
      method: type || 'GET',
      data,
    });
  },
};
export default Resource;
