@import '@/assets/styles/var.less';
#container-wrap {
  color: @jt-text-color-primary;
  .overview-content,.detail-overview-item,.links-content {
    display: flex;
  }
  #container {
    position: relative;
    padding: 20px 20px 32px 20px;
    .sub-title {
      margin-bottom: 20px;
      font-size: @jt-font-size-lg;
    }
    // .collapse-title {
    //   padding-bottom: 10px;
    //   border-bottom: 1px solid #ccc;
    // }
    .collapse-content {
      position: absolute;
      top: 20px;
      right: 20px;
      cursor: pointer;
      .icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-size: 24px 24px;
        &.icon_close {
          background-image: url('~@/assets/images/home/<USER>');
          &:hover {
            background-image: url('~@/assets/images/home/<USER>');
          }
        }
        &.icon_open {
          background-image: url('~@/assets/images/home/<USER>');
          &:hover {
            background-image: url('~@/assets/images/home/<USER>');
          }
        }
      }
    }
    .collapse-detail-info {
      display: none;
      &.show {
        display: block;
        transition: 1.5s;
      }
      .line {
        width: 100%;
        height: 1px;
        background: #EFEFEF;
      }
      .tb-title {
        margin: 20px 0 10px 0;
        padding-left: 12px;
        border-left: 4px solid #FFC53D;
        &.blue-border {
          border-left: 4px solid #00D2E5;
        }
      }
      /deep/ .ant-table-content {
        font-size: 12px;
        .ant-table-thead tr {
          th::before {
            width: 0;
          }
        }
      }
    }
    .sub-item {
      flex: 1;
      margin-right: 5px;
      &:last-child {
        margin-right: 0;
      }
      &.org-item {
        flex: 2;
      }
    }
  }
  .detail-overview-item {
    margin-bottom: 20px;
    .side-line {
      width: 4px;
      height: 64px;
      margin-right: 15px;
      background: #FFC53D;
      &.blue {
        background: #00D2E5;
      }
    }
  }
}
.flex {
  display: flex;
}
.jt-icon {
  font-size: @jt-font-size-lg;
  color: @jt-text-color-secondary;
  margin-left: 5px;
  vertical-align: bottom;
}
  