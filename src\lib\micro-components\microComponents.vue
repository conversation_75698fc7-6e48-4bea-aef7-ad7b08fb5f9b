<template>
  <micro-app :name="appName || moduleName" :url="url" :data="microAppData"></micro-app>
</template>

<script>
import microApp from '@micro-zoe/micro-app';
// 本地开发模式地址不固定
const path = location.pathname.split('/').slice(0, -1).join('/');
const url = process.env.NODE_ENV === 'development' ? 'http://localhost:8089/common-components' : `${location.origin}${path}/common-components`;
export default {
  name: 'micro-components',
  props: {
    appName: String,
    moduleName: String,
    data: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      url,
    };
  },
  mounted() {
    microApp.addDataListener(this.moduleName, (remoteData) => {
      if (remoteData.isEvent) {
        this.$emit(remoteData.detail.type, remoteData.detail.data);
      }
    });

    console.log(this.data, this.moduleName);
  },
  computed: {
    microAppData() {
      return {
        moduleName: this.moduleName,
        data: {
          ...this.data,
        },
      };
    },
  },
};
</script>
