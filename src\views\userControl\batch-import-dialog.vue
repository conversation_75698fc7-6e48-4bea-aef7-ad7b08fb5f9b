<template>
  <a-modal :visible="visible" title="批量导入用户" :maskClosable="false">
    <a-upload-dragger :file-list="fileList" :before-upload="beforeUpload" accept=".xls, .xlsx" :remove="handleRemove">
      <p class="ant-upload-drag-icon">
        <jt-icon type="iconshangchuanwenjian" class="upload-icon" />
      </p>
      <p class="ant-upload-text">
        请拖拽.xls或.xlsx文件到框内，或
        <a style="color: #0082ff">点击上传</a>
      </p>
    </a-upload-dragger>
    <div class="model-tips">
      <span>请您参考表格样例，填写并上传表格。文件大小不能超过10M。</span>
      <a @click="downloadTemplate">下载模板</a>
    </div>
    <template #footer>
      <a-space style="margin: 6px 16px">
        <a-button class="w-64" @click="cancelGroupImport">取消</a-button>
        <a-button type="primary" class="w-64" @click="confirmGroupImport" :loading="loading" :disabled="uploadingBtnDisable">确定</a-button>
      </a-space>
    </template>
  </a-modal>
</template>

<script lang="ts">
import { downloadFile } from '@/utils/file';
import { defineComponent, ref, watch } from 'vue';
import { message } from 'ant-design-vue';

const MaxFileSize = 10 * 1024 * 1024;

export default defineComponent({
  props: {
    visible: {
      value: Boolean,
      default: false,
    },
    loading: Boolean,
  },
  setup(props) {
    const fileList = ref<any[]>([]);
    let uploadingBtnDisable = ref(true);

    watch(fileList, (newValue) => {
      if (newValue.length > 0) {
        uploadingBtnDisable.value = false;
      } else {
        uploadingBtnDisable.value = true;
      }
    });
    return {
      fileList,
      uploadingBtnDisable,
    };
  },
  data() {
    return {};
  },
  watch: {
    visible(value) {
      if (!value) {
        this.fileList = [];
      }
    },
  },
  methods: {
    beforeUpload(file: any) {
      console.log(file);
      if (file.size > MaxFileSize) {
        message.error(`文件大小不能超过${MaxFileSize / 1024 / 1024}M`);
        return false;
      }
      this.fileList = [file];
      return false;
    },
    handleRemove(file: any) {
      const index = this.fileList.indexOf(file);
      const newFileList = this.fileList.slice();
      newFileList.splice(index, 1);
      this.fileList = newFileList;
    },
    cancelGroupImport() {
      this.$emit('cancel');
    },
    confirmGroupImport() {
      this.$emit('ok', this.fileList);
    },
    downloadTemplate() {
      downloadFile({ url: '/web/admin/um/v1/user/import-template' });
    },
  },
});
</script>

<style lang="less" scoped>
.model-tips {
  margin-top: 8px;
  color: #a0a6ab;
}
.ant-upload.ant-upload-drag p.ant-upload-drag-icon .anticon {
  color: #606972;
}
.ant-upload-text {
  font-size: 12px;
  color: #606972;
}
</style>
