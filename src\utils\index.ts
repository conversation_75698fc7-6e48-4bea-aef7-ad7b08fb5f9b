export function getLocalStorage(key: string): Record<string, unknown> {
  let result = {};
  try {
    result = JSON.parse(localStorage.getItem(key) || '');
    // eslint-disable-next-line no-empty
  } catch (err) {}
  return result;
}

export function setLocalStorage(key: string, value: Record<string, unknown>): void {
  const _value = typeof value === 'object' ? JSON.stringify(value) : value;
  localStorage.setItem(key, _value);
}

// 手机号隐藏中间四位
export function hidePhone(phone: string): string {
  const innerPhone = phone || '';
  return innerPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

//防抖
export function debounce(fn: (args?: any) => void, delay: number): (args?: any) => void {
  let timer;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      typeof fn === 'function' && fn();
    }, delay * 1000);
  };
}
//复制
import { message } from 'ant-design-vue';
//复制文本
export function copyContent(text: string, text1: string): void {
  try {
    const oInput = document.createElement('textarea');
    const textData = text ? text : text1;
    oInput.value = textData.split(',').join('\n');
    document.body.appendChild(oInput);
    oInput.select();
    document.execCommand('Copy');
    document.body.removeChild(oInput);
    message.success('复制成功');
  } catch (err) {
    console.warn(err);
  }
}
//复制文本
export function copyContentB(text: string, text1: string, text2: string): void {
  try {
    const oInput = document.createElement('textarea');
    const isText = text ? text : '-';
    const isText1 = text1 ? text1 : '-';
    const isText2 = text2 ? text2 : '-';
    const textData = isText + ',' + isText1 + ',' + isText2;
    oInput.value = textData.split(',').join('\n');
    document.body.appendChild(oInput);
    oInput.select();
    document.execCommand('Copy');
    document.body.removeChild(oInput);
    message.success('复制成功');
  } catch (err) {
    console.warn(err);
  }
}

//打开新的窗口
export function openInNewTab(url) {
  const newWin: any = window.open(url) || {};
  newWin.opener = null;
}

//判断跳转路径的类型
export function isAbsolutePath(url) {
  if (!url) return;
  return RegExp(/(http|https):\/\/www\.[a-zA-Z0-9]+\.[a-zA-Z0-9]+|www\.[a-zA-Z0-9]+\.[a-zA-Z0-9]+|[a-zA-Z0-9]+\.[a-z0-9]+/).test(url);
}

// 校验手机号
export const validatePhone = async (value: string) => {
  if (value?.trim().length > 11) {
    return Promise.reject('手机号不超过11个数字');
  } else {
    if (!/^1[3456789]\d{9}$/.test(value)) {
      return Promise.reject('请输入正确的手机号');
    }
  }
  return Promise.resolve();
};

// 校验邮箱
export const validateEmail = async (value: string) => {
  if (!/^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/.test(value)) {
    return Promise.reject('请输入正常邮箱格式');
  }
  return Promise.resolve();
};

// table的loading状态优化
export const tableAttr = (loading) => {
  return {
    loading: { spinning: loading, tip: '加载中' },
    locale: { emptyText: loading ? ' ' : null }, // 防止出现默认文案，使用空格填充文案
  };
};

// 如何展示table为空文案
export const showNoDataText = (obj) => {
  const values = Object.values(obj);
  return !values.some((item) => {
    return Array.isArray(item) ? item.length > 0 : item;
  });
};
