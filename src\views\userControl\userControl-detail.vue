<template>
  <div class="user-details">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '用户管理', query: $route.query }"> 用户管理 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>用户详情：{{ userInfo.userName }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="main">
      <div class="top-title">
        <span class="title1">用户详情</span>
        <a-button :disabled="userInfo.status === 2" v-if="editable" @click="goUserEdit" type="primary" ghost style="margin-right: 4px"> <edit-outlined /> 编辑 </a-button>
      </div>
      <div class="base-info">
        <HeadTitle :title="infoTitle[0]" />
        <a-row class="info-row">
          <a-col :span="8">
            <span>用户名：</span><span>{{ checkData(userInfo.userName) }}</span>
          </a-col>
          <a-col :span="8">
            <span>用户ID：</span><span>{{ checkData(userInfo.userId) }}</span>
          </a-col>
          <a-col :span="8">
            <span>注册时间：</span><span>{{ checkData(userInfo.createTime) }}</span>
          </a-col>
          <a-col :span="8">
            <span>姓名：</span><span>{{ checkData(userInfo.fullName) }}</span>
          </a-col>
          <a-col v-if="!loginType.isOnlyPassword()" :span="8">
            <span>手机号：</span><span>{{ checkData(userInfo.phoneNum) }}</span>
          </a-col>
          <a-col :span="8">
            <span>注册渠道：</span><span>{{ checkData(userInfo.channel) }}</span>
          </a-col>
          <a-col :span="8">
            <span>邮箱：</span><span :title="checkTitle(checkData(userInfo.email))" class="info-email">{{ checkMail(checkData(userInfo.email)) }}</span>
          </a-col>
          <a-col v-if="!loginType.isOnlySmscode()" :span="8"> <span>密码：</span><span>******</span> <a-button type="link" :disabled="userInfo.status === 2" @click="visible = true">重置</a-button> </a-col>
          <a-col :span="8">
            <span>状态：</span>
            <a-tag style="border-radius: 8px" :color="userInfo.status === 0 ? 'success' : userInfo.status === 1 ? 'default' : 'error'">{{ checkStatus(userInfo.status) }}</a-tag>
          </a-col>
          <a-col :span="8" v-if="showInviter">
            <span>邀请人：</span><span>{{ checkData(userInfo.inviter) }}</span>
          </a-col>
          <a-col :span="16" class="organization">
            <span>所属组织：</span>
            <span>{{ checkData(userInfo.formatGroupPath) }}</span>
          </a-col>
          <a-col :span="8"> </a-col>
          <a-row v-if="isUseUserIdentity" style="width: 100%">
            <a-col :span="8">
              <span>身份：</span><span>{{ checkData(userInfo.identity) }}</span>
            </a-col>
            <a-col :span="8">
              <span>学校：</span><span>{{ checkData(userInfo.school) }}</span>
            </a-col>
            <a-col :span="8" class="headPortrait">
              <span>头像：</span>
              <img v-if="userInfo.image" :src="HeadImage(userInfo.image)" alt="" />
              <img v-else src="@/assets/images/avatar_big.png" alt="" />
            </a-col>
            <a-col :span="8">
              <span>院系：</span><span>{{ checkData(userInfo.faculty) }}</span>
            </a-col>
            <a-col :span="8">
              <span>专业：</span><span>{{ checkData(userInfo.major) }}</span>
            </a-col>
            <a-col :span="8"> </a-col>
            <a-col :span="8">
              <span>学号：</span><span>{{ checkData(userInfo.stuNum) }}</span>
            </a-col>
            <a-col :span="8">
              <span>工作单位：</span><span>{{ checkData(userInfo.company) }}</span>
            </a-col>
            <a-col :span="8"> </a-col>
            <a-col :span="16">
              <span>简介：</span><span>{{ checkData(userInfo.introduction) }}</span>
            </a-col>
          </a-row>
        </a-row>
      </div>
      <div class="role-info">
        <HeadTitle :title="infoTitle[1]" />
        <div class="search-box">
          <a-input class="role-search" @change="handlerSearchInfo" placeholder="平台 / 角色 / 角色描述 / 权限" v-model:value="searchData">
            <template #prefix>
              <jt-icon type="iconsousuo" />
            </template>
          </a-input>
        </div>
        <a-tabs class="set-width" v-model:activeKey="tabActiveValue" @change="tabChange">
          <a-tab-pane v-for="item in tabListValue" :key="item.code" :tab="item.name" force-render></a-tab-pane>
        </a-tabs>
        <a-table
          showSizeChanger="true"
          class="info-table"
          :loading="tableAttr(isLoading).loading"
          :columns="columns"
          :data-source="tableList"
          @change="handleTableChange"
          :pagination="{
            showSizeChanger: 'true',
            total: tableList.length,
            current: current,
            pageSize: pageSize,
          }"
          rowKey="rowKey"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'permissions'">
              <a-tooltip>
                <template #title>
                  <span> {{ record.permissions }}</span>
                </template>
                {{ record.permissions }}
              </a-tooltip>
            </template>
          </template>
        </a-table>
      </div>
      <a-form :colon="false" class="form-content" ref="ruleForm" :model="passwordForm" :rules="passwordRules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-modal :maskClosable="false" @cancel="resetForm" :visible="visible" title="重置密码">
          <template #footer>
            <a-button key="back" @click="resetForm">取消</a-button>
            <a-button key="submit" type="primary" html-type="submit" @click="resetPassword">确定</a-button>
          </template>
          <a-form-item label="新密码" name="newPassword">
            <a-input-password placeholder="请重新设置密码" autocomplete="new-password" v-model:value="passwordForm.newPassword" />
          </a-form-item>
          <a-form-item label="确认密码" name="confirmPassword">
            <a-input-password placeholder="请再次输入密码" autocomplete="new-password" v-model:value="passwordForm.confirmPassword" />
          </a-form-item>
          <a-form-item v-if="isUseUserIdentity" style="margin-bottom: 0" label="登录后强制修改">
            <a-switch v-model:checked="passwordForm.temporary" checked-children="开" un-checked-children="关" default-checked />
            <p class="form-tip">开启后，用户使用该密码登录后，将强制其再次设置新密码</p>
          </a-form-item>
        </a-modal>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts">
import HeadTitle from './components/headTitle/index.vue';
import { defineComponent } from 'vue';
import { message } from 'ant-design-vue';
import { EditOutlined } from '@ant-design/icons-vue';
import _ from 'lodash';
import request from '@/request';
import { getEnvConfig } from '@/config';
import { auths, isLoginType } from './auth';
import { checkKeycloakAuth } from '@/utils/auth';
import { tableAttr, showNoDataText } from '@/utils';
import { setPassword } from './utils/encryptStr';
import { getClientList } from './utils/baseTool';
import { STATUSENUM } from './utils/enum';
import { pwdRegex } from '@/constants/regex';

const keycloakAuths = {
  edit: checkKeycloakAuth('COMMON_MGM_USER_EDIT'),
};

export default defineComponent({
  components: {
    HeadTitle,
    EditOutlined,
  },
  data() {
    return {
      userId: this.$route.query.id,
      infoTitle: ['基本信息', '角色信息'],
      userInfo: {} as any,
      tabActiveValue: 'all',
      tabListValue: [
        {
          code: 'all',
          name: '全部',
        },
      ],
      columns: [
        {
          title: '平台',
          dataIndex: 'platformCode',
          key: 'platformCode',
        },
        {
          title: '角色',
          dataIndex: 'roleName',
          key: 'roleName',
        },
        {
          title: '角色描述',
          dataIndex: 'roleDesc',
          key: 'roleDesc',
        },
        {
          title: '权限',
          key: 'permissions',
          dataIndex: 'permissions',
          ellipsis: true,
        },
      ],
      getTabCode: 'all',
      isLoading: true,
      current: 1,
      pageSize: 10,
      searchData: '',
      tableList: [] as any[],
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      visible: false,
      passwordForm: {
        newPassword: '',
        confirmPassword: '',
        temporary: true, //是否强制修改
      },
    };
  },
  created() {
    this.getUserInfo();
    this.roleList();
  },
  computed: {
    passwordRules() {
      return {
        newPassword: [
          { required: true, message: '8-20个字符，必须包含大、小写字母和数字', trigger: ['change', 'blur'] },
          {
            validator: (rule, value, callback) => {
              if (value === '' && this.isUseUserIdentity) {
                return Promise.resolve();
              }
              if (pwdRegex.test(value)) {
                return window.zxcvbn(value).score > 1 ? Promise.resolve() : Promise.reject('密码过于简单或存在安全风险，请修改');
              } else {
                // 为空的时候防止与required校验重复
                return Promise.reject(value === '' ? '' : '8-20个字符，必须包含大、小写字母和数字');
              }
            },
            trigger: ['blur', 'change'],
          },
        ],
        confirmPassword: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === this.passwordForm.newPassword) {
                return Promise.resolve();
              } else {
                return Promise.reject('新密码和旧密码不一致');
              }
            },
            message: '两次密码输入不一致',
            trigger: ['blur', 'change'],
          },
        ],
      };
    },
    isUseUserIdentity() {
      return getEnvConfig('USE_USER_IDENTITY') === '1';
    },
    loginType() {
      return isLoginType;
    },
    editable() {
      return auths.edit && keycloakAuths.edit;
    },
    showInviter(this: any) {
      return this.userInfo.channel === 'PC端受邀注册' || this.userInfo.channel === 'H5端受邀注册';
    },
  },
  methods: {
    tableAttr,
    showNoDataText,
    checkData(val) {
      return val ? val : '--';
    },
    checkTitle(email) {
      return email.length > 29 ? email : '';
    },
    checkMail(mail) {
      return mail.length > 29 ? mail.slice(0, 29) + '...' : mail;
    },
    checkStatus(status) {
      const item = STATUSENUM.find((item) => item.value === status);
      return item ? item.text : '--';
    },
    goUserEdit(this: any) {
      this.$router.push({
        path: '/user-management/userControl-edit',
        query: {
          ...this.$route.query,
          flag: '1',
          id: this.userId,
        },
      });
    },
    // 获取基本信息
    async getUserInfo() {
      await request('/web/admin/um/v1/user/detail', { method: 'GET', data: { userId: this.userId } }).then((res: any) => {
        if (res.data && res.msg == 'OK') {
          this.userInfo = res.data;
          if (this.showInviter) this.getInviterName();
        }
      });
    },
    // 获取邀请人用户名
    getInviterName() {
      request('/marketing/web/getInviterNameByUser', { method: 'GET', data: { username: this.userInfo.userName } }).then((res: any) => {
        if (res.body && res.state == 'OK') {
          this.userInfo.inviter = res.body;
        }
      });
    },
    HeadImage(val) {
      if (!val || val == undefined) return val;
      return `./${val}`;
    },
    tabChange(code) {
      this.isLoading = true;
      this.searchData = '';
      this.getRoleList(code);
    },
    handlerSearchInfo(event) {
      const newValue = event.target.value;
      if (newValue === '') {
        this.getRoleList(this.getTabCode);
      }
      const filteredData = this.tableList
        .map((item) => {
          const { ...rest } = item;
          return { ...rest };
        })
        .filter((item) => {
          if (item.roleName.includes(newValue)) {
            return item;
          }
          if (item.platformCode.includes(newValue)) {
            return item;
          }
          if (item.roleDesc && item.roleDesc.includes(newValue)) {
            return item;
          }
          if (item.permissions && item.permissions.includes(newValue)) {
            return item;
          }
        });
      if (filteredData) {
        this.tableList = filteredData;
      }
    },
    async roleList() {
      let getTableCode = await getClientList();
      if (getTableCode) {
        this.tabListValue = this.tabListValue.concat(getTableCode);
      }
      await this.getRoleList();
    },
    // 获取角色信息表数据
    async getRoleList(code = 'all') {
      this.getTabCode = code;
      await request('/web/admin/um/v1/user/role/list', {
        method: 'GET',
        data: {
          platformCode: code,
          userId: this.userId,
        },
      }).then((res1: any) => {
        if (res1.msg == 'OK' && res1.data != null) {
          let resData = res1.data;
          // 过滤平台名称
          resData.map((itemTab) => {
            const match = this.tabListValue.find((itemValue) => itemValue.code === itemTab.platformCode);
            if (match) {
              itemTab.platformCode = match.name;
            }
            this.tableList = resData;
          });

          this.isLoading = false;
        }
      });
    },
    changePageNum(pageNum) {
      this.postData.pageNum = pageNum;
      this.getRoleList();
    },
    changePageSize(pageSize) {
      this.postData.pageSize = pageSize;
      this.postData.pageNum = 1;
      this.getRoleList();
    },
    // 重置密码
    resetPassword(this: any) {
      this.$refs.ruleForm.validate().then(async () => {
        let passwordForge = await setPassword(this.passwordForm.newPassword);
        // '表单校验成功
        await request('/web/admin/um/v1/user/password/reset', {
          data: { password: passwordForge, temporary: this.passwordForm.temporary, userId: this.userId },
          method: 'POST',
        }).then((res: any) => {
          if (res.msg === 'OK') {
            this.resetForm();
            message.success('重置成功');
          } else {
            this.resetForm();
            message.error(res.msg);
          }
        });
      });
    },
    resetForm(this: any) {
      this.visible = false;
      this.$refs.ruleForm.resetFields();
    },
    handleTableChange(pag, filters, sorter) {
      this.current = pag.current;
      this.pageSize = pag.pageSize;
    },
  },
});
</script>
<style lang="less" scoped>
.user-details {
  .nav {
    height: 58px;
    line-height: 58px;
    padding-left: 20px;
    background: #ffffff;
    font-weight: 500;
    color: #121f2c;
  }

  .main {
    padding: 20px;
    margin: 20px;
    background: #ffffff;
    position: relative;

    .top-title {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;

      .title1 {
        font-weight: 500;
        color: #121f2c;
        font-size: 16px;
      }

      /deep/ .ant-btn-background-ghost {
        margin-left: 15px;
      }
    }

    .base-info {
      margin-bottom: 20px;
      padding-bottom: 20px;

      .info-row {
        padding: 20px 0 20px 20px;
        border-bottom: 1px solid #efefef;
      }

      .ant-row {
        .ant-col {
          margin-bottom: 24px;

          span:nth-of-type(1) {
            display: inline-block;
            width: 80px;
            text-align: right;
            color: #606972;
          }

          // span:nth-of-type(2) {
          //   width: 70px;
          //   text-align: right;
          //   color: #121f2c;
          // }
        }

        .organization {
          span {
            vertical-align: top;
          }

          span:nth-of-type(2) {
            text-align: left;
            display: inline-block;
            min-width: 600px;
            word-wrap: break-word;
          }
        }

        .headPortrait {
          span {
            vertical-align: top;
          }

          img {
            width: 120px;
            height: 120px;
            position: absolute;
            top: 0;
            left: 78px;
            border-radius: 50%;
            border: none;
          }
        }
      }
    }

    .role-info {
      position: relative;
      margin-bottom: 20px;

      .set-width {
        width: calc(100% - 350px);
      }

      .search-box {
        position: absolute;
        top: 34px;
        right: 0;
        z-index: 1;

        .role-search {
          width: 250px;

          /deep/.anticon svg {
            width: 18px;
            height: 18px;
          }
        }
      }

      /deep/.ant-tabs-nav {
        margin-top: 9px;
      }

      /deep/.ant-tabs-nav::before {
        border: none;
      }

      /deep/.ant-tabs-tab {
        width: 136px;
        height: 32px;
        display: flex;
        justify-content: center;
        margin: 0;
        font-size: 14px;
        color: #606972;
        line-height: 20px;
        border-bottom: 1px solid #e0e1e1;
      }

      /deep/.ant-tabs-tab-active {
        font-weight: 500;
        color: #0082ff;
      }

      .info-table {
        margin: 0 0 20px 0;

        .table-empty {
          font-size: 18px;
          font-weight: bold;
          position: relative;
          top: -120px;
        }
      }

      /deep/.ant-table {
        font-size: 12px;

        th {
          background: #edf1f3;
          color: #121f2c;
        }

        td {
          color: #606972;
        }
      }

      /deep/ .ant-spin-nested-loading {
        min-height: 0px;
      }
    }
  }
}

.form-tip {
  width: 312px;
  font-size: 12px;
  color: #bbbbbb;
  margin-top: 10px;
  white-space: nowrap;
}

/deep/.ant-form-item-label {
  min-width: 110px !important;
}

/deep/#form_item_isForced {
  margin-top: 4px;
}

/deep/.ant-form-item-explain {
  font-size: 12px;
}

/deep/.ant-breadcrumb-link {
  font-weight: 400;
}

// 去除表头竖线
/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}

// 表格列悬停颜色
/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}
</style>
