import { keycloak, getKeycloakClientId } from '@/keycloak';
import { KEYCLOAK_PATH, KEYCLOAK_PATH_ATTR } from '@/config/menu';

export function checkKeycloakPathAuth(authType: string): boolean {
  if (!keycloak.authenticated) {
    return true;
  }
  const authCollections = (keycloak as any).resourceAccess[getKeycloakClientId()]?.roles || [];
  if (KEYCLOAK_PATH[authType]) {
    return KEYCLOAK_PATH[authType].split(',').some((key) => authCollections.includes(key));
  }
  return true;
}

export function checkKeycloakAuth(authType: string): boolean {
  if (!keycloak.authenticated) {
    return true;
  }
  const authCollections = (keycloak as any).resourceAccess[getKeycloakClientId()]?.roles || [];
  return authCollections.includes(authType);
}

// 通过属性进行模块增删改查权限验证
export function checkKeycloakPathAttr(path: string): boolean {
  if (!keycloak.authenticated) {
    return true;
  }
  const authCollections = (keycloak as any).tokenParsed || {};
  if (KEYCLOAK_PATH_ATTR[path]) {
    const authValue = authCollections[KEYCLOAK_PATH_ATTR[path]];
    if (authValue) {
      // 兼容开发平台写发 若返回 [] 数组格式，存在view 则为true
      if (Array.isArray(authValue)) {
        return authValue.includes('view');
      }
      return authValue === '1';
    } else {
      return false;
    }
  }
  return true;
}

export function checkKeycloakAttr(authType: string): boolean {
  if (!keycloak.authenticated) {
    return true;
  }
  const authCollections = (keycloak as any).tokenParsed || {};
  if (authCollections[authType]) {
    return authCollections[authType] === '1';
  } else {
    return false;
  }
}

// 数组类型的权限验证
export function checkKeycloakArray(authType: string): boolean {
  if (!keycloak.authenticated) {
    return true;
  }
  const authCollections = (keycloak as any).tokenParsed || {};
  if (authCollections[authType]) {
    return authCollections[authType];
  } else {
    return false;
  }
}
