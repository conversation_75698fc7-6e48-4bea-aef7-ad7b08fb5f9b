<!-- eslint-disable prettier/prettier -->
<template>
  <div>
    <sub-header title="首页"></sub-header>
    <a-spin :spinning="pageLoading" tip="加载中">
      <Container>
        <container-item>
          <h2 class="sub-title">平台总览</h2>
          <div class="overview-content">
            <template v-for="(item,i) in overviewDta" :key="i">
              <card :class="['sub-item', item?.content?.word === '组织数' && 'org-item' ]" :data="item" type="OVERVIEW"></card>
            </template>
          </div>
        </container-item>
        <container-item>
          <h2 class="sub-title">资源概况</h2>
          <div class="collapse-content" @click="handleCollapse">
            <span :class="['trigger', 'icon', collapsed ? 'icon_open' : 'icon_close']"  />
          </div>
          <div class="collapse-detail-overview">
            <div class="detail-overview-item">
              <div class="side-line"></div>
              <template v-for="(item,i) in trainData" :key="i">
                <card class="sub-item" :data="item" type="RESOURCE"></card>
              </template>
            </div>
            <div class="detail-overview-item" v-if="reasonData?.length">
              <div class="side-line blue"></div>
              <template v-for="(item,i) in reasonData" :key="i">
                <card class="sub-item" :data="item" type="RESOURCE"></card>
              </template>
            </div>
          </div>
          <div :class="{ 'collapse-detail-info': true, 'show': collapsed }">
            <div class="line"></div>
            <!--训练资源详情-->
            <div class="tb-title">训练资源详情</div>
            <a-table class="auto-height-table" :columns="detailColumsForHome('train')" :data-source="trainDataInfo" size="middle" :pagination="false"/>
            <!--推理资源详情-->
            <template v-if="reasonDataInfo?.length">
              <div class="tb-title blue-border">推理资源详情</div>
              <a-table class="auto-height-table" :columns="detailColumsForHome('reason')" :data-source="reasonDataInfo" size="middle" :pagination="false"/>
            </template>
          </div>
        </container-item>
        <container-item>
          <h2 class="sub-title">用户看板</h2>
          <chart-box></chart-box>
        </container-item>
        <container-item>
          <h2 class="sub-title">快捷入口</h2>
          <div class="links-content">
            <template v-for="(item,i) in linksData" :key="i">
                <card class="sub-item" :data="item" type="LINKS"></card>
            </template>
          </div>
        </container-item>
      </Container>
    </a-spin>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Card from '@/components/card.vue';
import ChartBox from '@/components/business/chart/index.vue';
import { detailColumsForHome, SUMMARY_TYPE, SUMMARY_CLASS_NAME, SUMMARY_CLASS, SUMMARY_CLASS_UNIT } from '@/constants';
import { convertToTB, toFixed } from '@/utils/format';
import { Resource, User } from '@/apis';
const { getPlatformAccess, getResourceSummary } = Resource;
const { getWebUserCount, getCountByLevel, getCommonOverview } = User;

export default defineComponent({
  components: { subHeader, Card, ChartBox, Container, ContainerItem },
  setup() {
    const collapsed = ref<boolean>(false);
    let pageLoading = ref<boolean>(false);
    // 平台总览数据
    let overviewDta = ref<Array<Record<string, any>>>([
      {
        imgUrl: require('@/assets/images/home/<USER>'),
        content: {
          count: 0,
          word: '用户数',
          description: '',
        },
      },
      {
        imgUrl: require('@/assets/images/home/<USER>'),
        content: {
          count: 0,
          word: '登录用户数',
          description: '登录过平台的用户数',
        },
      },
      {
        imgUrl: require('@/assets/images/home/<USER>'),
        content: {
          count: 0,
          word: '月活用户数',
          description: '最近一月登录过平台的用户数',
        },
      },
      {
        imgUrl: require('@/assets/images/home/<USER>'),
        content: {
          count: 0,
          word: '组织数',
          description: '',
          orgInfo: [
            { title: '一级', count: 0 },
            { title: '二级', count: 0 },
            { title: '三级', count: 0 },
            { title: '四级', count: 0 },
          ],
        },
      },
    ]);
    // 快捷入口
    let linksData = ref<Array<Record<string, any>>>([]);
    // 资源数据格式
    let trainData = ref<any>([
      {
        title: '训练加速卡',
        used: 0,
        total: 0,
        unit: '卡',
      },
      {
        title: '训练加速卡vGPU',
        used: 0,
        total: 0,
        unit: '卡',
      },
      {
        title: '训练CPU',
        used: 0,
        total: 0,
        unit: '核',
      },
      {
        title: '训练内存',
        used: 0,
        total: 0,
        unit: 'T',
      },
      {
        title: '训练存储',
        used: 0,
        total: 0,
        unit: 'T',
      },
    ]);
    let reasonData = ref<any>([]);
    // 详情表格数据
    let trainDataInfo = ref<any>([]);
    let reasonDataInfo = ref<any>([]);

    let fetchCount = 5;

    // 处理展开收起
    const handleCollapse = () => {
      collapsed.value = !collapsed.value;
    };
    // 请求数
    const fetchComputed = () => {
      fetchCount--;
      if (fetchCount <= 0) {
        pageLoading.value = false;
      }
    };
    // 获取用户数
    const getUserCount = async () => {
      try {
        const res: any = await getWebUserCount();
        const { code, data } = res || {};
        fetchComputed();
        if (code === 0) {
          overviewDta.value.forEach((item: any) => {
            if (item.content.word === '用户数') {
              item.content.count = data || 0;
            }
          });
        }
      } catch (error) {
        console.log(error, 'error');
        pageLoading.value = false;
      }
    };
    // 获取总览数据
    const getOverViewData = async () => {
      try {
        const res: any = await getCommonOverview();
        const { state, body } = res || {};
        fetchComputed();
        if (state === 'OK') {
          overviewDta.value.forEach((item: any) => {
            if (item.content.word === '登录用户数') {
              item.content.count = body?.userLoginCount || 0;
            }
            if (item.content.word === '月活用户数') {
              item.content.count = body?.userMonthActiveCount || 0;
            }
          });
        }
      } catch (error) {
        console.log(error, 'error');
        pageLoading.value = false;
      }
    };
    // 获取组织数
    const getOrgInfo = async () => {
      try {
        const res: any = await getCountByLevel();
        const { code, data = {} } = res || {};
        fetchComputed();
        if (code === 0) {
          overviewDta.value.forEach((item: any) => {
            if (item.content.word === '组织数') {
              item.content.count = data?.total - (data?.[0] || 0);
              item.content.orgInfo = [
                { title: '一级', count: data?.[1] || 0 },
                { title: '二级', count: data?.[2] || 0 },
                { title: '三级', count: data?.[3] || 0 },
                { title: '四级', count: data?.[4] || 0 },
              ];
            }
          });
        }
      } catch (error) {
        console.log(error, 'error');
        pageLoading.value = false;
      }
    };
    // 推理数据特殊处理逻辑
    const handleReasonDta = (item) => {
      if (item?.type === SUMMARY_TYPE.REASON) {
        const arr: any = [];
        for (let ele in item) {
          !['type', 'platformName'].includes(ele) && arr.push(+item[ele]);
        }
        return arr.every((val: any) => !val);
      }
      return false;
    };
    // 获取平台资源信息
    const getResourceInfo = async () => {
      try {
        const res: any = await getResourceSummary();
        const { state, body } = res || {};
        fetchComputed();
        if (state === 'OK') {
          (body || []).forEach((item: any, i) => {
            if (item?.platformName === 'total') {
              // 概览推理数据处理-所有值都为0时则不显示推理资源模块
              if (handleReasonDta(item)) return;
              handleResourceOverview(item);
            } else {
              // 表格详情数据
              handleResourceDetail(item, i);
            }
          });
        }
      } catch (error) {
        console.log(error, 'error');
        pageLoading.value = false;
      }
    };
    // 获取子平台快捷入口信息
    const getPlatformAccessInfo = async () => {
      try {
        const res: any = await getPlatformAccess();
        const { state, body } = res || {};
        fetchComputed();
        if (state === 'OK') {
          linksData.value = body || [];
        }
      } catch (error) {
        console.log(error, 'error');
        pageLoading.value = false;
      }
    };
    //处理资源概况概览数据
    const handleResourceOverview = (item: Record<string, any> = {}) => {
      const adjustItem = {
        gpu: item?.gpu,
        gpuAll: item?.gpuAll,
        vgpu: item?.vgpu,
        vgpuAll: item?.vgpuAll,
        cpu: item?.cpu,
        cpuAll: item?.cpuAll,
        memory: item?.memory,
        memoryAll: item?.memoryAll,
        storage: item?.storage,
        storageAll: item?.storageAll,
      };
      const arr: any = [];
      for (let ele in adjustItem) {
        const obj: any = {};
        if (SUMMARY_CLASS.includes(ele)) {
          obj.title = SUMMARY_CLASS_NAME[item?.type][ele];
          obj.used = item[ele];
          obj.total = item[`${ele}All`];
          obj.unit = SUMMARY_CLASS_UNIT[ele];
          arr.push(obj);
        }
      }
      if (item?.type === SUMMARY_TYPE.TRAIN) {
        trainData.value = arr;
      } else {
        reasonData.value = arr;
      }
    };
    //处理资源概况详情数据
    const handleResourceDetail = (item: Record<string, any> = {}, i = 0) => {
      let arr: any = [],
        obj: any = {};
      obj.key = i;
      obj.platform = item?.platformName;
      obj.GPU = item?.gpuAll ? `${toFixed(item?.gpu, 1)}/${toFixed(item?.gpuAll, 1)}` : `- / -`;
      obj.vGPU = item?.vgpuAll ? `${toFixed(item?.vgpu, 1)}/${toFixed(item?.vgpuAll, 1)}` : `- / -`;
      obj.CPU = item?.cpuAll ? `${toFixed(item?.cpu, 1)}/${toFixed(item?.cpuAll, 1)}` : `- / -`;
      obj.memory = item?.memoryAll ? `${convertToTB(item?.memory)}/${convertToTB(item?.memoryAll)}` : `- / -`;
      obj.storage = item?.storageAll ? `${convertToTB(item?.storage)}/${convertToTB(item?.storageAll)}` : `- / -`;
      arr.push(obj);
      if (item?.type === SUMMARY_TYPE.TRAIN) {
        trainDataInfo.value = trainDataInfo.value.concat(arr);
      } else {
        reasonDataInfo.value = reasonDataInfo.value.concat(arr);
      }
    };
    //获取初始数据
    const getInitData = async () => {
      pageLoading.value = true;
      getUserCount();
      getOverViewData();
      getOrgInfo();
      getResourceInfo();
      getPlatformAccessInfo();
    };

    onMounted(getInitData);
    return {
      pageLoading,
      collapsed,
      overviewDta,
      linksData,
      trainData,
      reasonData,
      detailColumsForHome,
      trainDataInfo,
      reasonDataInfo,
      handleCollapse,
    };
  },
});
</script>

<style lang="less" scoped>
@import url('./home.less');
</style>
