module.exports = {
  '/api/test': {
    target: 'http://172.31.192.238/portal/',
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/api/test/': '/',
    },
    rewrite: (path) => path.replace(/^\/api\/test/, '/'),
  },
  '/api/dev': {
    target: 'http://172.31.192.237/',
    changeOrigin: true,
    logLevel: 'debug',
    pathRewrite: {
      '^/api/dev/': '/',
    },
    rewrite: (path) => path.replace(/^\/api\/dev/, '/'),
  },
  // 图片上传
  './messagecenter': {
    target: 'http://**************:32082/',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
    pathRewrite: {
      '^.*/messagecenter/': '/messagecenter/',
    },
    rewrite: (path) => path.replace(/^.*\/messagecenter\//, '/messagecenter/'),
  },
  // 动态管理部分图片上传
  './homepage': {
    target: 'http://**************:32082/',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
    pathRewrite: {
      '^.*/homepage/': '/homepage/',
    },
    rewrite: (path) => path.replace(/^.*\/homepage\//, '/homepage/'),
  },
  // 文件上传和下载相关接口
  './objects-download': {
    target: 'http://172.31.192.238/portal/',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
    pathRewrite: {
      '^.*/objects-download/': '/objects-download/',
    },
    rewrite: (path) => path.replace(/^.*\/objects-download\//, '/objects-download/'),
  },
  // 文件上传和下载相关接口 for vite
  '/objects-download': {
    target: 'http://*************/edu/',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
  },
  // 文件上传和下载相关接口
  './object': {
    target: 'http://*************/edu/',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
    pathRewrite: {
      '^.*/object/': '/object/',
    },
    rewrite: (path) => path.replace(/^.*\/object\//, '/object/'),
  },
  // 文件上传和下载相关接口 for vite
  '/object': {
    target: 'http://*************/edu/',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
  },
  // 文件上传和下载相关接口
  './keycloak/web/admin': {
    target: 'http://*************/edu/',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
    pathRewrite: {
      '^.*/keycloak/web/admin/': '/keycloak/web/admin/',
    },
  },
  // 文件上传和下载相关接口 for vite
  '/keycloak/web/admin': {
    target: 'http://*************/edu/',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
  },
  // 文件
  './aiipweb/om-service/os/upload': {
    target: 'http://**************:32089/',
    changeOrigin: true, //表示是否跨域
    logLevel: 'debug',
    pathRewrite: {
      '^.*/common-management/': '/',
    },
    rewrite: (path) => path.replace(/^.*\/common-management\//, '/'),
  },
  // 接口文档图片显示

  '/aiipweb': {
    target: 'http://**************:32082/',
    changeOrigin: true, //表示是否跨域
    pathRewrite: {
      '^.*/aiipweb/': '/aiipweb/',
    },
    // rewrite: (path) => path.replace(/^\/file/, '/'),
  },
};
