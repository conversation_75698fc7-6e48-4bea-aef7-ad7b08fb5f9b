apiVersion: apps/v1
kind: Deployment
metadata:
  name: jt-common-frontend-management
  namespace: ${JT_MGM_NAMESPACE}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: jt-common-frontend-management
  template:
    metadata:
      labels:
        app: jt-common-frontend-management
    spec:
      #   affinity:
      #     nodeAffinity:
      #       requiredDuringSchedulingIgnoredDuringExecution:
      #         nodeSelectorTerms:
      #         - matchExpressions:
      #           - key: node
      #             operator: In
      #             values:
      #             - cpu
      terminationGracePeriodSeconds: 10
      containers:
        - name: jt-common-frontend-management
          image: ${HARBOR_BASE_URL}/${JT_SVC_IMAGE_PREFIX}/jt-common-frontend-management:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              protocol: TCP
          securityContext:
            runAsUser: ${CONTAINER_USER_ID}
          env:
            - name: NACOS_ADDRESS
              value: nacos-headless:8848
            - name: SERVER_PORT
              value: '8080'
            # keycloak认证地址
            - name: <PERSON>E<PERSON><PERSON><PERSON><PERSON>_URL
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: KEYCLOAK_URL
            # keycloak 登出 cas前缀
            - name: KEYCLOAK_LOGOUT_PREFIX
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: KEYCLOAK_LOGOUT_PREFIX
            # keycloak初始化的客户端id
            - name: KEYCLOAK_CLIENT_ID
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: KEYCLOAK_CLIENT_ID
            # 用户管理模块使用用户身份信息 1 使用、0 不使用
            - name: USE_USER_IDENTITY
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: USE_USER_IDENTITY
            # 用户管理模块使用登陆方式    
            - name: USER_LOGIN_OPTION
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: USER_LOGIN_OPTION
            # 人工智能运管平台-用户管理可用的操作
            - name: COMMON_MANAGEMENT_USER_MANAGEMENT_AVAILABLE_OPERATIONS
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: COMMON_MANAGEMENT_USER_MANAGEMENT_AVAILABLE_OPERATIONS
            # 消息中心的url后缀
            - name: MESSAGE_URL_PATH
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: MESSAGE_URL_PATH
            # 用于判断当前网络环境是内网还是互联网
            - name: CAPABILITY_INNER_VERSION
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: CAPABILITY_INNER_VERSION
            # 用于显隐消息中心
            - name: FEATURE_MESSAGECENTER
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: FEATURE_MESSAGECENTER
            # 工单详情处，是否支持短信和邮件
            # 1 支持
            # 0 不支持（对应按钮置灰）
            - name: FEATURE_SMS
              valueFrom:
                configMapKeyRef:
                  name: env-configure
                  key: FEATURE_SMS
            # 是否显示问题反馈
            - name: SHOW_REQUIRMENT_ICON
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: SHOW_REQUIRMENT_ICON
            # 是否显示需求反馈
            - name: SHOW_DEMAND_ICON
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: SHOW_DEMAND_ICON
            # 挂件 联系人邮箱
            - name: CONTACT_EMAIL
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: CONTACT_EMAIL
            # 支持的登录方式
            - name: USER_LOGIN_OPTION
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: USER_LOGIN_OPTION
            # 是否账号管理，适配内外网环境
            - name: SHOW_ACCOUNT_MANAGEMENT
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: SHOW_ACCOUNT_MANAGEMENT
            # 是否显示admin类型页面的logo点击跳转路由，适配私有化部署
            - name: ADMIN_HEADER_HOMEPAGE_LINK
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: ADMIN_HEADER_HOMEPAGE_LINK
            # 账号信息编辑里是否可编辑手机号，适配私有化部署
            - name: USER_INFO_PHONE_EDITABLE
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: USER_INFO_PHONE_EDITABLE
              # 问题反馈
            - name: SHOW_FEATURE_TICKET
              valueFrom:
                configMapKeyRef:
                  name: common-env-configure
                  key: SHOW_FEATURE_TICKET