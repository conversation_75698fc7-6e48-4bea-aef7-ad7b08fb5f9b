<template>
  <a-modal :destroyOnClose="true" :width="600" :visible="visible" title="添加接收人" @cancel="handleClose" @ok="handleAdd">
    <a-spin tip="加载中..." :spinning="loading">
      <a-transfer
        class="add-receiver-transfer"
        :list-style="{
          width: '250px',
          height: '300px',
        }"
        :dataSource="dataSource"
        :filterOption="() => true"
        show-search
        @search="handleSearchDebounce"
        :render="(item) => item.userName"
        @change="handleChange"
        :target-keys="targetKeys"
        :selected-keys="selectedKeys"
        @selectChange="handleSelectChange"
        :locale="{
          searchPlaceholder: '请输入完整用户名',
        }"
      >
        <template #notFoundContent>
          <span>暂无数据</span>
        </template>
      </a-transfer>
    </a-spin>
    <div v-if="targetKeys.length === 0" class="addreceiver-alert-msg">
      请输入已注册的用户名进行搜索，并选择至少一个用户
      <br />
      请确保用户已具有需求工单或当前产品的工单管理权限，否则无法被搜索到
    </div>
  </a-modal>
</template>

<script lang="ts">
import request from '@/request';
import { defineComponent, ref, toRaw } from 'vue';
import { message } from 'ant-design-vue';
import { debounce } from 'lodash';
import { useRoute } from 'vue-router';

interface userInfoItem {
  phoneNum: string;
  userId: string;
  userName: string;
  key: string;
}

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const route = useRoute();
    const platform = route.query.platform;
    const isQuestionManage = route.query.activeKey === '2';
    let dataSource = ref<userInfoItem[]>([]);
    const targetKeys = ref<string[]>([]);
    const selectedKeys = ref<string[]>([]);
    const loading = ref<boolean>(false);

    const handleChange = (nextTargetKeys: string[], direction: string, moveKeys: string[]) => {
      targetKeys.value = nextTargetKeys;
    };
    const handleSelectChange = (sourceSelectedKeys: string[], targetSelectedKeys: string[]) => {
      selectedKeys.value = [...sourceSelectedKeys, ...targetSelectedKeys];
    };

    const handleClose = () => {
      emit('handleVisible', false);
      clearTransferData();
    };

    // 穿梭框左侧内容搜索
    const handleSearch = async (direction: 'left' | 'right', value: string) => {
      if (direction === 'left') {
        loading.value = true;
        if (value) {
          const url = isQuestionManage ? '/ticket/web/receiver/search' : '/ticket/web/demand/receiver/search';
          const res = await request(url, { method: 'GET', data: { userName: value } });
          if (res.state === 'OK') {
            const result = res.body.userId ? [res.body] : [];
            let data: userInfoItem[] = result.map((item) => {
              let userinfo = { ...item, key: item.userId };
              return userinfo;
            });
            // 在targetkeys中的items还需要保留并去重
            const currentTargetItems = dataSource.value.filter((item) => targetKeys.value.includes(item.key));
            data = [...data, ...currentTargetItems];
            let keyset = new Set();
            data = data.filter((item) => {
              if (keyset.has(item.userId)) {
                return false;
              } else {
                keyset.add(item.userId);
                return true;
              }
            });
            dataSource.value = data;
          } else {
            dataSource.value = dataSource.value.filter((item) => targetKeys.value.includes(item.key));
          }
        } else {
          dataSource.value = dataSource.value.filter((item) => targetKeys.value.includes(item.key));
        }
        loading.value = false;
      }
    };

    const handleSearchDebounce = debounce(handleSearch, 500);

    const clearTransferData = () => {
      dataSource.value = [];
      targetKeys.value = [];
      selectedKeys.value = [];
    };

    // 添加接收人
    const handleAdd = async () => {
      if (targetKeys.value.length === 0) {
        message.warning('请选择至少一个用户');
        return;
      }
      const userIds: string[] = [];
      dataSource.value
        .filter((item) => {
          return targetKeys.value.includes(item.key);
        })
        .map((x) => userIds.push(toRaw(x).userId));
      const reqData = {
        platform,
        userIds,
      };
      const url = isQuestionManage ? '/ticket/web/receiver/adds' : '/ticket/web/demand/receiver/adds';
      let res = await request(url, { method: 'POST', data: reqData });
      if (res.state === 'OK') {
        message.success('添加接收人成功');
        emit('initTableData');
        handleClose();
      } else {
        const errorUsers = res.errorParams.join('/');
        message.error(res.errorMessage ? `用户${errorUsers}${res.errorMessage}` : '添加接收人失败');
      }
    };

    return {
      dataSource,
      targetKeys,
      selectedKeys,
      loading,
      handleClose,
      handleAdd,
      handleChange,
      handleSearchDebounce,
      handleSelectChange,
    };
  },
});
</script>

<style lang="less" scoped>
.add-receiver-transfer {
  :deep(.ant-transfer-list:nth-child(3)) {
    .ant-transfer-list-body-with-search {
      padding-top: 0px;
      .ant-transfer-list-body-search-wrapper {
        display: none;
      }
    }
  }
}
.addreceiver-alert-msg {
  color: red;
  margin-top: 6px;
}
</style>
