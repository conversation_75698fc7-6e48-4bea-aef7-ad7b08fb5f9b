<template>
  <div class="org-detail">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '组织管理', query: route.query }"> 组织管理 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>组织详情</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="main">
      <div class="top-title">
        <span class="title">组织详情</span>
        <a-button v-if="showEdit" @click="goEdit" type="primary" ghost style="margin-right: 4px"> <edit-outlined /> 编辑 </a-button>
      </div>
      <div class="base-info">
        <a-row class="info-row">
          <a-col :span="8">
            <div class="col-item">
              <div class="col-label">组织名称:</div>
              <div class="col-content">{{ detailInfo?.groupName || '--' }}</div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="col-item">
              <div class="col-label">组织层级:</div>
              <div class="col-content">{{ levelFilter[detailInfo?.level] }}</div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="col-item">
              <div class="col-label">创建时间:</div>
              <div class="col-content">{{ detailInfo?.createTime ? dayjs(detailInfo?.createTime).format('YYYY-MM-DD HH:mm:ss') : '--' }}</div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="col-item">
              <div class="col-label">所属组织:</div>
              <div class="col-content">
                <div>一级组织:  {{ detailInfo.firstGroup }}</div>
                <div>二级组织:  {{ detailInfo.secondGroup }}</div>
                <div>三级组织:  {{ detailInfo.thirdGroup }}</div>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="col-item">
              <div class="col-label">子组织数:</div>
              <div class="col-content">
                <div>二级组织数:  {{ detailInfo.secondSubGroupCount }}</div>
                <div>三级组织数:  {{ detailInfo.thirdSubGroupCount }}</div>
                <div>四级组织数:  {{ detailInfo.fourthSubGroupCount }}</div>
              </div>
            </div>
          </a-col>
          <a-col :span="8">
            <div class="col-item">
              <div class="col-label">用户数:</div>
              <div class="col-content">
                {{ typeof(detailInfo?.userCount) == 'number' ? detailInfo.userCount : '--' }}
                <!-- <a-button type="text" @click="goDetail">查看详情</a-button> -->
                <!-- <span v-if="detailInfo?.userCount" class="col-detail" @click="goDetail">查看详情</span> -->
                <span class="col-detail" @click="goDetail">查看详情</span>
              </div>
            </div>
            <div class="col-item">
              <div class="col-label">
                <a-tooltip overlay-class-name="model-tooltip-wrapper">
                  <template #title>最近一月登录过平台的用户数</template>
                  <QuestionCircleOutlined class="hover-icon" />
                </a-tooltip>
                活跃用户数:
              </div>
              <div class="col-content">
                {{ typeof(detailInfo?.activeUser) == 'number' ? detailInfo.activeUser : '--' }}
              </div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
    <edit-modal :visible="editModalOpen" :detail="detailInfo" @cancelModal="cancelModal" />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { message } from 'ant-design-vue';
import { EditOutlined, QuestionCircleOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import request from '@/request';
import EditModal from './components/edit-modal.vue';
import { checkKeycloakAttr } from '@/utils/auth';
import { useRoute, useRouter } from 'vue-router';
import { openInNewTab } from '@/utils';


const route = useRoute();
const router = useRouter();

const keycloakAuths = {
  edit: checkKeycloakAttr('jtc-group-manage'),
};

const showEdit = computed(() => {
  return keycloakAuths.edit;
});

const detailInfo = ref({});

const getDeteil = async () => {
  await request('/web/admin/um/v1/group/detail', {
    method: 'GET',
    data: { groupId: route.query.id },
  }).then((res) => {
    if (res.code === 0) {
      detailInfo.value = res.data;
    } else {
      detailInfo.value = {};
    }
  }).catch(() => {
    detailInfo.value = {};
  });
};
getDeteil();

const levelFilter = {
  '1': '一级组织',
  '2': '二级组织',
  '3': '三级组织',
  '4': '四级组织',
  undefined: '--',
};

const editModalOpen = ref(false);
const goEdit = () => {
  editModalOpen.value = true;
};
// 关闭
const cancelModal = (value) => {
  editModalOpen.value = false;
  if (value) {
    getDeteil();
  }
};

const goDetail = () => {
  let firstGroupName = '';
  let secondGroupName = '';
  let thirdGroupName = '';
  let fourthGroupName = '';
  if (detailInfo.value.level === '1') {
    firstGroupName = detailInfo.value?.groupName;
    secondGroupName = '';
    thirdGroupName = '';
    fourthGroupName = '';
  } else if (detailInfo.value.level === '2') {
    firstGroupName = detailInfo.value?.firstGroup;
    secondGroupName = detailInfo.value?.groupName;
    thirdGroupName = '';
    fourthGroupName = '';
  } else if (detailInfo.value.level === '3') {
    firstGroupName = detailInfo.value?.firstGroup;
    secondGroupName = detailInfo.value?.secondGroup;
    thirdGroupName = detailInfo.value?.groupName;
    fourthGroupName = '';
  } else {
    firstGroupName = detailInfo.value?.firstGroup;
    secondGroupName = detailInfo.value?.secondGroup;
    thirdGroupName = detailInfo.value?.thirdGroup;
    fourthGroupName = detailInfo.value?.groupName;
  }
  const routeUrl = router.resolve({ 
    path: `/user-management`,
    query: {
      // pageSize: 10,
      // pageNum: 1,
      firstGroup: firstGroupName,
      secondGroup: secondGroupName,
      thirdGroup: thirdGroupName,
      fourthGroup: fourthGroupName,
    }
  });
  openInNewTab(routeUrl.href);
};
</script>

<style lang="less" scoped>
.org-detail {
  .nav {
    height: 58px;
    line-height: 58px;
    padding-left: 20px;
    background: #ffffff;
    font-weight: 500;
    color: #121f2c;
  }
  .main {
    padding: 20px 20px 0 20px;
    margin: 20px;
    background: #ffffff;
    position: relative;
    .top-title {
      display: flex;
      justify-content: space-between;
      .title {
        font-weight: 500;
        color: #121f2c;
        font-size: 16px;
      }
      /deep/ .ant-btn-background-ghost {
        margin-left: 15px;
      }
    }
    .base-info {
      margin-top: 60px;
      .info-row {
        padding: 20px 0 0 20px;
      }
      .ant-row {
        .ant-col {
          margin-bottom: 24px;
          .col-item {
            display: flex;
            &:first-child {
              margin-bottom: 20px;
            }
            .hover-icon {
              font-size: 14px;
              color: #7f828f;
            }
          }
          .col-label {
            width: 140px;
            text-align: right;
            color: #606972;
            margin-right: 8px;
          }
          .col-content {
            color: #121f2c;
            .col-detail {
              font-size: 14px;
              color: #0082ff;
              padding-left: 15px;
              cursor: pointer;
            }
            div {
              margin-bottom: 15px;
            }
          }
        }
      }
    }
  }
}
:deep .ant-btn {
  font-size: 12px;
}
:deep .ant-breadcrumb-link {
  font-weight: 400;
}
:deep .ant-breadcrumb a:hover {
  color: #0082ff;
}
</style>
