<template>
  <div class="help-control-container">
    <sub-header title="能力文档管理"></sub-header>
    <a-spin :spinning="isLoding">
      <Container>
        <container-item>
          <div class="top-bar">
            <h3>开放能力文档</h3>
          </div>
          <table-tree :columns="detailColumsForTableTree('skillDocument')" type="SKILL" :dataSource="dataSource" :expandKeys="expandKeys" :loading="tableLoading" @refreshData="initTableData" />
          <Pagination :total="page.total" :pageNum="page.pageNum" :pageSize="page.pageSize" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
        </container-item>
      </Container>
    </a-spin>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import tableTree from '@/components/business/tableTree.vue';
import { detailColumsForTableTree } from '@/constants';
import { findParentIdById, formateDataSource } from '@/utils/format';
import { HelpCenter } from '@/apis';
const { getList } = HelpCenter;

export default defineComponent({
  components: {
    subHeader,
    Container,
    ContainerItem,
    tableTree,
    Pagination,
  },
  data() {
    return {
      isLoding: false,
      tableLoading: false,
      dataSource: [] as any,
      expandKeys: [] as any,
      page: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      } as any,
      detailColumsForTableTree,
    };
  },
  created() {
    this.initTableData();
  },
  computed: {},
  methods: {
    // 初始化table数据
    async initTableData(expandcatalogId: number | undefined = undefined) {
      this.tableLoading = true;
      const { pageNum, pageSize } = this.page;
      await getList({ name: 'SKILL', type: 'POST', data: { pageNum, pageSize } }).then((res: any) => {
        if (res.state === 'OK') {
          // 格式化 添加是否可排序 及发布 删除的显隐控制
          this.dataSource = formateDataSource(res.body.list || []);
          // 新建和编辑页面跳转过来时需要展开对应的父项
          if (expandcatalogId) {
            const parentId: any = findParentIdById(this.dataSource, expandcatalogId);
            parentId && this.expandKeys.push(parentId);
            this.expandKeys.push(expandcatalogId);
          }
          this.page.total = res.body.total;
        } else {
          this.dataSource = [];
        }
      });
      this.tableLoading = false;
    },
    // 分页操作
    changePageNum(pageNum) {
      this.page = { ...this.page, ...{ pageNum } };
      this.initTableData();
    },
    changePageSize(pageSize) {
      this.page = { ...this.page, ...{ pageNum: 1, pageSize } };
      this.initTableData();
    },
  },
});
</script>
<style lang="less" scoped>
.help-control-container {
  position: relative;
  .jt-pagination {
    display: flex;
    justify-content: space-between;
    padding: 16px 0px;
    padding-bottom: 0;
  }
}
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.spin {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}
</style>
