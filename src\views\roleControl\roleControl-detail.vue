<template>
  <div class="user-details">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '角色管理', query: $route.query }"> 角色管理 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>角色详情</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="main">
      <div class="top-title">
        <span class="title1">角色详情</span>
        <a-button @click="goRoleEdit" type="primary" ghost style="margin-right: 4px" v-if="showCreate" :disabled="roleEditBtnDisabled()"> <edit-outlined /> 编辑 </a-button>
      </div>
      <div class="base-info">
        <HeadTitle :title="infoTitle[0]" />
        <a-row class="info-row">
          <a-col :span="12">
            <span>角色：</span><span>{{ checkData(baseInfo.name) }}</span>
          </a-col>
          <a-col :span="12">
            <span>平台：</span><span>{{ checkData(baseInfo.platformName) }}</span>
          </a-col>
          <a-col :span="12">
            <span>创建时间：</span><span>{{ checkData(baseInfo.createTime) }}</span>
          </a-col>
          <a-col :span="12">
            <span>用户数：</span><span>{{ baseInfo.userCount }}</span>
          </a-col>
          <a-col :span="24" class="organization">
            <span>角色描述：</span>
            <span>{{ checkData(baseInfo.desc) }}</span>
          </a-col>
        </a-row>
      </div>
      <div class="power-info">
        <HeadTitle :title="infoTitle[1] + ' ' + powerTotal" />
        <div class="search-box">
          <a-input class="role-search" placeholder="权限 / 权限描述" @change="handlerPowerSearch">
            <template #prefix>
              <jt-icon type="iconsousuo" />
            </template>
          </a-input>
        </div>
        <a-table class="info-table" @change="powerTableChange" :loading="tableAttr(powerLoading).loading" :columns="powerColumns" :data-source="currentPowerTableData" :pagination="false" size="small" rowKey="rowKey">
          <template #emptyText>
            <empty
              v-if="!powerLoading"
              title="数据"
              :showNoDataText="
                showNoDataText({
                  keyword: powerPostData.searchCondition,
                })
              "
            ></empty>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a-tooltip>
                <template #title>
                  <span>{{ record.name }}</span>
                </template>
                <span class="overflow-ellipsis">
                  <span>{{ record.name }}</span>
                </span>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'type'">
              <span>{{ getPermissionType(record) }}</span>
            </template>
            <template v-if="column.key === 'desc'">
              <a-tooltip>
                <template #title v-if="record.desc">
                  {{ record.desc }}
                </template>
                {{ record.desc || '-' }}
              </a-tooltip>
            </template>
          </template>
        </a-table>
        <Pagination :total="currentPowerTableTotal" :pageNum="powerPostData.pageNum" :pageSize="powerPostData.pageSize" @update:pageNum="powerChangePageNum" @update:pageSize="powerChangePageSize" />
      </div>
    </div>
    <div class="main">
      <div class="top-title">
        <span class="title1">角色用户列表</span>
      </div>
      <div class="user-list">
        <HeadTitle :title="infoTitle[2] + ' ' + roleTotal" />
        <div class="search-box">
          <a-input class="role-search" @change="handlerRoleSearch" v-model="rolePostData.searchKey" placeholder="用户名 / 手机号 / 姓名 / 用户ID">
            <template #prefix>
              <jt-icon type="iconsousuo" />
            </template>
          </a-input>
          <a-button @click="goUserEdit" type="primary" ghost style="margin: 0 4px 0 10px" v-if="showCreate" :disabled="roleEditBtnDisabled()"> <edit-outlined /> 编辑 </a-button>
        </div>
        <a-table class="info-table" :loading="tableAttr(roleLoading).loading" :columns="roleColumns" :data-source="roleList" :pagination="false" size="small" rowKey="rowKey">
          <template #emptyText>
            <empty
              v-if="!roleLoading"
              title="数据"
              :showNoDataText="
                showNoDataText({
                  keyword: rolePostData.searchKey,
                })
              "
            ></empty>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'userName'">
              <a-tooltip>
                <template #title v-if="record.userName">
                  {{ record.userName }}
                </template>
                <span>
                  {{ record.userName || '--' }}
                </span>
              </a-tooltip>
            </template>
            <template v-if="column.key === 'phoneNum'">
              <span v-if="record.phoneNum">{{ disposePhone(record.phoneNum) }}</span>
              <span v-else>--</span>
            </template>
            <template v-if="column.key === 'fullName'">
              <a-tooltip>
                <template #title v-if="record.fullName">
                  {{ record.fullName }}
                </template>
                {{ record.fullName || '--' }}
              </a-tooltip>
            </template>
            <template v-if="column.key === 'userId'">
              <a-tooltip>
                <template #title v-if="record.userId">
                  {{ record.userId }}
                </template>
                {{ record.userId || '--' }}
              </a-tooltip>
            </template>
          </template>
        </a-table>
        <Pagination :total="roleTotal" :pageNum="rolePostData.pageNum" :pageSize="rolePostData.pageSize" @update:pageNum="roleChangePageNum" @update:pageSize="roleChangePageSize" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import _ from 'lodash';

import { Role } from '@/apis';
import { tableAttr, showNoDataText } from '@/utils';
import { ROLE_TYPE, PERMISSION_TYPE } from '@/constants/role';

import HeadTitle from '@/components/headTitle.vue';
import { EditOutlined } from '@ant-design/icons-vue';
import Pagination from '@/components/pagination.vue';
import empty from '@/components/empty.vue';
import { checkKeycloakAttr } from '@/utils/auth';

// import request from '@/request';
// import { getEnvConfig } from '@/config';
const powerColumns = [
  {
    title: '权限',
    dataIndex: 'name',
    key: 'name',
    scopedSlots: { customRender: 'name' },
    width: '20%',
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    scopedSlots: { customRender: 'type' },
    // filters: [
    //   {
    //     text: '查看权限',
    //     value: '1',
    //   },
    //   {
    //     text: '操作权限',
    //     value: '2',
    //   },
    //   {
    //     text: '其他权限',
    //     value: '3',
    //   },
    // ],
    onFilter: (value, record) => record.type === value,
    width: '20%',
  },
  {
    title: '权限描述',
    dataIndex: 'desc',
    key: 'desc',
    scopedSlots: { customRender: 'desc' },
    width: '60%',
  },
];
const roleColumns = [
  {
    title: '用户名',
    key: 'userName',
    dataIndex: 'userName',
    scopedSlots: { customRender: 'userName' },
    width: '13%',
    ellipsis: true,
  },
  {
    title: '手机号',
    key: 'phoneNum',
    dataIndex: 'phoneNum',
    scopedSlots: { customRender: 'phoneNum' },
    width: '13%',
  },
  {
    title: '姓名',
    key: 'fullName',
    dataIndex: 'fullName',
    scopedSlots: { customRender: 'fullName' },
    width: '13%',
  },
  {
    title: '用户ID',
    key: 'userId',
    dataIndex: 'userId',
    scopedSlots: { customRender: 'userId' },
    width: '60%',
  },
];
export default defineComponent({
  components: {
    HeadTitle,
    Pagination,
    EditOutlined,
    empty,
  },
  data() {
    return {
      roleId: this.$route.query.id,
      platformName: this.$route.query.platform,
      platformCode: this.$route.query.platformCode,
      infoTitle: ['基本信息', '权限信息', '用户列表'],
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      baseInfo: {} as any,
      powerColumns,
      powerLoading: true,
      powerList: [] as any[],
      powerTotal: 0,
      powerPostData: { pageNum: 1, pageSize: 10, searchCondition: '', type: [] },
      powerTableFilter: [],
      roleColumns,
      roleLoading: true,
      roleList: [] as any[],
      roleTotal: 0,
      rolePostData: { pageNum: 1, pageSize: 10, searchKey: '' },
    };
  },
  created() {
    this.getPermissionTypeList();
    this.getBaseInfo();
    this.getPowerList();
    this.getRoleList();
  },
  computed: {
    showCreate() {
      return checkKeycloakAttr('jtc-role-manage');
    },
    currentPowerFilteredData() {
      return this.powerList.filter((x) => {
        let temp = x.name.includes(this.powerPostData.searchCondition) || x.desc.includes(this.powerPostData.searchCondition);
        if (this.powerTableFilter.length && temp) {
          temp = this.powerTableFilter.includes(x.type);
        }
        return temp;
      });
    },
    currentPowerTableData() {
      return this.currentPowerFilteredData.slice(this.powerPostData.pageSize * (this.powerPostData.pageNum - 1), this.powerPostData.pageSize * this.powerPostData.pageNum);
    },
    currentPowerTableTotal() {
      return this.currentPowerFilteredData.length;
    },
  },
  methods: {
    tableAttr,
    showNoDataText,
    async getPermissionTypeList() {
      const res = await Role.getPermissionTypeList();
      if (res.code === 0) {
        const columns: any = [...this.powerColumns];
        columns.find((x) => x.key === 'type').filters = res.data.map((x: any) => {
          x.text = x.desc;
          x.value = x.name;
          return x;
        });
        this.powerColumns = columns;
      }
    },
    getPermissionType(record) {
      return PERMISSION_TYPE[record.type];
    },
    checkData(val) {
      return val ? val : '--';
    },
    async getBaseInfo() {
      const data = {
        platformCode: this.platformCode,
        roleId: this.roleId,
      };
      const res = await Role.getRoleBasicInfo({ data });
      if (res.code === 0) {
        this.baseInfo = { ...res.data, platformName: this.platformName };
      }
    },
    handlerPowerSearch(this: any, e) {
      this.powerPostData.searchCondition = e.target.value;
      this.powerChangePageNum(1);
    },
    powerTableChange(pagination, filters, sorter) {
      this.powerTableFilter = filters.type ? filters.type : [];
      this.powerChangePageNum(1);
    },
    async getPowerList() {
      this.powerLoading = true;
      const data = {
        platformCode: this.platformCode,
        roleId: this.roleId,
      };
      const res = await Role.getPermissionInfo({ data });
      this.powerLoading = false;
      if (res.code === 0) {
        this.powerList = res.data;
        this.powerTotal = res.data.length;
      }
    },
    powerChangePageNum(pageNum) {
      this.powerPostData.pageNum = pageNum;
    },
    powerChangePageSize(pageSize) {
      this.powerPostData.pageSize = pageSize;
      this.powerChangePageNum(1);
    },
    roleEditBtnDisabled(this: any) {
      return this.baseInfo.type === ROLE_TYPE.COMMON_USER || this.baseInfo.type === ROLE_TYPE.ADMIN;
    },
    goRoleEdit(this: any) {
      this.$router.push({
        path: '/role-management/roleControl-roleEdit',
        query: {
          flag: '1',
          platform: this.platformName,
          id: this.roleId,
          platformCode: this.platformCode,
        },
      });
    },
    handlerRoleSearch(this: any, e) {
      this.roleLoading = true;
      this.rolePostData.searchKey = e.target.value;
      this.roleSearch(this);
    },
    roleSearch: _.debounce(function (this: any) {
      this.roleChangePageNum(1);
    }, 500),
    disposePhone(phoneNum) {
      let phoneHead = phoneNum.slice(0, 3);
      let phoneFooter = phoneNum.slice(7);
      return `${phoneHead}****${phoneFooter}`;
    },
    async getRoleList() {
      this.roleLoading = true;

      const data = {
        platformCode: this.platformCode,
        roleId: this.roleId,
        ...this.rolePostData,
      };
      const res = await Role.getRoleUserList({ data });
      this.roleLoading = false;
      if (res.code === 0) {
        this.roleList = res.data.data;
        this.roleTotal = res.data.total;
      }
    },
    roleChangePageNum(pageNum) {
      this.rolePostData.pageNum = pageNum;
      this.getRoleList();
    },
    roleChangePageSize(pageSize) {
      this.rolePostData.pageSize = pageSize;
      this.roleChangePageNum(1);
    },
    goUserEdit(this: any) {
      this.$router.push({
        path: '/role-management/roleControl-roleUserEdit',
        query: {
          ...this.$route.query,
        },
      });
    },
  },
});
</script>
<style lang="less" scoped>
.user-details {
  .nav {
    height: 58px;
    line-height: 58px;
    padding-left: 20px;
    background: #ffffff;
    font-weight: 500;
    color: #121f2c;
  }
  .main {
    min-width: 1268px;
    padding: 20px;
    margin: 20px;
    background: #ffffff;
    position: relative;
    .top-title {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      .title1 {
        font-weight: 500;
        color: #121f2c;
        font-size: 16px;
      }
      /deep/ .ant-btn-background-ghost {
        margin-left: 15px;
      }
    }
    .base-info {
      margin-bottom: 20px;
      padding-bottom: 20px;
      .info-row {
        padding: 17px 0 16px 58px;
        border-bottom: 1px solid #efefef;
      }

      .ant-row {
        .ant-col {
          margin-bottom: 24px;
          span:nth-of-type(1) {
            display: inline-block;
            width: 70px;
            text-align: right;
            color: #606972;
          }
          span:nth-of-type(2) {
            width: 70px;
            text-align: right;
            color: #121f2c;
          }
        }
        .organization {
          span {
            vertical-align: top;
          }
          span:nth-of-type(2) {
            text-align: left;
            display: inline-block;
            // width: 1050px;
            width: 78%;
          }
        }
        .headPortrait {
          span {
            vertical-align: top;
          }
          img {
            width: 120px;
            height: 120px;
            position: absolute;
            top: 0;
            left: 78px;
            border-radius: 50%;
            border: none;
          }
        }
      }
    }
    .powerNum {
      position: absolute;
      top: 0;
      left: 75px;
    }
    .search-box {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      .role-search {
        width: 250px;
        /deep/.anticon svg {
          width: 18px;
          height: 18px;
        }
      }
    }
    .power-info {
      position: relative;
      margin-bottom: 20px;
    }
    .info-table {
      margin: 20px 0 20px 0;
      .table-empty {
        font-size: 18px;
        font-weight: bold;
        position: relative;
        top: -120px;
      }
    }
    /deep/.ant-table {
      font-size: 12px;
      th {
        background: #edf1f3;
        color: #121f2c;
        padding: 10px;
      }
      td {
        color: #606972;
        padding: 10px;
      }
    }
    .user-list {
      position: relative;
    }
  }
}
.form-tip {
  width: 312px;
  font-size: 12px;
  color: #bbbbbb;
  margin-top: 10px;
  white-space: nowrap;
}
/deep/.ant-form-item-label {
  min-width: 110px !important;
}
/deep/#form_item_isForced {
  margin-top: 4px;
}
/deep/.ant-form-item-explain {
  font-size: 12px;
}
/deep/.ant-breadcrumb-link {
  font-weight: 400;
}
:deep(.ant-table-wrapper:not(.auto-height-table)) .ant-spin-nested-loading {
  min-height: 438px;
}
</style>
