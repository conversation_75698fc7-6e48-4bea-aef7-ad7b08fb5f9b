import store from '.';
import { getLocalStorage, setLocalStorage } from '@/utils/index';
const mutations = {
  UPDATE_RETRESHTOKEN(state, token) {
    state.refreshToken = token;
  },
  UPDATE_USERINFO(state, data: any): void {
    state.userInfo = data;
    store.commit('UPDATE_TOOLTIPSTORAGE');
  },
  UPDATE_TOOLTIPVISIBLE(state, bol: boolean): void {
    state.importTooltipVisible = bol;
    setLocalStorage('TOOLTIPVISIBLE', {
      userName: state.userInfo.userName,
      visible: bol,
    });
  },
  // 需要将提示框的显示状态存储在storage，保持在下次页面刷新的时候同步
  UPDATE_TOOLTIPSTORAGE(state): void {
    const tooltipStorage: any = getLocalStorage('TOOLTIPVISIBLE');
    if (tooltipStorage) {
      if (tooltipStorage.userName === state.userInfo.userName) {
        const visible = tooltipStorage.visible;
        store.commit('UPDATE_TOOLTIPVISIBLE', visible);
      }
    }
  },
  UPDATE_TOOLTIPFOLD(state): void {
    state.importTooltipFold = !state.importTooltipFold;
  },
  UPDATE_SIDEMENUAUTH(state, menuAuths: []): void {
    state.sideMenuAuth = menuAuths;
  },
  UPDATE_SIDEMENU(state, menus: []): void {
    state.sideMenu = menus;
  },
  UPDATE_SPINNING(state, spinning: boolean): void {
    state.spinning = spinning;
  },
  UPDATE_LAYOUTCONFIG(state, layoutConfig: any): void {
    state.layoutConfig = layoutConfig;
  },
  UPDATE_ROUTEVIEWREFRESHKEY(state): void {
    state.routeViewRefreshKey = new Date().toString();
  },

  updateAppInfo(state, info): void {
    state.appInfo = info;
  },
  // 开放能力第三步 同步到vuex
  updateAppInfoStep3(state, payload): void {
    state.appInfo.intro = payload;
  },
  // 开放能力step1
  UPDATE_APPINFO_BASEINFO(state, payload): void {
    state.appInfo.baseInfo = payload;
  },
  // 开放能力step2
  UPDATE_APPINFO_METERIAL(state, payload): void {
    state.appInfo.material = payload;
  },
  // 开放能力第四步 同步到vuex
  updateAppInfoStep4(state, payload): void {
    state.appInfo.techFeatures = payload;
  },

  updateAppInfoStep4techFeatures(state, payload): void {
    state.appInfo.techFeatures.funcIntros = payload;
  },

  // 打开模态框，传递路径
  openModal(state, path): void {
    state.modalVisible = true;
    state.toPath = path;
  },
  // 打开中途保存草稿的开关
  openSaveDraft(state): void {
    state.isSaveDraft = true;
  },
  // 关闭中途保存草稿的开关
  closeSaveDraft(state): void {
    state.isSaveDraft = false;
    state.toPath = '';
    state.modalVisible = false;
  },
  // 更新appinfo 中的id
  saveAppInfoId(state, id): void {
    state.appInfo.id = id;
  },
  // 获取能力信息id: store 与 本地
  // getAppInfoId(state) {
  //   return state.appInfoId || JSON.parse(localStorage.getItem('appInfoId') as string) || null;
  // },
  // 清空能力信息id: store 与 本地
  // clearAppInfoId(state): void {
  //   state.appInfoId = null;
  //   localStorage.removeItem('appInfoId');
  // },
  // 能力信息复位
  resetAppInfo(state): void {
    state.appInfo = { ...state.appInfoBackUp };
  },
  // 开启全局loading
  openGlobalLoading(state): void {
    state.showGlobalLoading = true;
  },
  // 关闭全局loading
  closeGlobalLoading(state): void {
    state.showGlobalLoading = false;
  },
  //开放能力第五步
  capabilityIntro(state, capabilityIntro: string): void {
    state.appInfo.recCard.capabilityIntro = capabilityIntro;
  },
  //预存数据
  capabilityIntroArr(state, capabilityIntroArr: any): void {
    state.capabilityIntroArr = capabilityIntroArr;
  },
  theFirstFuncIntro(state, theFirstFuncIntro: string): void {
    state.appInfo.recCard.theFirstFuncIntro = theFirstFuncIntro;
  },
  theSecondFuncIntro(state, theSecondFuncIntro: string): void {
    state.appInfo.recCard.theSecondFuncIntro = theSecondFuncIntro;
  },
  technicalDocumentsContent(state, content: string): void {
    state.appInfo.techDoc.content = content;
  },
  technicalDocumentsNeedTechDoc(state, needTechDoc: string): void {
    state.appInfo.techDoc.needTechDoc = needTechDoc;
  },

  // 更新选择的媒体列表的文件
  UPDATE_DEFAULT_MEDIA_LIST(state, payload): void {
    state.defaultMediaUrlList = payload;
  },
  // 更新token
  UPDATE_REFRESH_TOKEN(state, payload): void {
    state.refreshToken = payload;
  },
};

export default mutations;
