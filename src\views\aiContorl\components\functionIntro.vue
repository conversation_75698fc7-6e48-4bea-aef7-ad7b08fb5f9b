<template>
  <div class="function">
    <div class="function-title">功能介绍及使用场景</div>

    <div class="function-content">
      <div class="function-content-left">
        <div class="function-intro">
          <div class="function-intro-title">功能介绍</div>
          <div class="function-intro-tips">
            <jt-icon type="iconinfo" class="iconfont-info"></jt-icon>
            <div class="info-text">建议修改示意图标，如不修改将在页面中显示如下</div>
          </div>

          <div class="function-intro-content">
            <funcIntro ref="functionRef1" :is-edit="props.isEdit" :edit-data="functionData1" :font-number="60" :icon-list="props.iconList" :headline="functionText" :num="'一'" @valid-change="functionValid1"></funcIntro>
            <funcIntro ref="functionRef2" :is-edit="props.isEdit" :edit-data="functionData2" :font-number="60" :icon-list="props.iconList" :headline="functionText" :num="'二'" @valid-change="functionValid2"></funcIntro>
            <funcIntro ref="functionRef3" :is-edit="props.isEdit" :edit-data="functionData3" :font-number="60" :icon-list="props.iconList" :headline="functionText" :num="'三'" @valid-change="functionValid3"></funcIntro>
            <funcIntro ref="functionRef4" :is-edit="props.isEdit" :edit-data="functionData4" :font-number="60" :icon-list="props.iconList" :headline="functionText" :num="'四'" @valid-change="functionValid4" :need-valid="false"></funcIntro>
          </div>
        </div>

        <div class="use-sence">
          <div class="use-sence-title">使用场景</div>
          <div class="use-sence-content">
            <useSence ref="useSenceRef1" :is-edit="props.isEdit" :edit-data="useSenceData1" :font-number="100" :headline="useSenceText" :num="'一'" :show-icons="false" :is-textarea-high="true" @valid-change="useSenceValid1"></useSence>
            <useSence ref="useSenceRef2" :is-edit="props.isEdit" :edit-data="useSenceData2" :font-number="100" :headline="useSenceText" :num="'二'" :show-icons="false" :is-textarea-high="true" @valid-change="useSenceValid2"></useSence>
            <useSence ref="useSenceRef3" :is-edit="props.isEdit" :edit-data="useSenceData3" :font-number="100" :headline="useSenceText" :num="'三'" :show-icons="false" :is-textarea-high="true" @valid-change="useSenceValid3"></useSence>
            <useSence ref="useSenceRef4" :is-edit="props.isEdit" :edit-data="useSenceData4" :font-number="100" :headline="useSenceText" :num="'四'" :show-icons="false" :is-textarea-high="true" @valid-change="useSenceValid4" :need-valid="false"></useSence>
          </div>
          <a-form :layout="'vertical'" autocomplete="off" class="sence-form">
            <a-form-item
              class="sence"
              :label="sencePic"
              :rules="[
                {
                  required: true,
                },
              ]"
            >
              <a-radio :checked="true" disabled>
                <div class="sence-box">
                  <img class="sence-box-img" :src="senceList.value" alt="" />
                </div>
              </a-radio>
            </a-form-item>
          </a-form>
        </div>

        <div class="btn-groups">
          <div class="pre" @click="toPre">
            <jt-icon type="iconleft" />
          </div>
          <a-button class="next" type="primary" @click="toNext">下一步</a-button>
          <a-button class="save" type="default" @click="saveDraft">保存</a-button>
        </div>
      </div>
      <div class="pre-view">
        <div class="pre-view-title">入驻能力页示意</div>
        <div class="pre-view-pic">
          <img class="pre-view-img" :src="preUrl" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, defineExpose, ref, reactive, computed, watch } from 'vue';
import request, { requestBlob } from '@/request';
import { useStore } from 'vuex';
import funcIntro from './components/funcIntro.vue';
import useSence from './components/funcIntro.vue';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  appInfo: {
    type: Object,
    default() {
      return {};
    },
  },
  iconList: {
    type: Array,
    default() {
      return [];
    },
  },
  capacityTypeMap: {
    type: Array,
    default() {
      return [];
    },
  },
});
// const preUrl = require('@/assets/images/aiControl/step3.png');
const preUrl: any = ref('');
const sencePic = ref('');
// 能力类别配图
const senceList = reactive({
  key: '',
  value: '',
});
const store = useStore();
const emit = defineEmits(['toPre', 'toNext', 'saveDraft']);

const intro: any = {
  funcIntros: [],
  useCases: [],
};

const capacityType = computed(() => {
  return store.state.appInfo.baseInfo.type;
});
// 监听能力类别变化
watch(
  capacityType,
  (newValue) => {
    if (newValue !== '') {
      // 获取使用场景配图
      const capacityArray = props.capacityTypeMap.filter((item: any) => item.value == newValue);
      if (capacityArray.length > 0) {
        const label = (capacityArray[0] as any).label;
        sencePic.value = `场景配图：能力类别-${label}`;
      }
      request('/aiipweb/om-service/os/getPictureList', {
        method: 'GET',
        data: {
          category: 2,
          capabilityCategory: newValue,
        },
      }).then((res: any) => {
        const tempList = res.body.map((item) => {
          return {
            key: item,
            value: `/aiipweb/om-service/os/getObject?category=2&object=${item}&capabilityCategory=${newValue}`,
          };
        });
        const requests = tempList.map((item) => {
          return requestBlob(item.value, { method: 'GET' });
        });
        Promise.all(requests).then((result: any) => {
          for (let i = 0; i < result.length; i++) {
            senceList.key = tempList[i].key;
            senceList.value = result[i];
          }
        });
      });
      // 获取 右侧的示例图
      const url = `/aiipweb/om-service/os/getObject?category=5&object=step3.png&capabilityCategory=${newValue}`;
      requestBlob(url, { method: 'GET' }).then((res) => {
        preUrl.value = res;
      });
    }
  },
  {
    immediate: true,
  }
);
// 处理 编辑回显的逻辑
const functionData1 = ref({});
const functionData2 = ref({});
const functionData3 = ref({});
const functionData4 = ref({});
const useSenceData1 = ref({});
const useSenceData2 = ref({});
const useSenceData3 = ref({});
const useSenceData4 = ref({});
watch(
  () => props.isEdit,
  (newValue) => {
    if (newValue) {
      if (props.appInfo.intro?.funcIntros?.length > 1) {
        functionData1.value = props.appInfo.intro.funcIntros[0];
        functionData2.value = props.appInfo.intro.funcIntros[1];
        functionData3.value = props.appInfo.intro.funcIntros[2];
        functionData4.value = props.appInfo.intro.funcIntros[3];
      } else {
        functionData1.value = props.appInfo.intro.funcIntros[0];
        functionData2.value = props.appInfo.intro.funcIntros[0];
        functionData3.value = props.appInfo.intro.funcIntros[0];
        functionData4.value = props.appInfo.intro.funcIntros[0];
      }

      if (props.appInfo.intro?.useCases?.length > 1) {
        useSenceData1.value = props.appInfo.intro.useCases[0];
        useSenceData2.value = props.appInfo.intro.useCases[1];
        useSenceData3.value = props.appInfo.intro.useCases[2];
        useSenceData4.value = props.appInfo.intro.useCases[3];
      } else {
        useSenceData1.value = props.appInfo.intro.useCases[0];
        useSenceData2.value = props.appInfo.intro.useCases[0];
        useSenceData3.value = props.appInfo.intro.useCases[0];
        useSenceData4.value = props.appInfo.intro.useCases[0];
      }
    }
  },
  {
    immediate: true,
  }
);

// 文案
const functionText = {
  title: '功能',
  subtitle: '功能介绍',
  content: '功能简介',
  icon: '功能图标',
};
// 各组件校验状态
const functionRef1 = ref();
const func1 = ref<boolean>(false);
const functionValid1 = (val) => {
  func1.value = val;
};
const functionRef2 = ref();
const func2 = ref<boolean>(false);
const functionValid2 = (val) => {
  func2.value = val;
};
const functionRef3 = ref();
const func3 = ref<boolean>(false);
const functionValid3 = (val) => {
  func3.value = val;
};
const functionRef4 = ref();
const func4 = ref<boolean>(false);
const functionValid4 = (val) => {
  func4.value = val;
};

const useSenceText = {
  title: '场景',
  subtitle: '场景名称',
  content: '场景简介',
};
const useSenceRef1 = ref();
const use1 = ref<boolean>(false);
const useSenceValid1 = (val) => {
  use1.value = val;
};
const useSenceRef2 = ref();
const use2 = ref<boolean>(false);
const useSenceValid2 = (val) => {
  use2.value = val;
};
const useSenceRef3 = ref();
const use3 = ref<boolean>(false);
const useSenceValid3 = (val) => {
  use3.value = val;
};
const useSenceRef4 = ref();
const use4 = ref<boolean>(false);
const useSenceValid4 = (val) => {
  use4.value = val;
};

const valid = computed(() => {
  return func1.value && func2.value && func3.value && func4.value && use1.value && use2.value && use3.value && use4.value;
});
// 同步到vuex
const updateToStore = () => {
  const functionItem = {
    intro: '',
    description: '',
    img: '',
  };

  functionItem.intro = functionRef1.value.formData.intro;
  functionItem.description = functionRef1.value.formData.desc;
  functionItem.img = (props.iconList as any).filter((item: any) => item.value === functionRef1.value.formData.icon)[0].key;
  intro.funcIntros[0] = { ...functionItem };

  functionItem.intro = functionRef2.value.formData.intro;
  functionItem.description = functionRef2.value.formData.desc;
  functionItem.img = (props.iconList as any).filter((item: any) => item.value === functionRef2.value.formData.icon)[0].key;
  intro.funcIntros[1] = { ...functionItem };

  functionItem.intro = functionRef3.value.formData.intro;
  functionItem.description = functionRef3.value.formData.desc;
  functionItem.img = (props.iconList as any).filter((item: any) => item.value === functionRef3.value.formData.icon)[0].key;
  intro.funcIntros[2] = { ...functionItem };

  functionItem.intro = functionRef4.value.formData.intro;
  functionItem.description = functionRef4.value.formData.desc;
  functionItem.img = (props.iconList as any).filter((item: any) => item.value === functionRef4.value.formData.icon)[0].key;
  intro.funcIntros[3] = { ...functionItem };

  const useSenceItem = {
    name: '',
    intro: '',
    img: '',
  };
  useSenceItem.name = useSenceRef1.value.formData.intro;
  useSenceItem.intro = useSenceRef1.value.formData.desc;
  useSenceItem.img = senceList.key as string;
  intro.useCases[0] = { ...useSenceItem };

  useSenceItem.name = useSenceRef2.value.formData.intro;
  useSenceItem.intro = useSenceRef2.value.formData.desc;
  useSenceItem.img = senceList.key as string;
  intro.useCases[1] = { ...useSenceItem };

  useSenceItem.name = useSenceRef3.value.formData.intro;
  useSenceItem.intro = useSenceRef3.value.formData.desc;
  useSenceItem.img = senceList.key as string;
  intro.useCases[2] = { ...useSenceItem };

  useSenceItem.name = useSenceRef4.value.formData.intro;
  useSenceItem.intro = useSenceRef4.value.formData.desc;
  useSenceItem.img = senceList.key as string;
  intro.useCases[3] = { ...useSenceItem };

  store.commit('updateAppInfoStep3', intro);
};

const toPre = () => {
  updateToStore();
  emit('toPre');
};
const toNext = () => {
  if (valid.value) {
    updateToStore();
    emit('toNext');
  } else {
    functionRef1.value.formValid();
    functionRef2.value.formValid();
    functionRef3.value.formValid();
    functionRef4.value.formValid();
    useSenceRef1.value.formValid();
    useSenceRef2.value.formValid();
    useSenceRef3.value.formValid();
    useSenceRef4.value.formValid();
    return;
  }
};
const saveDraft = () => {
  updateToStore();
  emit('saveDraft');
};

defineExpose({
  updateToStore,
});
</script>

<style lang="less" scoped>
.function {
  margin-top: 12px;
  background-color: #fff;
  padding: 28px 20px 104px 20px;
  border-radius: 2px;
  &-title {
    margin-bottom: 24px;
    height: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #121f2c;
    line-height: 24px;
  }
  &-content {
    display: flex;
    &-left {
      flex: 68;
      padding-right: 20px;
      .function-intro {
        margin-bottom: 64px;
        &-title {
          margin-bottom: 16px;
          height: 16px;
          font-size: 14px;
          font-weight: 500;
          color: #121f2c;
          line-height: 16px;
          border-left: 4px solid #0082ff;
          padding-left: 8px;
        }
        &-tips {
          margin-bottom: 24px;
          height: 34px;
          background: #edf7ff;
          border-radius: 2px;
          border: 1px solid #b0d5ff;
          padding-left: 17px;
          display: flex;
          align-items: center;
          .iconfont-info {
            width: 14px;
            height: 14px;
            background: #5a99f9;
            margin-right: 9px;
            color: #fff;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            font-size: 12px;
          }
          .info-text {
            font-size: 12px;
            font-weight: 400;
            color: #666666;
          }
        }
        &-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
      .use-sence {
        margin-bottom: 48px;
        &-title {
          margin-bottom: 16px;
          height: 16px;
          font-size: 14px;
          font-weight: 500;
          color: #121f2c;
          line-height: 16px;
          border-left: 4px solid #0082ff;
          padding-left: 8px;
        }
        &-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 24px;
        }
        .sence-form {
          .sence {
            /deep/ .ant-radio-wrapper {
              align-items: start;
            }
            &-box {
              width: 192px;
              height: 100px;
              border: 1px solid #d6d9db;
              padding: 8px;
              &-img {
                width: 100%;
                height: 100%;
              }
            }
          }
        }
      }
      .btn-groups {
        display: flex;
        align-items: center;
        .pre {
          margin-right: 8px;
          width: 32px;
          height: 32px;
          background: #ffffff;
          border-radius: 2px;
          border: 1px solid #d6d9db;
          text-align: center;
          line-height: 30px;
          color: #7f828f;
          font-size: 16px;
          cursor: pointer;
          &:hover {
            color: #0082ff;
            border-color: #0082ff;
          }
        }
        .next {
          margin-right: 8px;
          width: 120px;
          height: 32px;
          border-radius: 2px;
        }
        .save {
          width: 88px;
          height: 32px;
          border-radius: 2px;
        }
      }
    }
    .pre-view {
      flex: 32;
      border-left: 1px solid #efefef;
      padding-right: 20px;
      &-title {
        margin-left: 9px;
        padding-left: 8px;
        height: 16px;
        line-height: 16px;
        border-left: 4px solid #0082ff;
        font-size: 14px;
        font-weight: 500;
        color: #121f2c;
      }
      &-pic {
        margin-top: 16px;
        margin-left: 20px;
        width: 100%;
        .pre-view-img {
          width: 100%;
        }
      }
    }
  }
}
</style>
