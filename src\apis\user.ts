/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-17 14:19:09
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2023-03-08 11:08:45
 */
import request from '@/request';

const USER_API = {
  summary: '/opera/web/user/summary',
  count: '/web/admin/um/v1/user/count',
  countLevel: '/web/admin/um/v1/group/count-by-level',
  commonOverview: '/opera/web/platform/common_overview',
  commonSummary: '/opera/web/user/common_summary',
};

const User = {
  // 用户统计旧接口
  getSummary: (params: any = {}) => {
    const { type, data } = params;
    return request(USER_API.summary, {
      method: type || 'GET',
      data,
    });
  },
  // keyloak用户：查询用户总人数
  getWebUserCount: (params: any = {}) => {
    const { type, data } = params;
    return request(USER_API.count, {
      method: type || 'GET',
      data,
    });
  },
  // keycloak：查询各级组织个数
  getCountByLevel: (params: any = {}) => {
    const { type, data } = params;
    return request(USER_API.countLevel, {
      method: type || 'GET',
      data,
    });
  },
  // 获取统一平台总览数据
  getCommonOverview: (params: any = {}) => {
    const { type, data } = params;
    return request(USER_API.commonOverview, {
      method: type || 'GET',
      data,
    });
  },
  // 获取统一平台用户看板数据
  getCommonSummary: (params: any = {}) => {
    const { type, data } = params;
    return request(USER_API.commonSummary, {
      method: type || 'GET',
      data,
    });
  },
};
export default User;
