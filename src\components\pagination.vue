<template>
  <div class="pagination" v-if="total">
    <a-space size="large">
      <span>共 {{ total }} 条</span>
      <span v-if="showSizeOptions">
        每页显示
        <a-select :value="pageSize" style="width: 60px" :options="pageSizeOptions.map((item) => ({ label: item, value: item }))" @change="changePageSize"></a-select>
        条
      </span>
    </a-space>
    <a-pagination @change="changePageNum" :pageSize="pageSize" show-quick-jumper :current="pageNum" :total="total" :showSizeChanger="false" />
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import PropTypes, { withUndefined } from '@/lib/util/vue-types/index';

export const PaginationProps = () => ({
  total: PropTypes.number.def(0),
  pageNum: PropTypes.number.def(1),
  pageSize: PropTypes.number.def(10),
  pageSizeOptions: PropTypes.array.def(['5', '10', '15', '20']),
  showQuickJumper: withUndefined(PropTypes.oneOfType([PropTypes.looseBool, PropTypes.object])),
  showSizeOptions: PropTypes.looseBool.def(true),
});

export default defineComponent({
  props: { ...PaginationProps() },
  emits: ['change', 'update:pageNum', 'update:pageSize', 'changePageSize', 'changePageNum'],
  setup(props, context) {
    const changePageSize = (size) => {
      context.emit('update:pageSize', size);
      context.emit('changePageSize', size);
    };
    const changePageNum = (pageNum) => {
      context.emit('update:pageNum', pageNum);
      context.emit('changePageNum', pageNum);
    };
    return {
      changePageSize,
      changePageNum,
    };
  },
});
</script>
<style lang="less" scoped>
.pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}
</style>
