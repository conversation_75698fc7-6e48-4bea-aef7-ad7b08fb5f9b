import Role from '../../../apis/role';

/**
 * 一个简单的 find 通过find 找到编制中所属组织下的编辑字段
 * @param {Array} data - 所属组织下的所有内容
 * @param {Any | string} filterGroup  - 当前需要检索的字段内容
 * @param {String} key  - 当前需要绑定的关键属性 默认为 label
 */
export function labelForValue(data, filterGroup: any, key = 'label') {
  return data.find((item) => item[key] === filterGroup);
}

// 获取角色信息表头
export async function getClientList() {
  const getdata = [];
  const res = await Role.getPlatformList();
  if (res.msg == 'OK' && res.data != null) {
    for (let i = 0; i < res.data.length; i++) {
      getdata.push(res.data[i]);
    }
  }
  return getdata;
}
