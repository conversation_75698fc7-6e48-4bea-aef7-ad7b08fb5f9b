<template>
  <micro-components :appName="appName" moduleName="common-feedback" :data="{ requester, config }" />
</template>

<script>
import { getEnvConfig } from '@/config';
import { GET, POST } from '@/request';
const shoowFeedbackMsg = {
  0: false,
  1: true,
};
export default {
  name: 'common-feedback-remote',
  props: {
    size: {
      // 默认用小size, 传large用大的size
      type: String,
    },
    appName: {
      type: String,
    },
  },
  data() {
    return {
      requester: { GET, POST },
      config: {
        showDemand: getEnvConfig('SHOW_DEMAND_ICON') === '1',
        showRequirment: getEnvConfig('SHOW_REQUIRMENT_ICON') === '1',
        contactEmail: getEnvConfig('CONTACT_EMAIL'),
        size: this.size,
      },
    };
  },
  mounted() {
    this.getFeedbackMsg();
    this.getHelpcenterUrl();
  },
  methods: {
    getFeedbackMsg() {
      GET('/ticket/web/getMsg').then((res) => {
        if (res.state === 'OK') {
          this.config.showTextTip = shoowFeedbackMsg[Number(res.body)];
        }
      });
    },
    getHelpcenterUrl() {
      GET('/ticket/web/getCategorysAndCenterUrl', { platform: '' }).then((res) => {
        if (res.state === 'OK') {
          this.config.helpcenterUrl = res.body.centerurl;
        }
      });
    },
  },
};
</script>
