import request from '@/request';
import { formatSideMenu } from '@/config/menu';
const actions = {
  getUserInfo({ commit }: { commit: any }): void {
    request('/web/um/v1/user/brief').then((res: any) => {
      commit('UPDATE_USERINFO', res.data);
    });
  },
  initSideMenu({ commit }: { commit: any }) {
    return new Promise((resolve, reject) => {
      request('/web/settings/nav/getJtPlat').then((res: any) => {
        if (res.code === 0) {
          const { sideMenuAuth, sideMenuData } = formatSideMenu(res.data.body);
          commit('UPDATE_SIDEMENUAUTH', sideMenuAuth);
          commit('UPDATE_SIDEMENU', sideMenuData);
          commit('UPDATE_SPINNING', false);
          resolve(sideMenuAuth);
        }
      });
    });
  },
  initLayoutConfig({ commit }: { commit: any }) {
    request('/web/settings/lay/getLayout', {
      data: { projectFlag: '3' },
    }).then((res: any) => {
      if (res.code === 0) {
        document.title = res.data.body.subtab;
        commit('UPDATE_LAYOUTCONFIG', res.data.body);
      }
    });
  },
};

export default actions;
