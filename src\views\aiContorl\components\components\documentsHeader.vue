<template>
  <a-layout-header class="jt-header" :class="isDemo ? 'jt-header-demo' : ''">
    <div class="jt-header-left">
      <img src="@/assets/images/chinamobile-jiutian.png" alt="" />
      <span class="jt-header-left-title">九天人工智能平台</span>
      <div class="jt-header-left-center">
        <ul class="jt-header-left-center-ul" style="margin-bottom: 0">
          <li class="defaultStyle">开放能力</li>
          <li>开发平台</li>
          <li>解决方案</li>
          <li>生态合作</li>
          <li>关于我们</li>
        </ul>
      </div>
    </div>
    <div class="jt-header-right">
      <ul style="margin-bottom: 0" class="jt-header-right-ul">
        <li>消息中心</li>
        <li>帮助中心</li>
      </ul>
      <div class="right" style="height: 50px; display: flex; align-items: center">
        <a-space>
          <a-dropdown v-if="userInfo.userId">
            <div class="user-box">
              <img class="avatar" :src="userInfo.image || defaultAvatar" alt="" />
              <p :title="userInfo.userName" style="margin: 0">{{ userInfo.userName }}</p>
            </div>
          </a-dropdown>
          <ul v-else class="header-menu">
            <li>
              <a @click="gotoLogin">登录</a>
            </li>
          </ul>
        </a-space>
      </div>
    </div>
  </a-layout-header>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { useStore } from 'vuex';
import { keycloak } from '../../../../keycloak';
import defaultAvatar from '@/assets/images/avatar_big.png';
export default defineComponent({
  props: {
    collapsed: Boolean,
    isDemo: {
      type: Boolean,
      default: false,
    },
  },
  setup() {
    const store = useStore();
    return {
      userInfo: computed(() => store.state.userInfo),
    };
  },
  data() {
    return {
      defaultAvatar,
    };
  },
  methods: {
    gotoLogin() {
      const loginUrl = keycloak.createLoginUrl();
      window.location.replace(loginUrl);
    },
  },
});
</script>

<style lang="less" scoped>
.jt-header {
  background: #ffffff;
  box-shadow: 0px 1px 10px 0px rgba(18, 31, 44, 0.08);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: space-between;
  .jt-header-left {
    display: flex;
    align-items: center;
    img {
      height: 30px;
      cursor: pointer;
    }
    &-title {
      margin-left: 8px;
      margin-right: 48px;
      font-size: 16px;
      font-weight: 600;
      color: #121f2c;
      cursor: pointer;
    }
    &-center {
      &-ul {
        width: 420px;
        display: flex;
        li {
          width: 88px;
          height: 61px;
          font-size: 14px;
          font-weight: 600;
          color: #333333;
          display: flex;
          justify-content: center;
          border-bottom: 2px solid transparent;
        }
        .defaultStyle {
          color: #00b3cc;
          border-color: #00b3cc;
        }
      }
    }
  }
  &-right {
    display: flex;
    justify-content: center;
    align-items: center;
    &-ul {
      display: flex;
      width: 200px;
      li {
        width: 200px;
        font-size: 14px;
        font-weight: 600;
        color: #606972;
      }
    }
  }
}
.jt-header-demo {
  position: absolute;
  top: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.15);
  border-bottom: 1px solid rgba(18, 31, 44, 0.08);
  backdrop-filter: blur(7px);
  height: 60px;
  padding-left: 24px;
  padding-right: 0px;
  z-index: 2014; // 会与其他z-index有冲突，故使用2014
  transition: 0.3s all ease;
}
.user-box {
  display: flex;
  align-items: center;
  cursor: pointer;
  .avatar {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    border-radius: 50%;
  }
  p {
    color: #555555;
    font-size: 12px;
    line-height: 20px;
    max-width: 85px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
