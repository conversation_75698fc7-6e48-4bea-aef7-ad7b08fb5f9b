<template>
  <div id="container" :class="customClass" :style="customStyle">
    <slot></slot>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    customClass: String, // 自定义类名
    customStyle: Object, // 自定义行内样式
  },
  setup(props) {
    return {};
  },
});
</script>
<style lang="less" scoped>
#container {
  padding: 20px;
  margin-bottom: 20px;
  background-color: white;
}
</style>
