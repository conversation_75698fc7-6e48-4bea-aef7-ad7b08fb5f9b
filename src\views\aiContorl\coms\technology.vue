<template>
  <div class="technology-wrap">
    <img class="tech-img1" src="../../../assets/images/preview/bg-1.png" alt="" />
    <img class="tech-img2" src="../../../assets/images/preview/bg-2.png" alt="" />
    <!-- 标题 -->
    <div class="tech-title">{{ title }}</div>
    <!-- 列表 -->
    <div class="tech-ul">
      <div class="tech-item" v-for="(item, index) in list" :key="index">
        <img class="img" :src="item.img || technologyListImg" alt="" />
        <div class="tech-tep">
          <div class="desc">{{ item.name || technologyListName }}</div>
          <div class="text">{{ item.intro || technologyListIntro }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'TechnologyList',
  props: {
    //  标题 技术特色
    title: {
      type: String,
      default: '',
    },
    // 展示项数组
    list: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      listData: [],
      technologyListImg: require('@/assets/images/preview/<EMAIL>'),
      technologyListName: '技术特色',
      technologyListIntro: '这是一段特色简介这是一段特色简介这是一段特色简介',
    };
  },
  // created() {
  //   this.$nextTick(() => {
  //     console.log(this.list, '============');
  //     this.listData = this.list.filter((item) => {
  //       item.intro != '';

  //       // if (item.intro) {
  //       //   this.listData.push(item);
  //       //   console.log(this.listData, '=================');
  //       // }
  //     });
  //     console.log(this.listData, '-------------');
  //   });
  // },
};
</script>
<style lang="less" scoped>
.technology-wrap {
  width: 100%;
  min-width: 1320px;
  background: linear-gradient(88deg, #e2fdff 0%, #eaf8fb 100%);
  background-size: 100% 100%;
  height: 265px;
  text-align: left;
  margin-top: 56px;
  .tech-img1 {
    position: absolute;
  }
  .tech-img2 {
    position: absolute;
    right: 0;
  }
  .tech-title {
    text-align: center;
    font-size: 28px;
    margin-bottom: 10px;
    font-weight: 600;
    color: #121f2c;
    line-height: 40px;
    padding-top: 40px;
    z-index: 999;
  }
  .tech-ul {
    max-width: 1220px;
    margin: 0 auto;
    display: flex;
    text-align: center;
    .tech-item {
      width: 26%;
      display: flex;
      flex: 1;
      .tech-tep {
        display: flex;
        flex-direction: column;
        text-align: left;
        .desc {
          line-height: 20px;
          font-size: 18px;
          font-weight: 600;
          margin-top: 10px;
          margin-bottom: 11px;
          color: #121f2c;
          width: 256px;
          z-index: 999;
        }
        .text {
          font-size: 14px;
          font-weight: 400;
          color: #606972;
          line-height: 22px;
          width: 154px;
          text-align: justify;
          z-index: 999;
          word-wrap: break-word;
        }
      }
      .img {
        width: 88px;
        height: 88px;
      }
    }
  }
}
</style>
