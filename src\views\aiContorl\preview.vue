<template>
  <div class="preview">
    <loading v-if="data.showGlobalLoading"></loading>
    <headerTab></headerTab>
    <bannerTab :details="details" :backGroundTypeVal="data.backGroundTypeName" :sourceName="source"></bannerTab>
    <anchorTab :isDemo="data.isDemo"></anchorTab>
    <demoTab :path="imgName" :isDemo="data.isDemo" id="demo"></demoTab>
    <functionTab title="功能介绍" :list="data.functionList" id="functionTab" :icons="functionIconList"></functionTab>
    <scrollTab title="使用场景" :list="data.userList" id="scrollTab"></scrollTab>
    <technologyTab title="技术特色" :list="data.technologyList" id="technology"></technologyTab>
    <questionTab title="常见问题" :list="data.questionsList" id="questions"></questionTab>
    <recommendTab title="相关推荐" :list="data.recommendList" id="recommend"></recommendTab>
    <footTab></footTab>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue';
import headerTab from './coms/header.vue';
import bannerTab from './coms/banner.vue';
import anchorTab from './coms/anchor.vue';
import demoTab from './coms/demo.vue';
import functionTab from './coms/function.vue';
import scrollTab from './coms/scroll.vue';
import technologyTab from './coms/technology.vue';
import questionTab from './coms/question.vue';
import recommendTab from './coms/recommend.vue';
import footTab from './coms/footTab.vue';
import request, { requestBlob } from '@/request';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
const store = useStore();
const details = reactive({
  title: '',
  content: '',
  img: require('../../assets/images/preview/banner.png'),
});
const data = reactive({
  showGlobalLoading: true,
  backGroundTypeVal: '', //背景图
  backGroundTypeName: '',
  backGroundTypeList: [] as any[],
  typeList: { 0: 'IMG', 1: 'VIDEO', 2: 'AUDIO', 3: 'TEXT' },
  format: '', //格式
  isDemo: false,
  material: [],
  type: '',
  functionListIcon: [],
  functionList: [] as any[],
  userList: [] as any[],
  technologyList: [] as any[],
  questionsList: [
    {
      question: '',
      answer: '',
    },
    {
      question: '',
      answer: '',
    },
    {
      question: '',
      answer: '',
    },
    {
      question: '',
      answer: '',
    },
  ] as any[],
  recommendList: [
    { name: '', description: '', img: '' },
    { name: '', description: '', img: '' },
    { name: '', description: '', img: '' },
  ] as any[],
  recommendListFil: [] as any[],
});
const statusTransfer = (value) => {
  return data.typeList[value];
};
const path = ref(''); //功能演示地址
const imgName = ref('');
// 图标库
const functionIconList: any = ref([]);
const route = useRoute();
const source = route.query.listName;
const getData = () => {
  request('/aiipweb/om-service/capability/edit-show', {
    method: 'GET',
    data: {
      name: source,
    },
  }).then((res: any) => {
    data.showGlobalLoading = false;
    details.title = res.body.baseInfo.name;
    data.type = res.body.baseInfo.type;
    data.format = statusTransfer(res.body.material.type);
    data.isDemo = res.body.material.needToShow;
    data.material = res.body.material.materials;
    data.backGroundTypeVal = backGroundType(res.body.baseInfo.backGroundType) || '人脸类';
    data.backGroundTypeName = require('@/assets/images/preview/banner/' + `${data.backGroundTypeVal}` + '.png');
    if (data.format == 'IMG') {
      path.value = data.material.length != 0 ? 'picT.png' : 'picF.png';
    } else if (data.format == 'VIDEO') {
      path.value = data.material.length != 0 ? 'videoT.png' : 'videoF.png';
    } else if (data.format == 'AUDIO') {
      path.value = data.material.length != 0 ? 'audioT.png' : 'audioF.png';
    } else if (data.format == 'TEXT') {
      path.value = data.material.length != 0 ? 'textT.png' : 'textF.png';
    } else {
      path.value = 'picF.png';
    }
    imgName.value = require('@/assets/images/preview/result/' + `${path.value}`);
    details.content = res.body.baseInfo.intro;
    if (res.body.intro.funcIntros.length == 1) {
      console.log(1);
      data.functionList = [
        {
          img: '',
          intro: '',
          description: '',
        },
        {
          img: '',
          intro: '',
          description: '',
        },
        {
          img: '',
          intro: '',
          description: '',
        },
        {
          img: '',
          intro: '',
          description: '',
        },
      ];
    } else {
      data.functionList = res.body.intro.funcIntros;
      functionListIcon(res.body.intro.funcIntros);
    }
    if (res.body.intro.useCases.length == 1) {
      data.userList = [
        {
          img: '',
          intro: '',
          name: '',
        },
        {
          img: '',
          intro: '',
          name: '',
        },
        {
          img: '',
          intro: '',
          name: '',
        },
        {
          img: '',
          intro: '',
          name: '',
        },
      ];
    } else {
      data.userList = res.body.intro.useCases;
      userListIcon(res.body.intro.useCases);
    }
    if (res.body.techFeatures.features.length == 1) {
      data.technologyList = [
        {
          img: '',
          intro: '',
          name: '',
        },
        {
          img: '',
          intro: '',
          name: '',
        },
        {
          img: '',
          intro: '',
          name: '',
        },
        {
          img: '',
          intro: '',
          name: '',
        },
      ];
    } else {
      data.technologyList = res.body.techFeatures.features;
      technologyListIcon(res.body.techFeatures.features);
    }
    if (res.body.techFeatures.questions.length == 1) {
      data.questionsList[0] = res.body.techFeatures.questions[0];
    } else if (res.body.techFeatures.questions.length == 2) {
      data.questionsList[0] = res.body.techFeatures.questions[0];
      data.questionsList[1] = res.body.techFeatures.questions[1];
    } else if (res.body.techFeatures.questions.length == 3) {
      data.questionsList[0] = res.body.techFeatures.questions[0];
      data.questionsList[1] = res.body.techFeatures.questions[1];
      data.questionsList[2] = res.body.techFeatures.questions[2];
    } else {
      data.questionsList = res.body.techFeatures.questions;
    }
    if (res.body.techFeatures.recommendDetails.length == 0) {
      data.recommendList = [
        { name: '', description: '', img: '' },
        { name: '', description: '', img: '' },
        { name: '', description: '', img: '' },
      ];
    } else {
      data.recommendList[0] = res.body.techFeatures.recommendDetails[0] || { name: '', description: '', img: '' };
      data.recommendList[1] = res.body.techFeatures.recommendDetails[1] || { name: '', description: '', img: '' };
      data.recommendList[2] = res.body.techFeatures.recommendDetails[2] || { name: '', description: '', img: '' };
    }
  });
};
getData();
// 请求功能介绍图标库
const functionListIcon = (val) => {
  data.functionList = val;
  const tempList = data.functionList.map((item) => {
    return {
      key: item,
      value: `/aiipweb/om-service/os/getObject?category=1&object=${item.img}`,
    };
  });
  const requests = tempList.map((item) => {
    return requestBlob(item.value, { method: 'GET' });
  });

  Promise.all(requests).then((result: any) => {
    for (let i = 0; i < result.length; i++) {
      const tempData = {
        key: tempList[i].key,
        value: result[i],
      };
      data.functionList[i].img = tempData.value;
    }
  });
};
const userListIcon = (val) => {
  data.userList = val;
  const tempList = data.userList.map((item) => {
    return {
      key: item,
      value: `/aiipweb/om-service/os/getObject?category=6&capabilityCategory=${data.type}&object=${item.img}`,
    };
  });
  const requests = tempList.map((item) => {
    return requestBlob(item.value, { method: 'GET' });
  });

  Promise.all(requests).then((result: any) => {
    for (let i = 0; i < result.length; i++) {
      const tempData = {
        key: tempList[i].key,
        value: result[i],
      };
      data.userList[i].img = tempData.value;
    }
  });
};
const technologyListIcon = (val) => {
  data.technologyList = val;
  const tempList = data.technologyList.map((item) => {
    return {
      key: item,
      value: `/aiipweb/om-service/os/getObject?category=0&object=${item.img}`,
    };
  });
  const requestsA = tempList.map((item) => {
    return requestBlob(item.value, { method: 'GET' });
  });

  Promise.all(requestsA).then((result: any) => {
    for (let i = 0; i < result.length; i++) {
      const tempData = {
        key: tempList[i].key,
        value: result[i],
      };
      data.technologyList[i].img = tempData.value;
    }
  });
};
//枚举值
const enumeration = () => {
  request('/aiipweb/om-service/dict/getDict', {
    method: 'GET',
    data: {
      dictName: 'backGroundType',
    },
  }).then((res: any) => {
    console.log(res, '---------00000000000000000');
    res.body.forEach((item) => {
      data.backGroundTypeList[item.value] = item.label;
    });
  });
};
enumeration();
//枚举值显示
const backGroundType = (value) => {
  return data.backGroundTypeList[value];
};
</script>

<style scoped lang="less">
.preview {
  // height: 100%;
  height: 100vh;
  position: relative;
}
</style>
