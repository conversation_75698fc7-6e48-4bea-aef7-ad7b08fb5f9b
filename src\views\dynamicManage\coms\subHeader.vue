<template>
  <div class="sub-header">
    <div v-if="breadCrumb.length === 0" class="title">{{ title }}</div>
    <a-breadcrumb v-else>
      <a-breadcrumb-item v-for="(item, i) in breadCrumb" :key="i">
        <router-link v-if="item.path" :to="{ path: item.path, query: item.query }">{{ item.name }}</router-link>
        <span v-else>{{ item.name }}</span>
      </a-breadcrumb-item>
    </a-breadcrumb>
    <slot name="subHeader"></slot>
  </div>
</template>

<script>
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    title: {
      type: String,
      default: '',
    },
    breadCrumb: {
      type: Array,
      default: () => [],
    },
  },
});
</script>

<style lang="less" scoped>
.sub-header {
  width: 100%;
  z-index: 99;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .title {
    font-size: 18px;
    color: #121f2c;
    font-weight: 600;
  }
}
</style>
