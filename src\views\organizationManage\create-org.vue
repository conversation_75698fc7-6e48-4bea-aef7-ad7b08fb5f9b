<template>
  <a-modal :visible="visible" title="新建组织" width="554px" @cancel="cancel" @ok="handleOK">
    <a-form ref="formRef" name="form" :colon="false" :model="formData" :rules="rules" :wrapper-col="{ span: 15 }" autocomplete="off">
      <a-form-item label="组织名称" name="groupName">
        <a-input v-model:value.trim="formData.groupName" placeholder="请输入" allow-clear />
      </a-form-item>
      <a-form-item label="所属一级组织" name="firstGroup">
        <a-select placeholder="请选择" v-model:value="formData.firstGroup" :filter-option="filterOption" :options="firstGroupList" :field-names="{ label: 'groupName', value: 'groupId' }" allowClear show-search :getPopupContainer="getPopupContainer"></a-select>
      </a-form-item>
      <a-form-item label="所属二级组织" name="secondGroup">
        <a-select placeholder="请选择" :disabled="isSecondDisabled" :filter-option="filterOption" v-model:value="formData.secondGroup" :options="secondGroupList" :field-names="{ label: 'groupName', value: 'groupId' }" allowClear show-search :getPopupContainer="getPopupContainer"></a-select>
      </a-form-item>
      <a-form-item label="所属三级组织" name="thirdGroup">
        <a-select placeholder="请选择" :disabled="isThirdDisabled" :filter-option="filterOption" v-model:value="formData.thirdGroup" :options="thirdGroupList" :field-names="{ label: 'groupName', value: 'groupId' }" allowClear show-search :getPopupContainer="getPopupContainer"></a-select>
      </a-form-item>
    </a-form>
    <div class="tips">提示：新建成功后，仅可修改组织名称，不可修改所属组织，请谨慎设置</div>
    <template #footer>
      <div>
        <a-button type="default" @click="cancel">取消</a-button>
        <a-button type="primary" :loading="confirmLoading" @click="handleOK">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { defineProps, watch, ref, reactive, defineEmits } from 'vue';
import { message, Modal } from 'ant-design-vue';
import request from '@/request';
import { debounce } from 'lodash';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  firstGroupList: {
    type: Array,
    default: () => [],
  }
});

const formRef = ref(null);

const formData = reactive({
  groupName: '',
  firstGroup: null,
  secondGroup: null,
  thirdGroup: null,
});

const isSecondDisabled = ref(true);
const isThirdDisabled = ref(true);

const secondGroupList = ref([]);
const thirdGroupList = ref([]);
const groupId = ref('');

// 获取组织树
const getGroupList = async (value) => {
  let resData = [];
  const res = await request('/web/admin/um/v1/group/list-tree', {
    method: 'GET',
    data: { groupId: value || null },
  });
  if (res.code === 0) {
    resData = res.data;
  } else {
    resData = [];
  }
  return resData;
};
watch(
  () => formData.firstGroup,
  async (val) => {
    if (val) {
      groupId.value = val;
      secondGroupList.value = await getGroupList(val);
      isSecondDisabled.value = secondGroupList.value.length ? false : true;
    } else {
      groupId.value = '';
      secondGroupList.value = [];
      isSecondDisabled.value = true;
    }
    formData.secondGroup = null;
    formData.thirdGroup = null;
    thirdGroupList.value = [];
    isThirdDisabled.value = true;
  }
);
watch(
  () => formData.secondGroup,
  async (val) => {
    if (val) {
      groupId.value = val;
      thirdGroupList.value = await getGroupList(val);
      isThirdDisabled.value = thirdGroupList.value.length ? false : true;
    } else {
      groupId.value = formData.firstGroup || '';
      thirdGroupList.value = [];
      isThirdDisabled.value = true;
    }
    formData.thirdGroup = null;
  }
);
watch(
  () => formData.thirdGroup,
  (val) => {
    if (val) {
      groupId.value = val;
    } else {
      groupId.value = formData.secondGroup || formData.firstGroup || '';
    }
  }
);

const inputBlur = async () => {
  let resData = null;
  const param = {
    isCreate: true,
    groupId: groupId.value,
    groupName: formData.groupName,
  };
  const res = await request('/web/admin/um/v1/group/exist', {
    method: 'GET',
    data: param,
  })
  if (res.code === 0) {
    resData = res.data;
  } else {
    resData = null;
  }
  return resData;
};

const validateName = async (rule, value) => {
  if (!value || value.length > 30) {
    return Promise.reject('请输入30字符以内的名称');
  } else {
    const isRepeat = await inputBlur();
    if (isRepeat) {
      return Promise.reject('兄弟组织中已有同名组织，不可重复创建');
    } else {
      return Promise.resolve();
    }
  }
};
const rules = {
  groupName: [
    { required: true, trigger: 'blur', validator: validateName },
  ],
};

const emits = defineEmits(['cancelModal']);
//关闭
const cancel = () => {
  // formData.groupName = '';
  // formData.firstGroup = null;
  // formData.secondGroup = null;
  // formData.thirdGroup = null;
  groupId.value = '';
  formRef.value?.resetFields();
  emits('cancelModal');
};

const confirmLoading = ref(false);

const handleOK = debounce(() => {
  formRef.value.validate().then(() => {
    confirmLoading.value = true;
    const param = {
      groupName: formData.groupName,
      groupId: groupId.value,
    };
    request('/web/admin/um/v1/group/create', {
      method: 'POST',
      data: param,
    }).then((res) => {
      if (res.code === 0) {
        if (res.data) {
          message.success('新建组织成功');
          groupId.value = '';
          formRef.value?.resetFields();
          emits('cancelModal', 'create');
        } else {
          message.error(res.msg);
        }
      } else {
        message.error(`新建组织失败，${res.msg}`);
      }
      confirmLoading.value = false;
    });
  })
  .catch(err => {
    throw new Error(err);
  });
}, 500);

const filterOption = (input, option) => {
  return option.groupName.indexOf(input) >= 0;
};
</script>

<style lang="less" scoped>
.tips {
  font-weight: 400;
  font-size: 12px;
  color: #f5a623;
  margin: 4px 0 16px 35px;
}
:deep .ant-form-item {
  margin-bottom: 32px;
}
:deep .ant-modal-title {
  color: #121f2c;
}
:deep .ant-form-item-label > label {
  color: #121f2c;
  margin-right: 5px;
  width: 130px;
  text-align: right;
  display: inline-block;
  vertical-align: sub;
  &::after {
    content: '';
  }
}
:deep .ant-form-item-with-help .ant-form-item-explain{
  height: 0;
  min-height: 0;
}
</style>