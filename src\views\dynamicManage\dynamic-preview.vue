<template>
  <div class="preview">
    <headerTab></headerTab>

    <div class="plateform-updates-detail">
      <div class="header">
        <sub-header :bread-crumb="breadCrumb" />
      </div>
      <a-spin :spinning="pageLoading">
        <div class="content">
          <div class="content-header">
            <h2>{{ previewData.dynamicTitle }}</h2>
            <span>发布时间：{{ previewData.firstPubTime }}</span>
          </div>
          <div class="content-description" v-html="ifRefresh == 1 ? previewData.dynamicContentTags : previewData.dynamicContent"></div>
        </div>
      </a-spin>
    </div>

    <footTab></footTab>
  </div>
</template>

<script>
import { defineComponent } from 'vue';

import SubHeader from './coms/subHeader.vue';
import headerTab from './coms/header.vue';
import footTab from './coms/footTab.vue';

import { DynamicManagement } from '@/apis';
const { getDetail } = DynamicManagement;

export default defineComponent({
  components: {
    SubHeader,
    headerTab,
    footTab,
  },
  data() {
    return {
      previewData: {},
      ifRefresh: this.$route.query.iscreate || '0',
      pageLoading: false,
      breadCrumb: [
        {
          name: '平台动态',
        },
        {
          name: `动态详情`,
        },
      ],
    };
  },
  mounted() {
    this.loadPreviewData();
  },

  methods: {
    loadPreviewData() {
      console.log(this.ifRefresh);
      this.pageLoading = true;
      if (this.ifRefresh == 1) {
        this.previewData = JSON.parse(localStorage.getItem('previewData'));
        console.log(this.previewData, '--->this.previewData');
        this.pageLoading = false;
      } else {
        this.getPreviewData();
      }
    },
    async getPreviewData() {
      this.pageLoading = true;
      await getDetail({ data: { id: this.$route.query.id } }).then((res) => {
        if (res.state === 'OK') {
          this.previewData = res.body;
          console.log(this.previewData, '--->this.previewData');
          this.pageLoading = false;
        }
      });
    },
  },
});
</script>
<style lang="less" scoped>
.preview {
  height: 100vh;
  .plateform-updates-detail {
    padding: 60px 0px 64px;
    background: #f5fbfe;
    min-height: calc(100vh - 320px);
    .header {
      padding: 16px 0px;
      width: 1200px;
      margin: auto;
    }
    .content {
      background: #ffffff;
      box-shadow: 0px 2px 4px 0px rgba(0, 20, 26, 0.04);
      border-radius: 4px;
      border: 1px solid #ffffff;
      backdrop-filter: blur(8px);
      padding: 28px 32px 64px;
      min-height: 416px;
      width: 1200px;
      margin: auto;
      .content-header {
        margin-bottom: 8px;
        h2 {
          font-weight: 600;
          font-size: 30px;
          color: #121f2c;
        }
        span {
          display: inline-block;
          font-size: 12px;
          color: #a0a6ab;
          line-height: 22px;
          margin-top: 8px;
        }
      }
      .content-description {
        border-top: 1px solid #dfe3e7;
        // border-bottom: 1px solid #dfe3e7;
        padding: 16px 0px;
      }
      .content-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 16px;
        span {
          cursor: pointer;
          transition: all 0.3s;
          &:hover {
            color: #0082ff;
          }
        }
      }
    }
  }
}
</style>
