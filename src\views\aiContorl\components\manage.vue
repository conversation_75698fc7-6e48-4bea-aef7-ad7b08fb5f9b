<template>
  <a-modal :visible="props.manageVal" :width="560" title="标签管理" @ok="handleOk" @cancel="cancel">
    <a-form :model="props.manageObj" name="basic" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }" autocomplete="off">
      <a-form-item label="能力名称" name="name">{{ props.manageObj.name }} </a-form-item>
      <a-form-item label="能力类别" name="type">{{ statusTransfer(props.manageObj.type) }} </a-form-item>
      <a-form-item label="能力URL" name="url">
        <a-input :value="props.manageObj.url" readonly="readonly" />
      </a-form-item>
      <a-form-item label="能力标签" name="label" :required="true" help="">
        <a-radio-group v-model:value="valueLabel">
          <a-radio value="0"><span class="null">N/A</span></a-radio>
          <a-radio value="1"><span class="hot">HOT</span></a-radio>
          <a-radio value="2"><span class="new">NEW</span></a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="热词配置" name="hotWord" :required="true" help="">
        <a-radio-group v-model:value="valueHotWord" :options="options" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup lang="ts">
import { defineProps, defineEmits, ref, reactive } from 'vue';
import request from '@/request';
import { message } from 'ant-design-vue';
const props = defineProps({
  manageVal: {
    //模态框
    type: Boolean,
    default: false,
  },
  manageObj: {
    //能力所有数据
    type: Object,
    default: () => ({}),
  },
});
const data = reactive({
  inputVal: '',
  enumerationList: {}, //枚举值
});
const valueLabel = ref(props.manageObj.label || '0');
const valueHotWord = ref(props.manageObj.hotWordsConfig || '0');
const options = [
  {
    label: '否',
    value: '0',
  },
  {
    label: '是',
    value: '1',
  },
];
const emits = defineEmits(['cancelModal']);
//关闭
const cancel = () => {
  emits('cancelModal');
};
//确认请求
const handleOk = () => {
  const capabilityId = props?.manageObj?.id;
  const label = Number(valueLabel.value);
  const hotWordsConfig = Number(valueHotWord.value);
  request('/aiipweb/om-service/capability/updateLabelInfo', {
    method: 'POST',
    data: {
      id: capabilityId,
      label: label,
      hotWordsConfig: hotWordsConfig,
    },
  }).then((res: any) => {
    if (res.body == 'success') {
      message.success('标签配置成功');
      emits('cancelModal', true);
    } else {
      message.error(res.errorMessage);
      emits('cancelModal');
    }
  });
};
//枚举值
const enumeration = () => {
  request('/aiipweb/om-service/dict/getDict', {
    method: 'GET',
    data: {
      dictName: 'category',
    },
  }).then((res: any) => {
    res.body.forEach((item) => {
      data.enumerationList[item.value] = item.label;
    });
  });
};
enumeration();
//枚举值显示
const statusTransfer = (value) => {
  return data.enumerationList[value];
};
</script>
<style lang="less" scoped>
.null {
  border-radius: 2px;
  border: 1px solid #a0a6ab;
  padding: 0 6px;
  color: #a0a6ab;
}
.hot {
  border-radius: 2px;
  border: 1px solid #ff454d;
  color: #ff454d;
  padding: 0 6px;
}
.new {
  border-radius: 2px;
  border: 1px solid #ff8b00;
  color: #ff8b00;
  padding: 0 6px;
}
/deep/.ant-form-item-label > label {
  color: #606972;
}
/deep/.ant-form-item-control-input-content {
  color: #121f2c;
}
</style>
