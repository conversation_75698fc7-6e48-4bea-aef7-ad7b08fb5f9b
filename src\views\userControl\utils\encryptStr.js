// 请求密钥
import request from '@/request';
export function setPassword(password) {
  return request('/web/um/v1/public/key').then((res) => {
    return encryptStr(res.data, password);
  });
}
function encryptStr(publicKey, password) {
  if (!publicKey) return '';
  let forge = window.forge;
  const publicKeyPem = `-----BEGIN PUBLIC KEY-----${publicKey}-----END PUBLIC KEY-----`;
  const rsaEncryptedBytes = forge.pki.publicKeyFromPem(publicKeyPem).encrypt(password, 'RSA-OAEP', {
    md: forge.md.sha256.create(),
    mgf1: forge.mgf.mgf1.create(forge.md.sha1.create()),
  });
  const value = forge.util.encode64(rsaEncryptedBytes);
  return value;
}
