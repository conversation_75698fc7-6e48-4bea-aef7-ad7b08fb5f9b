<template>
  <div class="modal-wrap" ref="modal-wrap">
    <a-modal :visible="visible" width="360px" :closable="false" :get-container="getContainer()">
      <template #title>
        <div class="title">
          <jt-icon type="iconjinggao-tianchong" class="icon-warn"></jt-icon>
          <span class="title-text">确定删除组织“{{ delOrg.groupName }}”吗？</span>
        </div>
      </template>
      <div class="content">将同时删除该组织的所有子组织，删除后不可恢复，请谨慎操作</div>
      <template #footer>
        <div>
          <a-button type="default" @click="cancel">取消</a-button>
          <a-button type="danger" :loading="confirmLoading" @click="handleOK">删除</a-button>
        </div>
      </template>
    </a-modal>
  </div>
</template>

<script setup>
import { defineProps, watch, ref, defineEmits } from 'vue';
import { message, Modal } from 'ant-design-vue';
import request from '@/request';
import { debounce } from 'lodash';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  delOrg: {
    type: Object,
    default: () => {},
  }
});

const emits = defineEmits(['cancelModal']);
//关闭
const cancel = () => {
  emits('cancelModal');
};

const confirmLoading = ref(false);

const handleOK = debounce(() => {
  confirmLoading.value = true;
  request('/web/admin/um/v1/group/delete', {
    method: 'GET',
    data: { groupId: props.delOrg.groupId },
  }).then((res) => {
    if (res.code === 0) {
      if (res.data) {
        message.success('删除组织成功');
        emits('cancelModal', props.delOrg.level);
      } else {
        message.error(res.msg);
      }
    } else {
      message.error(`删除组织失败，${res.msg}`);
    }
    confirmLoading.value = false;
  });
}, 500);

const getContainer = () => {
  return document.querySelector('.modal-wrap');
};
</script>

<style lang="less" scoped>
.modal-wrap {
  .title {
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 14px;
    color: #121f2c;
    padding: 32px 32px 12px;
    .icon-warn {
      font-size: 18px;
      color: #f24444;
      margin-right: 9px;
    }
  }
  .content {
    font-weight: 400;
    font-size: 12px;
    color: #606972;
    line-height: 18px;
    padding: 0 24px 24px 60px;
  }
  :deep .ant-modal-header {
    padding: 0;
    border-bottom: none;
  }
  :deep .ant-modal-body {
    padding: 0;
  }
  :deep .ant-modal-footer {
    border-top: none;
    padding: 0 32px 24px 0;
  }
  :deep .ant-btn {
    font-size: 12px;
  }
}
</style>