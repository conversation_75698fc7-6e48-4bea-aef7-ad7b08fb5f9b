<template>
  <div class="form-box">
    <a-form ref="appFormRef" :model="formData" :layout="'vertical'" autocomplete="off" class="form-box-content">
      <a-form-item
        class="form-box-item"
        label="推荐卡片一"
        name="recommend1"
        :rules="[
          {
            required: false,
            // message: '请选择推荐能力',
            // trigger: 'change',
          },
        ]"
      >
        <a-select v-model:value="formData.recommend1" :optionFilterProp="'label'" :options="option1" show-search allowClear placeholder="请选择" style="width: 190px; height: 32px" :getPopupContainer="(triggerNode) => triggerNode.parentNode"> </a-select>
      </a-form-item>

      <a-form-item
        class="form-box-item"
        label="推荐卡片二"
        name="recommend2"
        :rules="[
          {
            required: false,
            // message: '请选择推荐能力',
            // trigger: 'change',
          },
        ]"
      >
        <a-select v-model:value="formData.recommend2" :optionFilterProp="'label'" :options="option2" show-search allowClear placeholder="请选择" style="width: 190px; height: 32px" :getPopupContainer="(triggerNode) => triggerNode.parentNode"> </a-select>
      </a-form-item>

      <a-form-item
        class="form-box-item"
        label="推荐卡片三"
        name="recommend3"
        :rules="[
          {
            required: false,
            // message: '请选择推荐能力',
            // trigger: 'change',
          },
        ]"
      >
        <a-select v-model:value="formData.recommend3" :optionFilterProp="'label'" :options="option3" show-search allowClear placeholder="请选择" style="width: 190px; height: 32px" :getPopupContainer="(triggerNode) => triggerNode.parentNode"> </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import { reactive, watch, defineExpose, defineProps, computed, watchEffect, defineEmits } from 'vue';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Array,
    default() {
      return [];
    },
  },
  originOptions: {
    type: Array,
    default() {
      return [];
    },
  },
});
// const appFormRef = ref();
// const emits = defineEmits(['validChange']);
// 数据源
interface fromDataType {
  recommend1: any;
  recommend2: any;
  recommend3: any;
}
const formData: fromDataType = reactive({
  recommend1: undefined,
  recommend2: undefined,
  recommend3: undefined,
});
// 选项不能重复
const option1 = computed(() => {
  return props.originOptions.map((item: any) => {
    if (item.value === formData.recommend2 || item.value === formData.recommend3) {
      return {
        value: item.value,
        label: item.label,
        disabled: true,
      };
    } else {
      return {
        value: item.value,
        label: item.label,
        disabled: false,
      };
    }
  });
});
const option2 = computed(() => {
  return props.originOptions.map((item: any) => {
    if (item.value === formData.recommend1 || item.value === formData.recommend3) {
      return {
        value: item.value,
        label: item.label,
        disabled: true,
      };
    } else {
      return {
        value: item.value,
        label: item.label,
        disabled: false,
      };
    }
  });
});
const option3 = computed(() => {
  return props.originOptions.map((item: any) => {
    if (item.value === formData.recommend1 || item.value === formData.recommend2) {
      return {
        value: item.value,
        label: item.label,
        disabled: true,
      };
    } else {
      return {
        value: item.value,
        label: item.label,
        disabled: false,
      };
    }
  });
});

// 监听 是否是编辑状态，回填数据
watch(
  () => props.isEdit,
  (newValue) => {
    if (newValue) {
      if (props.editData.length > 0) {
        formData.recommend1 = props.editData[0] || undefined;
        formData.recommend2 = props.editData[1] || undefined;
        formData.recommend3 = props.editData[2] || undefined;

        // 若已下架，则置空
        formData.recommend1 = props.originOptions.filter((item: any) => item.value === formData.recommend1).length > 0 ? formData.recommend1 : undefined;
        formData.recommend2 = props.originOptions.filter((item: any) => item.value === formData.recommend2).length > 0 ? formData.recommend2 : undefined;
        formData.recommend3 = props.originOptions.filter((item: any) => item.value === formData.recommend3).length > 0 ? formData.recommend3 : undefined;
      }
    }
  },
  {
    immediate: true,
  }
);
// 组件校验状态同步给父组件
// watchEffect(() => {
//   if (formData.recommend1 !== undefined && formData.recommend2 !== undefined && formData.recommend3 !== undefined) {
//     emits('validChange', true);
//   } else {
//     emits('validChange', false);
//   }
// });

// 主动触发表单验证
// const formValid = () => {
//   appFormRef.value.validateFields(['recommend1', 'recommend2', 'recommend3']);
// };

defineExpose({
  formData,
  // formValid,
});
</script>

<style lang="less" scoped>
.form-box {
  padding-left: 12px;
  &-content {
    display: flex;
    align-items: center;
    .form-box-item {
      margin-right: 24px;
    }
  }
}
</style>
