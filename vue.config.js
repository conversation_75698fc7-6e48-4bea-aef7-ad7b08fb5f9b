const path = require('path');
const fs = require('fs');
const proxyTable = require('./proxy-table');
const lessToJs = require('less-vars-to-js');
const themeVariables = lessToJs(fs.readFileSync(path.join(__dirname, './src/assets/styles/theme/theme.less'), 'utf8'));
const IS_DEV = process.env.NODE_ENV === 'development';

module.exports = {
  publicPath: IS_DEV ? '/common-management' : './common-management',
  productionSourceMap: false,
  devServer: {
    proxy: proxyTable,
  },
  chainWebpack: (config) => {
    config.plugin('define').tap((args) => {
      args[0]['process.env'].PROXY_ENV = JSON.stringify(process.env.PROXY_ENV);
      return args;
    });
  },
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: themeVariables,
          javascriptEnabled: true,
        },
      },
    },
  },
  configureWebpack: {
    module: {
      rules: [
        {
          test: /.mjs$/,
          include: /node_modules/,
          type: 'javascript/auto',
        },
      ],
    },
  },
};
