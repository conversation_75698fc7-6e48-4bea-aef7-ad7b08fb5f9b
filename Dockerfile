FROM  ${HAR<PERSON>R_BASE_URL}/${JT_BASE_IMAGE_PREFIX}/${JT_TOMCAT_VERSION}

MAINTAINER xuhuiqun "<EMAIL>"

ENV TOMCAT_BASE_DIR="/usr/local/tomcat" 

WORKDIR $${q}{TOMCAT_BASE_DIR}

ADD dist/ webapps/ROOT/
ADD error.html webapps/ROOT/
ADD error.txt .

ADD start.sh bin/
RUN chmod +rx $${q}{TOMCAT_BASE_DIR}/bin/start.sh

RUN sed -i '$${q}{d}' conf/web.xml \
        && cat error.txt >> conf/web.xml \
        && rm error.txt

RUN useradd -s "/bin/sh" -u ${CONTAINER_USER_ID} apps && chown -RH apps $${q}{TOMCAT_BASE_DIR}
USER apps

ENTRYPOINT sh $${q}{TOMCAT_BASE_DIR}/bin/start.sh
