<template>
  <div class="technology">
    <div class="technology-title">技术特色及常见问题</div>

    <div class="technology-content">
      <div class="technology-content-left">
        <div class="technology-feature">
          <div class="technology-feature-title">技术特色</div>
          <div class="technology-feature-tips">
            <jt-icon type="iconinfo" class="iconfont-info"></jt-icon>
            <div class="info-text">建议修改示意图标，如不修改将在页面中显示如下</div>
          </div>
          <div class="technology-feature-content">
            <feature ref="featureRef1" :is-edit="props.isEdit" :edit-data="featureData1" :font-number="28" :icon-list="props.iconList" :headline="featureText" :num="'一'" @valid-change="featureValid1"></feature>
            <feature ref="featureRef2" :is-edit="props.isEdit" :edit-data="featureData2" :font-number="28" :icon-list="props.iconList" :headline="featureText" :num="'二'" @valid-change="featureValid2"></feature>
            <feature ref="featureRef3" :is-edit="props.isEdit" :edit-data="featureData3" :font-number="28" :icon-list="props.iconList" :headline="featureText" :num="'三'" @valid-change="featureValid3"></feature>
            <feature ref="featureRef4" :is-edit="props.isEdit" :edit-data="featureData4" :font-number="28" :icon-list="props.iconList" :headline="featureText" :num="'四'" @valid-change="featureValid4" :need-valid="false"></feature>
          </div>
        </div>

        <div class="question">
          <div class="question-title">常见问题</div>
          <div class="question-content">
            <question ref="questionRef" @valid-change="questionValid" :is-edit="props.isEdit" :edit-data="questionData"> </question>
          </div>
        </div>

        <div class="recommend">
          <div class="recommend-title">相关推荐</div>
          <div class="recommend-content">
            <recommend :is-edit="props.isEdit" :edit-data="recommendData" :origin-options="props.originOptions" ref="recommendRef"></recommend>
            <!-- <recommend @valid-change="recommendValid" :is-edit="props.isEdit" :edit-data="recommendData" :origin-options="props.originOptions" ref="recommendRef"></recommend> -->
          </div>
        </div>

        <div class="btn-groups">
          <div class="pre" @click="toPre">
            <jt-icon type="iconleft" />
          </div>
          <a-button class="next" type="primary" @click="toNext">下一步</a-button>
          <a-button class="save" type="default" @click="saveDraft">保存</a-button>
        </div>
      </div>
      <div class="pre-view">
        <div class="pre-view-title">入驻能力页示意</div>
        <div class="pre-view-pic">
          <img class="pre-view-img" :src="preUrl" alt="" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineEmits, defineProps, defineExpose, ref, computed, watch } from 'vue';
import { useStore } from 'vuex';
import { requestBlob } from '@/request';
import feature from './components/funcIntro.vue';
import question from './components/question.vue';
import recommend from './components/recommend.vue';

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  appInfo: {
    type: Object,
    default() {
      return {};
    },
  },
  iconList: {
    type: Array,
    default() {
      return [];
    },
  },
  originOptions: {
    type: Array,
    default() {
      return [];
    },
  },
});

// const preUrl = require('@/assets/images/aiControl/step4.png');
const preUrl: any = ref('');
const emit = defineEmits(['toPre', 'toNext', 'saveDraft']);
const store = useStore();
// 组件文案
const featureText = {
  title: '特色',
  subtitle: '技术特色',
  content: '技术简介',
  icon: '特色图标',
};
// 同步到vuex中的数据结构
const techFeatures: any = {
  features: [],
  questions: [],
  recommends: [],
};

const capacityType = computed(() => {
  return store.state.appInfo.baseInfo.type;
});
// 监听能力类别变化
watch(
  capacityType,
  (newValue) => {
    if (newValue !== '') {
      // 获取 右侧的示例图
      const url = `/aiipweb/om-service/os/getObject?category=5&object=step4.png&capabilityCategory=${newValue}`;
      requestBlob(url, { method: 'GET' }).then((res) => {
        preUrl.value = res;
      });
    }
  },
  {
    immediate: true,
  }
);

// 监听 是否是编辑状态，回填数据
const featureData1 = ref({});
const featureData2 = ref({});
const featureData3 = ref({});
const featureData4 = ref({});

const questionData = ref([]);
const recommendData = ref([]);

watch(
  () => props.isEdit,
  (newValue) => {
    if (newValue) {
      if (props.appInfo.techFeatures?.features?.length > 1) {
        featureData1.value = props.appInfo.techFeatures.features[0];
        featureData2.value = props.appInfo.techFeatures.features[1];
        featureData3.value = props.appInfo.techFeatures.features[2];
        featureData4.value = props.appInfo.techFeatures.features[3];
      } else {
        featureData1.value = props.appInfo.techFeatures.features[0];
        featureData2.value = props.appInfo.techFeatures.features[0];
        featureData3.value = props.appInfo.techFeatures.features[0];
        featureData4.value = props.appInfo.techFeatures.features[0];
      }

      questionData.value = props.appInfo.techFeatures?.questions;

      recommendData.value = props.appInfo.techFeatures?.recommends;
    }
  },
  {
    immediate: true,
  }
);

// 技术特色子组件实例 与 其返回的校验状态
const featureRef1 = ref();
const feature1 = ref<boolean>(false);
const featureValid1 = (val) => {
  feature1.value = val;
};
const featureRef2 = ref();
const feature2 = ref<boolean>(false);
const featureValid2 = (val) => {
  feature2.value = val;
};
const featureRef3 = ref();
const feature3 = ref<boolean>(false);
const featureValid3 = (val) => {
  feature3.value = val;
};
const featureRef4 = ref();
const feature4 = ref<boolean>(false);
const featureValid4 = (val) => {
  feature4.value = val;
};

// 常见问题子组件实例 与 其返回的校验状态
const questionRef = ref();
const questionValidetor = ref<boolean>(false);
const questionValid = (val) => {
  questionValidetor.value = val;
};

// 推荐卡片子组件实例 与 其返回的校验状态
const recommendRef = ref();
// const recommendValidetor = ref<boolean>(false);
// const recommendValid = (val) => {
//   recommendValidetor.value = val;
// };

// 下一步可点击的状态
const valid = computed(() => {
  return feature1.value && feature2.value && feature3.value && feature4.value && questionValidetor.value;
});

// 整理数据，同步到vuex
const updateToStore = () => {
  const featuresItem = {
    name: '',
    intro: '',
    img: '',
  };
  featuresItem.name = featureRef1.value.formData.intro;
  featuresItem.intro = featureRef1.value.formData.desc;
  featuresItem.img = (props.iconList as any).filter((item: any) => item.value === featureRef1.value.formData.icon)[0].key;
  techFeatures.features[0] = { ...featuresItem };

  featuresItem.name = featureRef2.value.formData.intro;
  featuresItem.intro = featureRef2.value.formData.desc;
  featuresItem.img = (props.iconList as any).filter((item: any) => item.value === featureRef2.value.formData.icon)[0].key;
  techFeatures.features[1] = { ...featuresItem };

  featuresItem.name = featureRef3.value.formData.intro;
  featuresItem.intro = featureRef3.value.formData.desc;
  featuresItem.img = (props.iconList as any).filter((item: any) => item.value === featureRef3.value.formData.icon)[0].key;
  techFeatures.features[2] = { ...featuresItem };

  featuresItem.name = featureRef4.value.formData.intro;
  featuresItem.intro = featureRef4.value.formData.desc;
  featuresItem.img = (props.iconList as any).filter((item: any) => item.value === featureRef4.value.formData.icon)[0].key;
  techFeatures.features[3] = { ...featuresItem };

  techFeatures.questions = [];
  const questionItem = {
    answer: '',
    question: '',
  };
  if (questionRef.value.resultData.length > 0) {
    for (let i = 0; i < questionRef.value.resultData.length; i++) {
      questionItem.answer = questionRef.value.resultData[i].value;
      questionItem.question = questionRef.value.resultData[i].key;
      techFeatures.questions[i] = { ...questionItem };
    }
    if (questionRef.value.formData.key !== '' || questionRef.value.formData.value !== '') {
      questionItem.answer = questionRef.value.formData.value;
      questionItem.question = questionRef.value.formData.key;
      techFeatures.questions[questionRef.value.resultData.length] = { ...questionItem };
    }
  } else {
    if (questionRef.value.formData.key !== '' || questionRef.value.formData.value !== '') {
      questionItem.answer = questionRef.value.formData.value;
      questionItem.question = questionRef.value.formData.key;
      techFeatures.questions[0] = { ...questionItem };
    }
  }

  techFeatures.recommends[0] = recommendRef.value.formData.recommend1 || null;
  techFeatures.recommends[1] = recommendRef.value.formData.recommend2 || null;
  techFeatures.recommends[2] = recommendRef.value.formData.recommend3 || null;

  store.commit('updateAppInfoStep4', techFeatures);
};

const toPre = () => {
  updateToStore();
  emit('toPre');
};
const toNext = () => {
  // 检验通过才可以点击，不通过不支持点击，且有msg提示；
  if (valid.value) {
    updateToStore();
    emit('toNext');
  } else {
    featureRef1.value.formValid();
    featureRef2.value.formValid();
    featureRef3.value.formValid();
    featureRef4.value.formValid();
    questionRef.value.formValid();
    // recommendRef.value.formValid();
    return;
  }
};
const saveDraft = () => {
  updateToStore();
  emit('saveDraft');
};
defineExpose({
  updateToStore,
});
</script>

<style lang="less" scoped>
.technology {
  margin-top: 12px;
  background-color: #fff;
  padding: 28px 20px 104px 20px;
  border-radius: 2px;
  &-title {
    margin-bottom: 24px;
    height: 24px;
    font-size: 16px;
    font-weight: 500;
    color: #121f2c;
    line-height: 24px;
  }
  &-content {
    display: flex;
    &-left {
      flex: 68;
      padding-right: 20px;
      .technology-feature {
        margin-bottom: 64px;
        &-title {
          margin-bottom: 16px;
          height: 16px;
          font-size: 14px;
          font-weight: 500;
          color: #121f2c;
          line-height: 16px;
          border-left: 4px solid #0082ff;
          padding-left: 8px;
        }
        &-tips {
          margin-bottom: 24px;
          height: 34px;
          background: #edf7ff;
          border-radius: 2px;
          border: 1px solid #b0d5ff;
          padding-left: 17px;
          display: flex;
          align-items: center;
          .iconfont-info {
            width: 14px;
            height: 14px;
            background: #5a99f9;
            margin-right: 9px;
            color: #fff;
            border-radius: 50%;
            text-align: center;
            line-height: 16px;
            font-size: 12px;
          }
          .info-text {
            font-size: 12px;
            font-weight: 400;
            color: #666666;
          }
        }
        &-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
      .question {
        margin-bottom: 64px;
        &-title {
          margin-bottom: 24px;
          height: 16px;
          font-size: 14px;
          font-weight: 500;
          color: #121f2c;
          line-height: 16px;
          border-left: 4px solid #0082ff;
          padding-left: 8px;
        }
      }
      .recommend {
        margin-bottom: 48px;
        &-title {
          margin-bottom: 24px;
          height: 16px;
          font-size: 14px;
          font-weight: 500;
          color: #121f2c;
          line-height: 16px;
          border-left: 4px solid #0082ff;
          padding-left: 8px;
        }
        // &-content {
        //   height: 100px;
        // }
      }
      .btn-groups {
        padding-left: 12px;
        display: flex;
        align-items: center;
        .pre {
          margin-right: 8px;
          width: 32px;
          height: 32px;
          background: #ffffff;
          border-radius: 2px;
          border: 1px solid #d6d9db;
          text-align: center;
          line-height: 30px;
          color: #7f828f;
          font-size: 16px;
          cursor: pointer;
          &:hover {
            color: #0082ff;
            border-color: #0082ff;
          }
        }
        .next {
          margin-right: 8px;
          width: 120px;
          height: 32px;
          border-radius: 2px;
        }
        .save {
          width: 88px;
          height: 32px;
          border-radius: 2px;
        }
      }
    }
    .pre-view {
      flex: 32;
      border-left: 1px solid #efefef;
      padding-right: 20px;
      &-title {
        margin-left: 9px;
        padding-left: 8px;
        height: 16px;
        line-height: 16px;
        border-left: 4px solid #0082ff;
        font-size: 14px;
        font-weight: 500;
        color: #121f2c;
      }
      &-pic {
        margin-top: 16px;
        margin-left: 20px;
        width: 100%;
        .pre-view-img {
          width: 100%;
        }
      }
    }
  }
}
</style>
