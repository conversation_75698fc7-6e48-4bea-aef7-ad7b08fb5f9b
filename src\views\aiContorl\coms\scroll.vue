<template>
  <div class="scroll-tab">
    <div class="title">{{ title }}</div>
    <div class="main">
      <a-tabs v-model="tab" tab-position="left" animated :style="`background-image:url(${img || userListImg});background-size:100% 100%`">
        <a-tab-pane :key="index + ''" :tab="item.name || userListName" v-for="(item, index) in list">
          <div @mousewheel="onMouse" class="part">
            <div class="tit">{{ item.name || userListName }}</div>
            <div class="name">{{ item.intro || userListIntro }}</div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ScrollTab',
  props: {
    //  标题
    title: {
      type: String, //该项是确定传值类型的可用数组多种类型[String, Number]
      default: '',
    },
    // 展示项数组
    list: {
      type: Array,
      default() {
        //当为数组或者对象时，默认值要用函数方式返回
        return [];
      },
    },
  },
  data() {
    return {
      tab: '0',
      loading: false,
      userListImg: require('@/assets/images/preview/pic-2.png'),
      userListName: '场景名称',
      userListIntro: '这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介这是一段场景简介',
    };
  },
  computed: {
    img() {
      let val = this.list[this.tab];
      return val ? val.img : '';
    },
  },
  methods: {
    onMouse(e) {
      //滚动事件
      if (this.loading) {
        // 阻止默认滚动
        e.preventDefault();
        return false;
      }
      let tabLength = this.list.length;
      if (e.wheelDeltaY == 0 || (e.wheelDeltaY < 0 && this.tab == (tabLength - 1).toString()) || (e.wheelDeltaY > 0 && this.tab == '0')) {
        return false;
      }
      //控制一定时间内只出发一次
      this.loading = true;
      setTimeout(() => {
        this.loading = false;
      }, 2000);
      if (e.wheelDeltaY < 0) {
        e.preventDefault();
        this.tab = this.tab * 1 + 1 + '';
      } else if (e.wheelDeltaY > 0) {
        e.preventDefault();
        this.tab = this.tab - 1 + '';
      }
    },
  },
};
</script>
<style lang="less" scoped>
.scroll-tab {
  width: 100%;
  padding-top: 56px;
  text-align: left;
  min-width: 1320px;
  margin: 0 auto;
  .title {
    color: #121f2c;
    text-align: center;
    font-size: 28px;
    line-height: 36px;
    margin-bottom: 24px;
    font-weight: 600;
  }
  .main {
    padding-bottom: 24px;
    max-width: 1180px;
    margin: 0 auto;
    line-height: 50px;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top right;
    height: 200px;
    color: #606972;
  }
  .part {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-left: 51px;
    margin-top: 40px;
    .tit {
      font-size: 18px;
      font-weight: 600;
      color: #121f2c;
      margin-bottom: 16px;
    }
    .name {
      width: 480px;
      height: 66px;
      font-size: 14px;
      font-weight: 400;
      color: #606972;
      line-height: 22px;
      word-wrap: break-word;
    }
  }
  .part1 {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    height: 200px;
    padding: 50px 0 0 72px;
    .tit1 {
      font-size: 18px;
      font-weight: 600;
      color: #121f2c;
    }
    .name1 {
      width: 480px;
      height: 66px;
      font-size: 14px;
      font-weight: 400;
      color: #606972;
      line-height: 22px;
    }
  }
}
/deep/.ant-tabs-nav-list {
  padding-top: 10px;
}
/deep/.ant-tabs-tab-active {
  background: linear-gradient(90deg, rgba(0, 255, 213, 0) 0%, #00d1e7 100%);
}
/deep/.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #ffffff;
}
/deep/.ant-tabs-tab {
  width: 160px;
  height: 45px;
  color: #ffffff;
  display: flex;
  justify-content: flex-end;
}
/deep/.ant-tabs-left > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab {
  margin-top: 0;
}
/deep/.ant-tabs-nav .ant-tabs-tab:hover {
  color: #ffffff;
  background: linear-gradient(90deg, rgba(0, 255, 213, 0) 0%, rgba(0, 209, 231, 0.3) 100%);
}
/deep/.ant-tabs-ink-bar {
  background-color: rgba(220, 38, 38, 0);
}
/deep/.ant-tabs .ant-tabs-left-bar .ant-tabs-nav {
  color: rgba(255, 255, 255, 0.9);
}
/deep/.ant-tabs .ant-tabs-left-bar {
  width: 160px;
}
/deep/.ant-tabs-nav-wrap {
  background: url('../../../assets/images/preview/tabbg.png');
}
/deep/.ant-tabs .ant-tabs-left-bar .ant-tabs-tab,
.ant-tabs .ant-tabs-right-bar .ant-tabs-tab {
  display: block;
  float: none;
  margin: 0 0 4px 0;
  padding: 8px 24px;
}

/deep/.ant-tabs-nav-scroll {
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  height: 100%;
  align-items: center;
}
/deep/.ant-tabs .ant-tabs-left-content {
  padding-left: 24px;
  border-left: 1px solid #e8e8e8;
  height: 100%;
}
</style>
