<template>
  <div>
    <sub-header :breadCrumb="[{ name: '工单管理', path: '/workorder-manage', query: $route.query }, { name: '接收人管理' }]"> </sub-header>
    <Container>
      <container-item>
        <div class="receiver-manage-header">
          <h3>接收人列表</h3>
          <a-space>
            <a-input v-model:value="keyword" @change="handleSearch" placeholder="用户名/手机号" style="width: 280px; height: 32px">
              <template #prefix>
                <jt-icon type="iconsousuo" style="color: #bec2c5"></jt-icon>
              </template>
            </a-input>
            <a-button @click="handleVisible" type="primary"
              ><template #icon><PlusOutlined /></template>添加接收人</a-button
            >
          </a-space>
        </div>
        <a-table :loading="tableAttr(loading).loading" :columns="columns" :data-source="dataSource" @change="handleTableChange" :pagination="false">
          <template #emptyText>
            <empty
              v-if="!loading"
              title="接收人"
              :showNoDataText="
                showNoDataText({
                  keyword,
                })
              "
            >
              <template #description>
                请立即
                <a href="javascript:;" @click="handleVisible">添加接收人</a>
              </template>
            </empty>
          </template>
          <template #operationSlot="{ record }">
            <a @click="() => handleDelete(record)">删除</a>
          </template>
        </a-table>
        <Pagination :total="pagination.total" :pageNum="pagination.pageNum" :pageSize="pagination.pageSize" @update:pageNum="changePageNum" @update:pageSize="changePageSize" />
      </container-item>
    </Container>
    <add-receiver-modal :visible="addReceiverModal" @initTableData="initTableData" @handleVisible="handleVisible" />
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, createVNode, reactive, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import AddReceiverModal from '../addReceiverModal/index.vue';
import subHeader from '@/components/subHeader.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import Pagination from '@/components/pagination.vue';
import { debounce } from 'lodash';
import request from '@/request';
import { tableAttr, showNoDataText } from '@/utils';
import empty from '@/components/empty.vue';

interface receiverItem {
  addTime: string;
  phoneNum: string;
  userId: string;
  userName: string;
}

export default defineComponent({
  components: {
    AddReceiverModal,
    PlusOutlined,
    subHeader,
    Container,
    ContainerItem,
    Pagination,
    empty,
  },
  setup() {
    const route = useRoute();
    const platform = route.query.platform;
    const isQuestionManage = route.query.activeKey === '2';
    const breadCrumbs = [{ name: '工单管理', path: '/workorder-manage', query: route.query }, { name: '接收人管理' }];

    const addReceiverModal = ref(false);
    const keyword = ref('');
    const columns = [
      {
        dataIndex: 'userName',
        key: 'userName',
        title: '用户名',
        width: '25%',
      },
      {
        dataIndex: 'phoneNum',
        key: 'phoneNum',
        title: '手机号',
        width: '25%',
      },
      {
        dataIndex: 'addTime',
        key: 'addTime',
        title: '添加时间',
        sorter: true,
        width: '30%',
      },
      {
        dataIndex: 'userId',
        key: 'userId',
        title: '操作',
        width: '20%',
        slots: {
          customRender: 'operationSlot',
        },
      },
    ];
    let sortkey = 'desc';
    const loading = ref(false);
    const dataSource = ref<receiverItem[]>([]);
    const pagination = reactive({
      pageSize: 10,
      pageNum: 1,
      total: 0,
    });

    const changePageNum = (pageNum) => {
      pagination.pageNum = pageNum;
      initTableData();
    };
    const changePageSize = (pageSize) => {
      pagination.pageSize = pageSize;
      pagination.pageNum = 1;
      initTableData();
    };

    const handleSearch = () => {
      loading.value = true;
      search();
    };
    const search = debounce(() => {
      pagination.pageNum = 1;
      initTableData();
    }, 500);

    // 初始化获取列表数据
    const initTableData = async () => {
      let reqData = {
        pageSize: pagination.pageSize,
        pageNum: pagination.pageNum,
        keyword: keyword.value,
        addTimeSort: sortkey,
        platform,
      };

      loading.value = true;
      const url = isQuestionManage ? '/ticket/web/receiver/list' : '/ticket/web/demand/receiver/list';
      const res = await request(url, { method: 'POST', data: reqData });
      if (res.state === 'OK') {
        dataSource.value = res.body.list;
        pagination.total = res.body.total;
      }
      loading.value = false;
    };

    // 打开或关闭添加接收人modal框
    const handleVisible = (bol = true) => {
      addReceiverModal.value = bol;
    };

    // 点击排序操作
    const handleTableChange = (page, filter, sort) => {
      sortkey = sort.order == 'ascend' ? 'asc' : 'desc'; // 默认降序
      initTableData();
    };

    // 删除接收人确定
    const handleDelete = (record) => {
      Modal.confirm({
        title: () => `确定删除接收人“${record.userName}”吗？`,
        icon: () => createVNode(ExclamationCircleOutlined),
        content: () => createVNode('span', {}, '删除后工单通知将不再发送至该接收人，请谨慎操作'),
        cancelText: () => '取消',
        okText: () => '确定',
        okType: 'danger',
        width: 476,
        onOk() {
          const url = isQuestionManage ? '/ticket/web/receiver/delete' : '/ticket/web/demand/receiver/delete';
          request(url, { method: 'POST', data: { platform, userId: record.userId, userName: record.userName } }).then((res) => {
            if (res.state === 'OK') {
              initTableData();
              message.success(`删除接收人${record.userName}成功`);
            }
          });
        },
      });
    };

    onMounted(() => {
      initTableData();
    });

    return {
      breadCrumbs,
      columns,
      dataSource,
      keyword,
      addReceiverModal,
      handleSearch,
      pagination,
      loading,
      handleVisible,
      initTableData,
      handleDelete,
      changePageNum,
      changePageSize,
      handleTableChange,
      tableAttr,
      showNoDataText,
    };
  },
});
</script>

<style scoped>
.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}
.receiver-manage-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
</style>
