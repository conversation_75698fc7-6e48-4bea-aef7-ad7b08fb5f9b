import { defineConfig } from 'vite';
const path = require('path');
import vue from '@vitejs/plugin-vue';
const fs = require('fs');
const lessToJs = require('less-vars-to-js');
const themeVariables = lessToJs(fs.readFileSync(path.join(__dirname, './src/assets/styles/theme/theme.less'), 'utf8'));
import requireTransform from 'vite-plugin-require-transform';
import vueJsx from '@vitejs/plugin-vue-jsx';
const proxyTable = require('./proxy-table');

export default defineConfig({
  define: {
    'process.env': {
      PROXY_ENV: process.env.PROXY_ENV,
    },
  },

  resolve: {
    alias: { '@': path.resolve(__dirname, 'src') },
  },
  plugins: [vue(), requireTransform({}), vueJsx()],
  css: {
    preprocessorOptions: {
      less: {
        modifyVars: themeVariables,
        javascriptEnabled: true,
      },
    },
  },
  server: {
    proxy: {
      ...proxyTable,
    },
  },
});
