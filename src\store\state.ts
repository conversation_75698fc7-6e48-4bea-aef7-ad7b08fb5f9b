interface stateType {
  userInfo: any;
  importTooltipVisible: boolean;
  importTooltipFold: boolean;
  sideMenuAuth: string[];
  sideMenu: any[];
  spinning: boolean;
  layoutConfig: any;
  routeViewRefreshKey: string;
  appInfo: any;
  appInfoBackUp: any;
  modalVisible: boolean;
  isSaveDraft: boolean;
  toPath: string;
  // appInfoId: any;
  showGlobalLoading: boolean;
  defaultMediaUrlList: string[];
  refreshToken: string;
  capabilityIntroArr: any;
  jtPK: string;
}
const state: stateType = {
  jtPK: '',
  userInfo: {},
  importTooltipVisible: false,
  importTooltipFold: false,
  sideMenuAuth: [], // 侧边栏数据递归权限
  sideMenu: [], // 侧边栏数据
  spinning: true,
  layoutConfig: {},
  routeViewRefreshKey: new Date().toString(),
  capabilityIntroArr: [],
  //新建能力
  appInfo: {
    abilityId: '',
    id: '',
    //step1 能力基本信息
    baseInfo: {
      type: '',
      intro: '',
      name: '',
      providerAccount: '',
      providerId: 0,
      providerPhone: '',
      providerEmail: '',
      shelvePubNet: true,
      pubNetCallAddr: '',
      priNetCallAddr: '',
    },
    //step2 功能演示
    material: {
      needToShow: true,
      type: '', //类型
      materials: [],
    },
    //step3 功能介绍
    intro: {
      //功能介绍
      funcIntros: [
        {
          description: '',
          img: '',
          intro: '',
        },
      ],
      //使用场景
      useCases: [
        {
          img: '',
          intro: '',
          name: '',
        },
      ],
    },
    //step4 //技术特色
    techFeatures: {
      //技术特色
      features: [
        {
          name: '',
          intro: '',
          img: '',
        },
      ],
      questions: [
        //常见问题
        {
          answer: '',
          question: '',
        },
      ],
      recommends: [], //相关推荐
    },
    //step5 推荐卡片
    recCard: {
      capabilityName: '', //本期不用
      capabilityIntro: '', //能力简介
      theFirstFuncIntro: '', //功能标签一
      theSecondFuncIntro: '', //功能标签二
    },
    //step6 技术文档
    techDoc: {
      content: '', //本期不用
      fileUrl: '', //本期不用
      needTechDoc: true,
    },
  },

  // 能力信息复位备份
  appInfoBackUp: {
    //step1 能力基本信息
    baseInfo: {
      type: 0,
      intro: '',
      name: '',
      providerAccount: '',
      providerId: 0,
      providerPhone: '',
      providerEmail: '',
      shelvePubNet: true,
      pubNetCallAddr: '',
      priNetCallAddr: '',
    },
    //step2 功能演示
    material: {
      needToShow: true,
      type: '', //类型
      materials: [],
    },
    //step3 功能介绍
    intro: {
      //功能介绍
      funcIntros: [
        {
          description: '',
          img: '',
          intro: '',
        },
      ],
      //使用场景
      useCases: [
        {
          img: '',
          intro: '',
          name: '',
        },
      ],
    },
    //step4 //技术特色
    techFeatures: {
      //技术特色
      features: [
        {
          name: '',
          intro: '',
          img: '',
        },
      ],
      questions: [
        //常见问题
        {
          answer: '',
          question: '',
        },
      ],
      recommends: [], //相关推荐
    },
    //step5 推荐卡片
    recCard: {
      capabilityName: '', //本期不用
      capabilityIntro: '', //能力简介
      theFirstFuncIntro: '', //功能标签一
      theSecondFuncIntro: '', //功能标签二
    },
    //step6 技术文档
    techDoc: {
      content: '', //本期不用
      fileUrl: '', //本期不用
      needTechDoc: true,
    },
  },

  // 模态框是否开启
  modalVisible: false,
  // 是否中途保存开放能力的草稿
  isSaveDraft: false,
  // 中途要跳转页面的路径
  toPath: '',
  showGlobalLoading: false,
  // 能力管理的上传文件
  defaultMediaUrlList: [],
  // keycloak token
  refreshToken: '',
};
export default state;
