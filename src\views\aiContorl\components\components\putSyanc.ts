import { message } from 'ant-design-vue';
import request from '@/request';
import getSaveOPtions from './optionsSave';
export function usePutSyancTask(store, router, capacityButton, optionsUse) {
  //step = 1
  function putSyancTask() {
    store.commit('openGlobalLoading');
    const options = getSaveOPtions(store, optionsUse);
    request('/aiipweb/om-service/capability/on-sync', {
      method: 'POST',
      data: options,
    }).then((res: any) => {
      store.commit('closeGlobalLoading');
      const abilityName = store.state.appInfo.baseInfo.name;

      if (res.state === 'OK') {
        message.success(capacityButton.value === '1' || capacityButton.value === undefined ? `【${abilityName}】能力上架操作成功，上架结果将通过站内信发送，请耐心等待` : `【${abilityName}】已同步到开放能力`);
        router.push({ path: '/capacity-management' });
      } else if (res.errorMessage === '能力名称重复') {
        message.error(`【${abilityName}】能力名称重复，请修改名称后重试`);
        return;
      } else {
        message.error(capacityButton.value === '1' || capacityButton.value === undefined ? `【${abilityName}】能力上架失败,将自动为您保存成草稿` : `【${abilityName}】未能同步到开放能力,将自动为您保存成草稿`);
        return;
      }
    });
  }
  return { putSyancTask };
}
