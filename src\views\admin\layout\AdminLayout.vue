<template>
  <div>
    <a-layout class="layout">
      <admin-header />
      <a-layout class="layout-main">
        <a-layout-sider>
          <p class="title">账号管理</p>
          <side-menu></side-menu>
        </a-layout-sider>
        <a-layout-content class="layout-content">
          <div class="main-content">
            <user-center />
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>
    <jt-feedback appName="adminFeedback" v-if="showFeatureTicket"></jt-feedback>
  </div>
</template>
<script>
import AdminHeader from './AdminHeader';
import sideMenu from './SideMenu.vue';
import UserCenter from '../userCenter/UserCenter.vue';
import JtFeedback from '../components/feedback.vue';
import { getEnvConfig } from '@/config';

export default {
  name: 'Layout',
  components: {
    AdminHeader,
    sideMenu,
    UserCenter,
    JtFeedback,
  },
  data() {
    return {
      collapsed: false,
      footerStyle: {
        background: '#002633',
      },
    };
  },
  computed: {
    showFeatureTicket() {
      return getEnvConfig('SHOW_FEATURE_TICKET') === '1';
    },
  },
  methods: {
    toggleCollapsed() {
      this.collapsed = !this.collapsed;
    },
  },
};
</script>

<style lang="less" scoped>
.layout {
  min-height: 100vh;
  background: url('~@/assets/images/admin-background.png') left / cover no-repeat;
}
.layout-main {
  padding-top: 50px;
  transition: all 0.3s;
  background-color: transparent;

  .ant-layout-sider {
    background-color: transparent;
  }

  .main-content {
    flex-grow: 1;
    margin: 20px 20px 40px;
    border-radius: 2px;
  }
}
.title {
  margin: 0;
  margin-left: 24px;
  padding-left: 24px;
  width: 176px;
  height: 58px;
  font-size: 14px;
  font-weight: 500;
  color: #16161c;
  line-height: 60px;
}
.main-content {
  min-height: calc(100vh - 120px);
}
</style>
