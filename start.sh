#!/bin/bash
sed -i  "s|<body>|<body keycloakUrl=\"$KEYCLOAK_URL\" capabilityInnerVersion=\"$CAPABILITY_INNER_VERSION\" keycloakClientId=\"$KEYCLOAK_CLIENT_ID\" keycloakLogoutPrefix=\"$KEYCLOAK_LOGOUT_PREFIX\" useUserIdentity=\"$USE_USER_IDENTITY\" contactEmail=\"$CONTACT_EMAIL\" showRequirmentIcon=\"$SHOW_REQUIRMENT_ICON\" showFeatureTicket=\"$SHOW_FEATURE_TICKET\" COMMONMANAGEMENTUSERMANAGEMENTAVAILABLEOPERATIONS=\"$COMMON_MANAGEMENT_USER_MANAGEMENT_AVAILABLE_OPERATIONS\" MESSAGEURLPATH=\"$MESSAGE_URL_PATH\" FEATUREMESSAGECENTER=\"$FEATURE_MESSAGECENTER\" FEATURESMS=\"$FEATURE_SMS\" SHOWDEMANDICON=\"$SHOW_DEMAND_ICON\" ADMINHEADERHOMEPAGELINK=\"$ADMIN_HEADER_HOMEPAGE_LINK\" USERINFOPHONEEDITABLE=\"$USER_INFO_PHONE_EDITABLE\" USERLOGINOPTION=\"$USER_LOGIN_OPTION\" SHOWACCOUNTMANAGEMENT=\"$SHOW_ACCOUNT_MANAGEMENT\">|" ${TOMCAT_BASE_DIR}/webapps/ROOT/index.html
/bin/bash ${TOMCAT_BASE_DIR}/bin/catalina.sh run 
