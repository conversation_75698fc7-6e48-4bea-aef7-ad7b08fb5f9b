import { getEnvConfig } from '@/config';

const operations = getEnvConfig('COMMON_MANAGEMENT_USER_MANAGEMENT_AVAILABLE_OPERATIONS').split(',');
const userlogin = getEnvConfig('USER_LOGIN_OPTION');

export const auths = {
  create: operations.includes('create'),
  edit: operations.includes('edit'),
  batch_import: operations.includes('batch_import'),
  batch_outport: operations.includes('batch_outport'),
};

export const isLoginType = {
  /**
     * <AUTHOR>
     * 互联网生产和办公网生产：USER_LOGIN_OPTION: "smscode"    表示仅支持短信验证码登录
        测试环境：USER_LOGIN_OPTION: "password,smscode"表示支持密码登录和短信验证码登录
        如果取值只有password，那就是只支持密码登录  多个值用英文逗号分隔
     */
  isOnlySmscode() {
    const isOnlySmscode = /^smscode$/.test(userlogin);
    return isOnlySmscode;
  },
  isOnlyPassword() {
    const isOnlyPassword = /^password$/.test(userlogin);
    return isOnlyPassword;
  },
};
