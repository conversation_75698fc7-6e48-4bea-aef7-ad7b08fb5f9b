<template>
  <div class="message-control-container">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '站内信发布', query: $route.query }">站内信发布</router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>站内信详情</a-breadcrumb-item>
    </a-breadcrumb>
    <Container>
      <container-item>
        <div class="top-bar">
          <h1>基本信息</h1>
          <mail-status-tag :mailStatus="mailInfo.mailStatus" class="mail-status-tag" />
          <a-space>
            <a-button v-if="showDelete" @click="showModal('删除')" type="danger" ghost>删除</a-button>
            <a-button v-if="showSend" @click="showModal('发送')" type="primary">发送</a-button>
            <a-button v-if="showStop" @click="showModal('终止发送')" type="primary" ghost>终止发送</a-button>
          </a-space>
        </div>
        <div class="mail-info">
          <a-row>
            <a-col :span="8">
              <span>创建人：</span><span>{{ handleInfo(mailInfo.creator) }}</span>
            </a-col>
            <a-col :span="8">
              <span>发送人：</span><span>{{ handleInfo(mailInfo.sender) }}</span>
            </a-col>
            <a-col :span="8">
              <span>终止人：</span><span>{{ handleInfo(mailInfo.cancelOperator) }}</span>
            </a-col>
            <a-col :span="8">
              <span>创建时间：</span><span>{{ handleInfo(mailInfo.creatTime) }}</span>
            </a-col>
            <a-col :span="8">
              <span>发送时间：</span><span>{{ handleInfo(mailInfo.sendTime) }}</span>
            </a-col>
            <a-col :span="8">
              <span>终止时间：</span><span>{{ handleInfo(mailInfo.cancelTime) }}</span>
            </a-col>
            <a-col :span="8">
              <span>发送对象：</span><span>{{ SENDER_TYPE_MSG[mailInfo.isGlobal] }}({{ handleInfo(mailInfo.userTotal) }})</span>
              <a-button class="btn-send-export" type="link" @click="exportUserList()" v-if="mailInfo.isGlobal === SENDER_TYPE.ASSIGN">导出用户列表</a-button>
            </a-col>
            <a-col :span="8">
              <span>
                <a-tooltip :overlayStyle="{ maxWidth: '150px' }">
                  <template #title>用户登录后，拉取注册时间后的站内信</template>
                  <question-circle-outlined style="width: 14px" />
                </a-tooltip>
                收取数：</span
              >
              <span>{{ handleInfo(mailInfo.sendCount) }}</span>
            </a-col>
            <a-col :span="8">
              <span>阅读数：</span><span>{{ handleInfo(mailInfo.readCount) }}</span>
            </a-col>
          </a-row>
        </div>
      </container-item>
      <container-item>
        <div class="top-bar">
          <h1>站内信内容</h1>
          <a-space>
            <a-button><router-link target="_blank" :to="{ path: `/message-management/messageControl-preview/${$route.query.mailId}`, query: $route.query }">预览</router-link></a-button>
            <a-button v-if="showEdit" @click="goEdit" type="primary" ghost>编辑</a-button>
          </a-space>
        </div>
        <div>
          <a-row>
            <a-col :span="24">
              <span>标题：</span><span>{{ handleInfo(mailInfo.mailTitle) }}</span>
            </a-col>
            <a-col :span="24">
              <span>类型：</span><span>{{ handleInfo(mailTypeMaps[mailInfo.mailType]) }}</span>
            </a-col>
            <a-col class="mail-content" :span="24">
              <span>内容：</span>
              <div v-html="mailInfo.mailContent" class="document-content markdown-body"></div>
            </a-col>
          </a-row>
        </div>
      </container-item>
    </Container>
    <confirm-dlg :visible="visible">
      <template #title>
        <div class="modal-title">
          <ExclamationCircleFilled :class="[{ 'danger': titleText !== '发送' }]" class="title-icon" />
          <span>确定{{ titleText }}站内信吗？</span>
        </div>
      </template>
      <template #content>
        <div v-if="titleText === '发送'" class="modal-content">
          <p>发送后将无法撤回，请谨慎操作</p>
        </div>
        <div v-else class="modal-content">
          <p>标题：{{ mailInfo.mailTitle }}</p>
          <p v-if="titleText === '删除'">删除后将不可恢复，请谨慎操作。</p>
          <p v-else>终止后未收取的用户将不再收取此站内信，请谨慎操作。</p>
        </div>
      </template>
      <template #footer>
        <div class="modal-footer">
          <a-button v-if="titleText != '发送'" @click="handleMail" type="primary" danger>{{ titleText === '删除' ? '删除' : '终止' }}</a-button>
          <a-button @click="visible = false" style="margin-left: 8px">取消</a-button>
          <a-button v-if="titleText === '发送'" @click="handleMail" type="primary" style="margin-left: 8px">立即发送</a-button>
        </div>
      </template>
    </confirm-dlg>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { message } from 'ant-design-vue';
import { MAIL_STATUS, SENDER_TYPE, SENDER_TYPE_MSG } from '@/constants';
import { MessageCenter } from '@/apis';
import { downloadFile } from '@/utils/file';
import { ExclamationCircleFilled, QuestionCircleOutlined } from '@ant-design/icons-vue';
import mailStatusTag from './components/mailStatusTag.vue';
import confirmDlg from '@/components/confirmDlg.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';

const { cancelMail, deleteMail, getDetail, getMailType, sendMail, exportSuccessUser } = MessageCenter;

export default defineComponent({
  components: { QuestionCircleOutlined, mailStatusTag, ExclamationCircleFilled, Container, ContainerItem, confirmDlg },
  data() {
    return {
      mailInfo: {} as any,
      // mailStatusMaps,
      mailTypeMaps: [],
      SENDER_TYPE,
      SENDER_TYPE_MSG,
      visible: false,
      titleText: '',
      postData: {
        mailType: null,
        mailStatus: null,
      },
    };
  },
  created(this: any) {
    this.getMailInfo();
    this.getMailType();
  },
  computed: {
    showDelete(this: any) {
      return this.mailInfo.mailStatus === MAIL_STATUS.DRAFT;
    },
    showSend(this: any) {
      return this.mailInfo.mailStatus === MAIL_STATUS.FAILED || this.mailInfo.mailStatus === MAIL_STATUS.DRAFT;
    },
    showStop(this: any) {
      return this.mailInfo.mailStatus === MAIL_STATUS.SENT && this.mailInfo.sendCount != this.mailInfo.userTotal;
    },
    showEdit(this: any) {
      return this.mailInfo.mailStatus === MAIL_STATUS.DRAFT || this.mailInfo.mailStatus === MAIL_STATUS.FAILED;
    },
  },
  methods: {
    handleInfo(v) {
      return v ? v : '--';
    },
    async getMailType(this: any) {
      const res: any = await getMailType({});
      const { state, body } = res || {};
      if (state === 'OK') {
        for (let i = 0; i < body.length; i++) {
          this.mailTypeMaps[body[i].mailTypeId] = body[i].mailTypeName;
        }
      }
    },
    async getMailInfo(this: any) {
      const res: any = await getDetail({ data: { mailId: this.$route.query.mailId } });
      if (res.state === 'OK') {
        this.mailInfo = res.body;
      }
    },
    showModal(this: any, text: string) {
      this.titleText = text;
      this.visible = true;
    },
    async handleMail(this: any) {
      const action = this.titleText;
      let mailTitle = this.mailInfo.mailTitle;
      const mailId = this.$route.query.mailId;
      let res: any;
      if (action === '发送') {
        res = await sendMail({ type: 'POST', data: mailId });
      } else if (action === '删除') {
        res = await deleteMail({ data: { mailId } });
      } else {
        res = await cancelMail({ data: { mailId } });
      }
      if (mailTitle.length > 8) mailTitle = mailTitle.substring(0, 4) + '...' + mailTitle.slice(-4);
      if (res.state === 'OK') {
        if (action === '删除') {
          this.goBack();
        } else {
          this.getMailInfo();
        }
        message.success(`站内信【${mailTitle}】${this.titleText === '发送' ? '提交审核' : this.titleText}成功！`);
      } else {
        message.error(`站内信【${mailTitle}】${this.titleText === '发送' ? '提交审核' : this.titleText}失败，请稍后再试！`);
      }
      this.visible = false;
    },
    goBack(this: any) {
      this.$router.push({ path: '/message-management', query: this.$route.query });
    },
    goEdit() {
      this.$router.push({
        path: '/message-management/messageControl-edit',
        query: {
          ...this.$route.query,
          mailId: this.$route.query.mailId,
        },
      });
    },
    exportUserList() {
      const url = `${exportSuccessUser}?mailId=${this.$route.query.mailId}`;
      downloadFile({ url });
    },
  },
});
</script>
<style lang="less" scoped>
.nav {
  height: 58px;
  line-height: 58px;
  padding-left: 20px;
  background: #ffffff;
  font-weight: 500;
  color: #121f2c;
}
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  h1 {
    margin-bottom: 16px;
  }
  .mail-status-tag {
    position: absolute;
    top: 0px;
    left: 77px;
  }
}
.ant-row {
  padding-left: 64px;
  .ant-col {
    margin-bottom: 24px;
  }
}
.mail-info {
  .ant-col span:nth-of-type(1) {
    display: inline-block;
    width: 100px;
    text-align: right;
    color: #606972;
  }
  .btn-send-export {
    height: 10px;
    padding: 0 15px;
    line-height: 1;
  }
}
.mail-content {
  position: relative;
  line-height: 1.5715;
  .document-content {
    display: inline-block;
    word-wrap: break-word;
    width: 704px;
    flex-wrap: wrap;
    vertical-align: top;
  }
}
.preview-top {
  text-align: center;
  & > p:nth-of-type(1) {
    font-size: 18px;
    font-weight: 500;
    color: #121f2c;
    margin-bottom: 8px;
  }
  & > p:nth-of-type(2) {
    font-size: 12px;
    color: #606972;
    margin: 0;
  }
}
.preview-bottom {
  padding: 0 36px;
}
</style>
