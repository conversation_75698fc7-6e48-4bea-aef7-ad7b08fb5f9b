<template>
  <div>
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '平台文档管理', query: { platformName, platformCode } }"> 平台文档管理 </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>{{ pageId ? '编辑' : '新建' }}页面</a-breadcrumb-item>
    </a-breadcrumb>
    <a-spin :spinning="isLoding">
      <Container>
        <container-item>
          <h3>{{ pageId ? '编辑' : '新建' }}页面</h3>
          <div class="create-help-body">
            <a-form :model="formState" ref="formRef" :rules="rules" v-bind="layout" :colon="false">
              <a-form-item label="平台" required>
                <a-input v-model:value="platformName" disabled />
              </a-form-item>
              <a-form-item label="标题" name="pageName">
                <a-input v-model:value="formState.pageName" @blur="pageNameCheck($event)" placeholder="请输入" />
              </a-form-item>
              <a-form-item label="父目录" name="parentCatalog">
                <a-tree-select v-model:value="formState.parentCatalog" show-search :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }" placeholder="请选择父目录" allow-clear tree-default-expand-all>
                  <a-tree-select-node v-for="item in catalogList" :key="item.catalogId" :value="item.catalogId" :title="item.catalogName">
                    <template v-if="item.catalogList">
                      <a-tree-select-node v-for="child in item.catalogList" :key="child.catalogId" :value="child.catalogId" :title="child.catalogName"></a-tree-select-node>
                    </template>
                  </a-tree-select-node>
                </a-tree-select>
              </a-form-item>
              <a-form-item label="内容" name="pageContentIndex">
                <text-editor :initParams="initParams" centerType="HELP" :editorContent="editorContent.value" @change="handleEditorChange" />
              </a-form-item>
              <a-form-item label=" ">
                <a-space size="middle">
                  <a-button @click="handleSave" type="primary">保存</a-button>
                  <a-button type="outline" @click="handleCancel">取消</a-button>
                </a-space>
              </a-form-item>
            </a-form>
          </div>
        </container-item>
      </Container>
    </a-spin>
  </div>
</template>

<script lang="ts">
import { defineComponent, UnwrapRef, reactive, ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import TextEditor from '@/components/editor/index.vue';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import { message } from 'ant-design-vue';
import { useStore } from 'vuex';
import { HelpCenter } from '@/apis';
const { getCatalogList, addPage, updatePage, getDetail, isPageExist } = HelpCenter;
const tagsList = ['p', 'li', 'div', 'blockquote', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
interface FormState {
  pageName: string | undefined;
  parentCatalog: number | undefined; // 当为空字符串时，为最顶级别目录
  content: string | undefined;
  pageContentIndex: string | undefined;
}

interface CatalogList {
  catalogId: number | string;
  catalogName: string;
  catalogList: any[] | null;
}

export default defineComponent({
  components: {
    TextEditor,
    Container,
    ContainerItem,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const formRef = ref();
    const isLoding = ref(false);
    const store = useStore();
    const pageId = route.query.pageId;
    const platformName = route.query.platformName;
    const platformCode = route.query.platformCode;
    const editorContent: any = reactive({
      value: '',
    });
    const refreshToken = store.state.refreshToken;
    const pageNameRepetition = ref(false) as any;
    const breadCrumbs = [{ name: '帮助中心管理', path: '/helpManage' }, { name: `${pageId ? '编辑' : '新建'}页面` }];
    const formState: UnwrapRef<FormState> = reactive({
      pageName: undefined,
      parentCatalog: undefined,
      content: undefined,
      pageContentIndex: undefined,
    });
    let catalogList = ref<CatalogList[]>([]);
    let initPageName: string | undefined = undefined; // 编辑时的页面名称，记录下，编辑完成后提示的时候需要
    let pageHeadList: any[] = [];
    const layout = {
      labelCol: { span: 2 },
      wrapperCol: { span: 22 },
    };
    const initParams: any = {
      uploadImgShowBase64: false,
      uploadFileName: 'file',
      uploadImgServer: './common_help/image/add',
      uploadImgTimeout: 20 * 1000,
      uploadImgHeaders: {
        Authorization: 'Bearer ' + refreshToken,
      },
    };
    const handleEditorChange = (v) => {
      formState.content = v.content;
      formState.pageContentIndex = v.pageContentIndex;
      pageHeadList = v.pageHeadList;
      // 手动触发整个表单校验
      formRef.value.validate();
    };
    const validatePageContent = (rule, value) => {
      if (value || (!value && formState.content)) {
        return Promise.resolve();
      } else {
        return Promise.reject('请输入页面内容');
      }
    };
    const rules = {
      pageName: [
        { required: true, whitespace: true, trigger: 'change', message: '请输入页面标题' },
        {
          message: '不超过20个字符',
          max: 20,
        },
      ],
      parentCatalog: [{ required: true, message: '请选择父目录' }],
      pageContentIndex: [
        { required: true, trigger: 'change', validator: validatePageContent, message: '请输入页面内容' },
        { max: 20000, trigger: 'change', message: '不超过20000个字符' },
      ],
    };

    const handleCancel = () => {
      router.push({ path: '/platform-document', query: { platformName, platformCode } });
    };
    // 查询页面标题是否已存在
    const pageNameCheck = (e) => {
      isPageExist({
        name: 'HELP',
        type: 'POST',
        data: { accessPlatformCode: platformCode, pageName: formState.pageName, pageId: pageId ? Number(pageId) : null },
      }).then((res: any) => {
        if (res.state === 'OK') {
          if (res.body) message.error('页面标题重复');
          pageNameRepetition.value = res.body;
        }
      });
    };

    // 新建或编辑页面
    const handleSave = () => {
      formRef.value.validate().then(async () => {
        if (pageNameRepetition.value) {
          message.error('页面标题重复');
          return;
        }
        isLoding.value = true;
        // 需要注意pageId为0的情况
        const params = {
          name: 'HELP',
          type: 'POST',
          data: {
            accessPlatformCode: platformCode,
            catalogId: formState.parentCatalog,
            pageContent: formState.content,
            pageContentIndex: formState.pageContentIndex,
            pageName: formState.pageName,
            titles: pageHeadList,
          },
        };
        const notPageId = pageId === '' || pageId === undefined;
        const res: any = notPageId ? await addPage(params) : await updatePage({ ...params, data: { ...params.data, pageId: Number(pageId) } });
        if (res.state === 'OK') {
          notPageId ? message.success(`页面"${formState.pageName}"创建成功`) : message.success(`页面"${initPageName}"编辑成功`);
          router.push({ path: '/platform-document', query: { platformName, platformCode, confirmLeave: 1 } });
        }
      });
    };

    // 根据json结构进行换行字符串的拼接
    const formatTxtByJson = (data) => {
      let str = '';
      if (data.length === 0) {
        return str;
      }
      data.forEach((item) => {
        if (item.children && item.children.length > 0) {
          const paraStr = formatTxtByJson(item.children);
          if (paraStr) {
            str += paraStr;
            if (tagsList.includes(item.tag)) {
              str += '\n';
            }
          }
        } else {
          // 字符拼接处理
          if (typeof item === 'string') {
            str += item.replaceAll('<br/>', '');
          }
        }
      });
      return str;
    };

    const initParentList = async () => {
      getCatalogList({ name: 'HELP', data: { flag: '2', accessPlatform: route.query.platformCode } }).then((res: any) => {
        if (res.state === 'OK') {
          let data: CatalogList[] = res.body as any;
          // catalogId设置为undefine的时候表单校验会认为该项为空
          data.unshift({ catalogId: 0, catalogName: '无', catalogList: null });
          catalogList.value = data;
        }
      });
    };

    const getPageById = async () => {
      if (pageId) {
        await getDetail({ name: 'HELP', data: { pageId: Number(pageId) } }).then((res: any) => {
          if (res.state === 'OK') {
            const { catalogId, pageContent, pageName, titles, pageContentIndex } = res.body;
            formState.pageName = pageName;
            initPageName = pageName;
            formState.parentCatalog = catalogId;
            editorContent.value = pageContent;
            formState.pageContentIndex = pageContentIndex;
            pageHeadList = titles;
          }
        });
      }
    };

    onMounted(() => {
      getPageById();
      initParentList();
    });
    return {
      breadCrumbs,
      formState,
      layout,
      rules,
      pageId,
      platformName,
      platformCode,
      formRef,
      catalogList,
      handleCancel,
      pageNameCheck,
      handleSave,
      isLoding,
      initParams,
      handleEditorChange,
      editorContent,
    };
  },
});
</script>

<style lang="less" scoped>
.nav {
  height: 58px;
  line-height: 58px;
  padding-left: 20px;
  background: #ffffff;
  font-weight: 500;
  color: #121f2c;
}
.create-help-body {
  width: 80%;
  padding-top: 20px;
}
.spin {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 111;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}
</style>
