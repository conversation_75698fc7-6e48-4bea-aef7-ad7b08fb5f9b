<template>
  <div>
    <a-modal :visible="visible" :width="360" title="请确认" @cancel="handleClose" :maskClosable="false">
      <template #footer>
        <div class="foot-box">
          <a-button @click="handleCancel">直接离开</a-button>
          <a-button type="primary" @click="handleOk">保存并离开</a-button>
        </div>
      </template>
      <div class="content">
        <img :src="iconSrc" class="content-icon" alt="" />
        <div class="content-title">确定离开当前页面吗？</div>
        <div class="content-sub">请确认已将能力信息保存为草稿</div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(['handleOk', 'handleCancel', 'handleClose']);
const iconSrc = require('@/assets/images/aiControl/icon_close_modal.png');

const handleOk = () => {
  emits('handleOk');
};
const handleCancel = () => {
  emits('handleCancel');
};
const handleClose = () => {
  emits('handleClose');
};
</script>

<style lang="less" scoped>
.foot-box {
  padding: 6px 0;
}
.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  &-icon {
    width: 32px;
    height: 32px;
    margin-bottom: 15px;
  }
  &-title {
    font-size: 16px;
    font-weight: 500;
    color: #121f2c;
    margin-bottom: 8px;
  }
  &-sub {
    font-size: 14px;
    font-weight: 400;
    color: #606972;
  }
}
</style>
