<template>
  <div class="abilityRecommend">
    <div class="abilityRecommend-left">
      <div class="abilityRecommend-title">能力推荐卡片</div>
      <div class="abilityRecommend-text">
        <jt-icon type="iconwarning-circle-fill" style="color: #b0d5ff; font-size: 16px; margin-right: 9px" />
        <span>能力的推荐卡片将在九天人工智能平台门户页的开放能力板块展示，以及可能在其他能力详情页的相关推荐中展示</span>
      </div>
      <div class="abilityRecommend-form">
        <a-form ref="formAbilityRef" :model="formData" :layout="'vertical'" autocomplete="off">
          <a-form-item
            class="form-desc"
            label="能力介绍"
            name="introduce"
            :rules="[
              {
                required: true,
                message: '',
                trigger: 'change',
                validator: validatePass,
              },
            ]"
          >
            <a-textarea class="form-textarea" style="width: 528px; margin-right: 160px" v-model:value="formData.introduce" :rows="4" placeholder="请输入" />
            <div v-if="descError" class="desc-tips">{{ descErrorMessage }}</div>
            <div class="desc-count">
              <span class="desc-num" :class="descError ? 'desc-error' : ''">{{ formData.introduce.length }}</span
              >/
              <span class="count-num">80</span>
            </div>
          </a-form-item>
          <a-form-item
            class="form-box-item"
            label="功能标签一"
            name="intro1"
            :rules="[
              {
                required: true,
              },
            ]"
          >
            <a-select v-model:value="formData.intro1" :optionFilterProp="'label'" :options="optionsIntro1" show-search allowClear placeholder="请选择" style="width: 190px; height: 32px" :getPopupContainer="(triggerNode) => triggerNode.parentNode"> </a-select>
          </a-form-item>
          <a-form-item
            class="selectInput"
            label="功能标签二"
            name="intro2"
            :rules="[
              {
                required: true,
              },
            ]"
          >
            <a-select v-model:value="formData.intro2" :optionFilterProp="'label'" :options="optionsIntro2" show-search allowClear placeholder="请选择" style="width: 190px; height: 32px" :getPopupContainer="(triggerNode) => triggerNode.parentNode"> </a-select>
          </a-form-item>
        </a-form>
      </div>
      <div class="abilityRecommend-but">
        <div class="iconfanhui" @click="toPre">
          <jt-icon type="iconfanhui" style="font-size: 20px; color: #7f828f" />
        </div>

        <a-button class="next" type="primary" @click="toNext">下一步</a-button>
        <a-button class="save" type="default" @click="saveDraft">保存</a-button>
      </div>
    </div>
    <div class="abilityRecommend-right">
      <div class="abilityRecommend-right-netCall" @click="capabilityInnerVersion"><img src="@/assets/images/messageControl/netcall.png" alt="" /> 查看现网</div>
      <div class="abilityRecommend-right-title">
        <p class="abilityRecommend-right-title-box"></p>
        <p class="abilityRecommend-right-title-text">入驻能力页示意</p>
      </div>
      <div class="abilityRecommend-right-img">
        <img src="../../../assets/images/aiControl/step5.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, defineEmits, defineProps, computed, defineExpose, watch, onActivated, onDeactivated, nextTick } from 'vue';
import { useStore } from 'vuex';
import type { Rule } from 'ant-design-vue/es/form';
import { getEnvConfig } from '@/config';
const emit = defineEmits(['toPre', 'toNext', 'saveDraft']);
const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  appInfo: {
    type: Object,
    default() {
      return {};
    },
  },
});
const formAbilityRef = ref();
const descError = ref(false);
const descErrorMessage = ref('');
let validatePass = async (_rule: Rule, value: string) => {
  if (value.length === 0) {
    descError.value = true;
    descErrorMessage.value = '请输入';
    return Promise.reject();
  } else if (value.length > 80) {
    descError.value = true;
    descErrorMessage.value = '字数超过限制';
    return Promise.reject();
  } else {
    descError.value = false;
    descErrorMessage.value = '';
    return Promise.resolve();
  }
};

const store = useStore();
const formData = reactive({
  introduce: store.state.appInfo.baseInfo.intro, //能力介绍
  intro1: null, //能力推荐
  intro2: null,
});
const funcIntrosArr = computed(() => {
  return store.state.appInfo.intro.funcIntros
    .map((item: any) => {
      return item.intro;
    })
    .filter((item) => {
      return item != undefined || '';
    });
});
onActivated(() => {
  formAbilityRef.value.validateFields(['introduce']);

  const funcIntros = computed(() => {
    return store.state.appInfo.intro.funcIntros
      .map((item: any) => {
        return item.intro;
      })
      .filter((item) => {
        return item != undefined || '';
      });
  });
  const arr = funcIntros.value.map((item) => {
    return funcIntrosArr.value.includes(item);
  });
  console.log(arr, 111);

  const funcIntrosList = computed(() => {
    return store.state.capabilityIntroArr.map((item: any) => {
      return item;
    });
  });
  const arrAbility = funcIntros.value.map((item) => {
    return funcIntrosList.value.includes(item);
  });
  if (arr.includes(false) && arrAbility.includes(false)) {
    formData.intro1 = null;
    formData.intro2 = null;
  }
});
onDeactivated(() => {
  const funcIntros = computed(() => {
    return store.state.appInfo.intro.funcIntros
      .map((item: any) => {
        return item.intro;
      })
      .filter((item) => {
        return item != undefined || '';
      });
  });
  store.commit('capabilityIntroArr', funcIntros.value);
});
//推荐卡片选择器数据
const optionsIntro1 = computed(() => {
  return store.state.appInfo.intro.funcIntros
    .filter((item) => {
      return item.intro != '';
    })
    .map((item) => {
      if (item.intro === formData.intro2) {
        return {
          value: item.intro,
          label: item.label,
          disabled: true,
        };
      } else {
        return {
          value: item.intro,
          label: item.label,
          disabled: false,
        };
      }
    });
});
const optionsIntro2 = computed(() => {
  return store.state.appInfo.intro.funcIntros
    .filter((item) => {
      return item.intro != '';
    })
    .map((item) => {
      if (item.intro === formData.intro1) {
        return {
          value: item.intro,
          label: item.label,
          disabled: true,
        };
      } else {
        return {
          value: item.intro,
          label: item.label,
          disabled: false,
        };
      }
    });
});
const capabilityInnerVersion = () => {
  const config = getEnvConfig('CAPABILITY_INNER_VERSION') === 'true';

  if (config) {
    window.open('http://jiutian.hq.cmcc');
  } else {
    window.open('http://jiutian.10086.cn');
  }
};
const introduce = computed(() => {
  return store.state.appInfo.baseInfo.intro;
});
watch(
  () => props.isEdit,
  (newValue) => {
    if (newValue) {
      formData.introduce = props.appInfo.recCard.capabilityIntro;
      formData.intro1 = props.appInfo.recCard.theFirstFuncIntro;
      formData.intro2 = props.appInfo.recCard.theSecondFuncIntro;
      nextTick(() => {
        formAbilityRef.value.validateFields(['introduce', 'intro1', 'intro2']);
      });
    }
  },
  {
    immediate: true,
  }
);
// 下一步可点击的状态
const valid = computed(() => {
  return formData.introduce && formData.introduce.length <= 80 && formData.intro1 && formData.intro2;
});
const toPre = () => {
  abilityRecommendStore();
  emit('toPre');
};

const abilityRecommendStore = () => {
  store.commit('capabilityIntro', formData.introduce);
  store.commit('theFirstFuncIntro', formData.intro1);
  store.commit('theSecondFuncIntro', formData.intro2);
};
const toNext = () => {
  if (valid.value) {
    abilityRecommendStore();
    emit('toNext');
  } else {
    if (formData.introduce == '' || formData.intro1 == '' || formData.intro1 == null || formData.intro2 == '' || formData.intro2 == null) {
      formAbilityRef.value.validateFields(['introduce', 'intro1', 'intro2']);
    }
  }
};

const saveDraft = () => {
  abilityRecommendStore();
  emit('saveDraft');
};
defineExpose({
  abilityRecommendStore,
});
</script>

<style lang="less" scoped>
.abilityRecommend {
  background: #fff;
  margin-top: 20px;
  padding: 20px;
  display: flex;
  height: 980px;
  justify-content: space-between;
  &-left {
    width: 809px;
  }
  &-title {
    font-size: 16px;
    font-weight: 500;
    color: #121f2c;
    line-height: 24px;
    margin-bottom: 28px;
  }
  &-text {
    width: 769px;
    height: 34px;
    background: #edf7ff;
    border-radius: 2px;
    border: 1px solid #b0d5ff;
    font-size: 12px;
    font-weight: 400;
    color: #666666;
    display: flex;
    align-items: center;
    padding-left: 17px;
    margin-bottom: 30px;
  }
  &-form {
    /deep/.ant-form {
      display: flex;
      flex-wrap: wrap;
    }
  }
  /deep/.ant-input {
    resize: none;
  }
  &-but {
    display: flex;
    width: 190px;
    justify-content: space-between;
    .iconfanhui {
      width: 32px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #d6d9db;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
    }
  }
  &-right {
    // height: 536px;
    width: 435px;
    border-left: 1px solid #efefef;
    margin-top: 52px;
    padding: 0 0 0 20px;
    position: relative;
    &-netCall {
      width: 100px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #0082ff;
      font-size: 12px;
      font-weight: 400;
      color: #0082ff;
      line-height: 18px;
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      position: absolute;
      left: 334px;
      top: -52px;
      &:hover {
        cursor: pointer;
      }
    }
    &-title {
      display: flex;
      align-items: center;
      &-box {
        width: 4px;
        height: 16px;
        background: #0082ff;
        margin-right: 8px;
      }
      &-text {
        height: 24px;
        font-size: 14px;
        font-weight: 500;
        color: #121f2c;
      }
    }
    &-img {
      // width: 350px;
      height: 850px;
      img {
        height: 100%;
        width: 100%;
      }
    }
  }
}
.selectInput {
  /deep/.ant-select-show-search.ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-left: none;
  }
}
.form-desc {
  position: relative;
  .desc-tips {
    position: absolute;
    margin-top: 5px;
    height: 18px;
    font-size: 14px;
    font-weight: 400;
    color: #ff454d;
    line-height: 18px;
  }
  .desc-count {
    position: absolute;
    right: 160px;
    margin-top: 5px;
    height: 18px;
    font-size: 14px;
    font-weight: 400;
    color: #a0a6ab;
    line-height: 18px;
    .desc-error {
      color: #ff454d;
    }
    .desc-num {
      margin-right: 4px;
    }
    .desc-count {
      margin-left: 2px;
    }
  }
}
</style>
