<template>
  <div class="jiutian-footer">
    <div class="footer-container">
      <div class="jiutian-intro-text">
        <div style="display: flex; color: #ffffff; font-size: 14px; align-items: center">
          <p class="email">联系我们</p>
          <p class="email"><a-icon type="mail" style="margin-right: 8px" /> <EMAIL></p>
        </div>

        <div class="logo-container">
          <img src="@/assets/images/<EMAIL>" alt="" />
          <p style="font-size: 12px; color: #a0a6ab; margin: 0 0 0 21px">Copyright © 2022 中国移动 版权所有</p>
        </div>
      </div>
      <div class="jiutian-qr-code">
        <img src="@/assets/images/二维码.png" alt="" />
        <span>九天人工智能公众号</span>
      </div>
    </div>
  </div>
</template>
<script>
import { getEnvConfig } from '@/config';
export default {
  name: 'Footer',
  data() {
    return {
      showRecordNumber: getEnvConfig('SHOW_RECORD_NUMBER') === '1',
    };
  },
};
</script>
<style lang="less" scoped>
.jiutian-footer {
  background: #002633;
  width: 100%;
  height: 280px;
  .footer-container {
    padding-top: 56px;
    width: 1200px;
    margin: auto;
    display: flex;
    justify-content: space-between;
    .jiutian-intro-text {
      color: #ffffff;
      .email {
        padding: 8px 0px;
      }
      .logo-container {
        display: flex;
        align-items: center;
        padding-top: 36px;
        img {
          height: 32px;
        }
        a {
          padding-left: 21px;
          font-size: 12px;
        }
      }
    }
    .jiutian-qr-code {
      padding-top: 16px;
      display: flex;
      flex-direction: column;
      img {
        width: 128px;
        height: 128px;
      }
      span {
        color: #a0a6ab;
        line-height: 20px;
        padding-top: 6px;
      }
    }
  }
}
</style>
