<template>
  <span :class="[{ 'fail-status': mailStatus == [MAIL_STATUS.FAILED] }, { 'succeed-status': mailStatus == [MAIL_STATUS.SENT] }, { 'draft-status': mailStatus == [MAIL_STATUS.DRAFT] }, { 'stop-status': mailStatus == [MAIL_STATUS.TERMINATED] }, { 'review-status': mailStatus == [MAIL_STATUS.REVIEWING] }]">{{ MAIL_STATUS_MSG[mailStatus] }}</span>
</template>

<script>
import { defineComponent } from 'vue';
import { MAIL_STATUS_MSG, MAIL_STATUS } from '@/constants';
export default defineComponent({
  props: {
    mailStatus: [String],
  },
  data() {
    return {
      MAIL_STATUS,
      MAIL_STATUS_MSG,
    };
  },
});
</script>

<style lang="less" scoped>
.mail-status {
  display: inline-block;
  height: 20px;
  line-height: 17px;
  border-radius: 2px;
  font-size: 12px;
  padding: 0 12px;
  border: 1px solid;
}
.succeed-status {
  background: #f0fff8;
  border-color: #68e3b4;
  color: #1dca94;
  .mail-status();
}
.fail-status {
  background: #fff2f0;
  border-color: #ffa099;
  color: #f24444;
  .mail-status();
}
.draft-status {
  background: #fef6e7;
  border-color: #ffd666;
  color: #faad14;
  .mail-status();
}
.stop-status {
  background: #f0f2f7;
  border-color: #c2c5cf;
  color: #7f828f;
  .mail-status();
}
.review-status {
  background: #ffa39e7a;
  border-color: #cf1322;
  color: #cf1322;
  .mail-status();
}
</style>
