<template>
  <div class="form-box" :class="props.num === '四' ? 'no-right' : ''">
    <a-form ref="appFormRef" :model="formData" :layout="'vertical'" autocomplete="off">
      <a-form-item class="title" :class="props.num === '四' ? 'no-required' : ''" :label="headline.title + num" :rules="[{ required: props.num === '四' ? false : true }]"> </a-form-item>

      <a-form-item
        class="intro-box"
        :label="headline.subtitle"
        name="intro"
        :rules="[
          {
            required: true,
            message: '',
            trigger: 'change',
            validator: validateIntro,
          },
        ]"
      >
        <a-input class="form-input" placeholder="请输入" v-model:value="formData.intro" :maxlength="8" />
        <div class="info-tips" :class="introError ? 'err-tips' : ''">8个字符以内</div>
      </a-form-item>

      <a-form-item
        class="form-desc"
        :class="isTextareaHigh ? 'form-desc-high' : ''"
        :label="headline.content"
        name="desc"
        :rules="[
          {
            required: true,
            message: '',
            trigger: 'change',
            validator: validateDesc,
          },
        ]"
      >
        <a-textarea :class="isTextareaHigh ? 'form-textarea-high' : 'form-textarea'" v-model:value="formData.desc" :rows="4" placeholder="请输入" />
        <div v-if="descError" class="desc-tips">{{ descErrorMessage }}</div>
        <div class="desc-count">
          <span class="desc-num" :class="descError ? 'desc-error' : ''">{{ formData.desc.length }}</span
          >/
          <span class="count-num">{{ fontNumber }}</span>
        </div>
      </a-form-item>

      <a-form-item
        v-if="showIcons"
        class="icon-box"
        :label="headline.icon"
        name="icon"
        :rules="[
          {
            required: true,
          },
        ]"
      >
        <span class="change" @click="handleChangeIcon">更改</span>
        <div class="img-box">
          <img :src="formData.icon" alt="" class="func-icon" />
        </div>
      </a-form-item>
    </a-form>
    <template v-if="visible">
      <selectIcon :visible="visible" :current="currentIndex" :icon-list="props.iconList" @handleOk="handleSelectIcon" @handleCancel="handleCancel"></selectIcon>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, watchEffect, defineEmits, defineExpose, defineProps, nextTick } from 'vue';
import selectIcon from './selectIcon.vue';

const props = defineProps({
  showIcons: {
    type: Boolean,
    default: true,
  },
  isTextareaHigh: {
    type: Boolean,
    default: false,
  },
  headline: {
    type: Object,
    default() {
      return {};
    },
  },
  fontNumber: {
    type: Number,
    default: 0,
  },
  needValid: {
    type: Boolean,
    default: true,
  },
  num: {
    type: String,
    default: '',
  },
  iconList: {
    type: Array,
    default() {
      return [];
    },
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Object,
    default() {
      return {};
    },
  },
});
const emits = defineEmits(['validChange']);
// 表单验证
const introError = ref(false);
const validateIntro = (rule, value) => {
  if (value.length === 0 || value.length > 8) {
    introError.value = true;
    return Promise.reject();
  } else {
    introError.value = false;
    return Promise.resolve();
  }
};
const descError = ref(false);
const descErrorMessage = ref('');
const validateDesc = (rule, value) => {
  if (value.length === 0) {
    descError.value = true;
    descErrorMessage.value = '请输入';
    return Promise.reject();
  } else if (value.length > props.fontNumber) {
    descError.value = true;
    descErrorMessage.value = '字数超过限制';
    return Promise.reject();
  } else {
    descError.value = false;
    descErrorMessage.value = '';
    return Promise.resolve();
  }
};

const formData = reactive({
  intro: '', // 介绍
  desc: '', // 简介
  icon: '', // 图标
});

const appFormRef = ref();
// 主动触发表单验证
const formValid = () => {
  if (props.needValid) {
    appFormRef.value.validateFields(['intro', 'desc']);
  } else {
    if (formData.intro !== '' || formData.desc !== '') {
      appFormRef.value.validateFields(['intro', 'desc']);
    }
  }
};
// 当前图标的索引
const currentIndex = ref(0);
// 监听图标库的变化
watch(
  () => props.iconList,
  (newValue: any) => {
    if (newValue.length > 0) {
      if (props.isEdit) {
        formData.icon = newValue.filter((item: any) => item?.key === props.editData.img)[0]?.value || newValue[0]?.value;
      } else {
        formData.icon = newValue[0]?.value as string;
      }
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
// 判断是否回显数据
watch(
  () => props.isEdit,
  (newValue) => {
    if (newValue) {
      formData.intro = props.editData.name === undefined ? props.editData.intro : props.editData.name;
      formData.desc = props.editData.description === undefined ? props.editData.intro : props.editData.description;
      formData.icon = (props.iconList as any).filter((item: any) => item?.key === props.editData.img)[0]?.value || (props.iconList[0] as any)?.value;
      // 数据回显，主动触发表单验证；
      nextTick(() => {
        formValid();
      });
    } else {
      formData.icon = (props.iconList[0] as any)?.value as string;
    }
  },
  {
    immediate: true,
  }
);

// 模态框的操作
const visible = ref<boolean>(false);
const handleSelectIcon = (index) => {
  formData.icon = (props.iconList[index] as any).value as string;
  currentIndex.value = index;
  visible.value = false;
};
const handleCancel = () => {
  visible.value = false;
};
const handleChangeIcon = () => {
  // 当前索引需要计算一下并赋值
  currentIndex.value = props.iconList.findIndex((i: any) => i.value === formData.icon) == -1 ? 0 : props.iconList.findIndex((i: any) => i.value === formData.icon);
  visible.value = true;
};

// 组件校验状态同步给父组件
watchEffect(() => {
  if (props.needValid) {
    if (formData.intro !== '' && formData.desc !== '' && formData.intro.length <= 8 && formData.desc.length <= props.fontNumber) {
      emits('validChange', true);
    } else {
      emits('validChange', false);
    }
  } else {
    if ((formData.intro === '' && formData.desc !== '') || (formData.intro !== '' && formData.desc === '')) {
      emits('validChange', false);
    } else if ((formData.intro === '' && formData.desc === '') || (formData.intro !== '' && formData.desc !== '' && formData.intro.length <= 8 && formData.desc.length <= props.fontNumber)) {
      emits('validChange', true);
      appFormRef.value?.clearValidate(['intro', 'desc']);
      introError.value = false;
      descError.value = false;
      descErrorMessage.value = '';
    } else {
      emits('validChange', false);
    }
  }
});
// 将组件中的数据暴露出去 供父组件使用; 触发验证暴露出去
defineExpose({
  formData,
  formValid,
});
</script>

<style lang="less" scoped>
.form-box {
  flex: 1;
  background: #fafafa;
  border-radius: 2px;
  margin-right: 20px;
  /deep/ .ant-form-item {
    padding-left: 12px;
    padding-right: 12px;
    margin-bottom: 24px;
  }
  .title {
    height: 44px;
    margin-left: 4px;
    margin-right: 4px;
    padding-left: 8px;
    border-bottom: 1px solid #efefef;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 500;
    color: #121f2c;
    line-height: 44px;
    /deep/ .ant-form-item-label {
      height: 44px;
      line-height: 44px;
    }
    /deep/ .ant-form-item-control {
      display: none;
    }
  }
  .no-required {
    padding-left: 19px;
  }
  .intro-box {
    height: 90px;
    position: relative;
    .info-tips {
      position: absolute;
      margin-top: 5px;
      height: 18px;
      font-size: 14px;
      font-weight: 400;
      color: #a0a6ab;
      line-height: 18px;
    }
    .err-tips {
      color: #ff454d;
    }
  }
  .icon-box {
    position: relative;
    .change {
      position: absolute;
      top: -30px;
      right: 0px;
      color: #0082ff;
      cursor: pointer;
    }
  }
  .img-box {
    width: 64px;
    height: 64px;
    padding: 8px;
    background: #f4f8ff;
    border-radius: 2px;
    .func-icon {
      width: 48px;
      height: 48px;
    }
  }
  .form-desc {
    position: relative;
    height: 220px;
    margin-bottom: 24px;
    .form-textarea {
      height: 154px;
    }
    .form-textarea-high {
      height: 254px;
    }
    /deep/ textarea {
      resize: none;
    }
    .desc-tips {
      position: absolute;
      margin-top: 5px;
      height: 18px;
      font-size: 14px;
      font-weight: 400;
      color: #ff454d;
      line-height: 18px;
    }
    .desc-count {
      position: absolute;
      right: 0;
      margin-top: 5px;
      height: 18px;
      font-size: 14px;
      font-weight: 400;
      color: #a0a6ab;
      line-height: 18px;
      .desc-error {
        color: #ff454d;
      }
      .desc-num {
        margin-right: 4px;
      }
      .desc-count {
        margin-left: 2px;
      }
    }
  }
  .form-desc-high {
    height: 320px;
  }
}
.no-right {
  margin-right: 0;
}
</style>
