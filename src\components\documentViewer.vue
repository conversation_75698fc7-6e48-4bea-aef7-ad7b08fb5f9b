<template>
  <div class="document-viewer-container">
    <div class="content">
      <h1 class="title">{{ documentItem.pageName }}</h1>
      <p class="update-time">更新时间：{{ documentItem.updateTime }}</p>
      <div class="w-e-text">
        <div class="document-content markdown-body" v-html="documentItem.pageContent"></div>
      </div>
    </div>
    <div class="anchor-wrap">
      <a-anchor class="anchor" @click="handleClick" :getContainer="getContainer">
        <a-anchor-link v-for="item of titles" :key="item.id" :href="`#${item.id}`" :title="item.text" />
      </a-anchor>
    </div>
  </div>
</template>

<script>
export default {
  name: 'documentViewer',
  props: {
    documentItem: {
      type: Object,
      required: true,
    },
    titles: {
      type: Array,
      required: true,
    },
  },
  methods: {
    handleClick(event) {
      event.preventDefault();
    },
    getContainer() {
      const ele = document.getElementById('document-container');
      return ele;
    },
  },
  mounted() {
    document.querySelector('.w-e-text').addEventListener(
      'click',
      (event) => {
        if (event.target.tagName === 'IMG') {
          this.$viewerApi({
            images: [event.target.src],
            options: {
              toolbar: false,
              navbar: false,
            },
          });
        }
      },
      true
    );
  },
};
</script>

<style lang="less" scoped>
.document-viewer-container {
  display: flex;
}
.content {
  flex: 1;
}
.title {
  margin: 24px 0 16px 0;
  font-size: 30px;
  font-weight: 500;
  color: #121f2c;
}
.update-time {
  margin-bottom: 40px;
  font-size: 14px;
  font-weight: 400;
  color: #a0a6ab;
}
.document-content {
  min-height: calc(100vh - 460px);
  width: calc(100vw - 640px);
  margin-bottom: 80px;
  line-height: 1.5715;
  word-wrap: break-word;
}
.anchor-wrap {
  .anchor {
    position: fixed;
    right: 60px;
    width: 240px;
    height: calc(100vh - 500px);
    overflow: auto;
    padding: 4px;
    &::-webkit-scrollbar {
      display: none;
    }
  }
}
/deep/ .ant-anchor-wrapper {
  background: transparent;
}
</style>
