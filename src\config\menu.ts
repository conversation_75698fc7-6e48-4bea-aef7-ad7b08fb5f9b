import { check<PERSON><PERSON><PERSON><PERSON><PERSON>PathAuth, checkKey<PERSON>loakPathAttr } from '@/utils/auth';
export enum ENV_PATH {}
export enum KEYCLOAK_PATH {
  'home' = 'COMMON_MGM_FRONT_PAGE',
  'user-management' = 'COMMON_MGM_USER',
  'userControl-edit' = 'COMMON_MGM_USER_EDIT',
  'userControl-create' = 'COMMON_MGM_USER_CREATE',
  'message-management' = 'COMMON_MGM_MAIL',
  'message-check' = 'COMMON_MGM_APPROVE_MAIL',
  'capacity-management' = 'COMMON_MGM_CAPABILITY_MANAGE',
  'platform-document' = 'COMMON_MGM_HELP_DOC_MANAGE',
  'page-check' = 'COMMON_MGM_HELP_DOC_APPROVE',
  'skill-document' = 'COMMON_MGM_HELP_ABILITY_DOC_MANAGE',
  'workorder-manage' = 'COMMON_MGM_TICKET,COMMON_MGM_DEMAND_TICKET',
  'dynamic-manage' = 'COMMON_MGM_DYNAMIC',
}

// 属性控制
export enum KEYCLOAK_PATH_ATTR {
  'organization-management' = 'jtc-group-view',
  'role-management' = 'jtc-role-view',
  'roleControl-create' = 'jtc-role-manage',
  'roleControl-roleEdit' = 'jtc-role-manage',
  'operation-log' = 'jtc-operation-log',
}

export function formatSideMenu(menuData: []): any {
  const sideMenuAuth: string[] = [];
  const sideMenuData = menuData.filter((menuItem: any) => {
    if (menuItem.subs && menuItem.subs.length > 0) {
      menuItem.subs = menuItem.subs.filter((subMenu: any) => {
        const path = subMenu.link.slice(1);
        // 一个菜单应该只出现在KEYCLOAK_PATH和KEYCLOAK_PATH_ATTR中的一个  先从KEYCLOAK_PATH_ATTR中找，能找到，就已属性的判断为最终结果   找不到去KEYCLOAK_PATH找，找到已这里的判断为最终结果  还是找不到，说明不控制，直接显示
        const keycloakAuth = checkKeycloakPathAuth(path);
        const keycloakAuthAttr = checkKeycloakPathAttr(path);
        if (!keycloakAuthAttr) {
          return keycloakAuthAttr;
        }
        if (!keycloakAuth) {
          return keycloakAuth;
        }
        sideMenuAuth.push(path);
        return true;
      });
      if (menuItem.subs.length > 0) {
        return true;
      } else {
        return false;
      }
    } else {
      const path = menuItem.link.slice(1);
      const keycloakAuth = checkKeycloakPathAuth(path);
      const keycloakAuthAttr = checkKeycloakPathAttr(path);
      if (!keycloakAuthAttr) {
        return keycloakAuthAttr;
      }
      if (!keycloakAuth) {
        return keycloakAuth;
      }
      sideMenuAuth.push(path);
      return true;
    }
  });
  return { sideMenuAuth, sideMenuData };
}
