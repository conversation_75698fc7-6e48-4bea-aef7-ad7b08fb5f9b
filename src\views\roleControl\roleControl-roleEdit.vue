<template>
  <div class="user-edit">
    <a-breadcrumb class="nav">
      <a-breadcrumb-item>
        <router-link :to="{ name: '角色管理', query: { platform: $route.query.platform } }">
          角色管理
          <span v-if="isEdit">&nbsp; /&nbsp; {{ $route.query.platform }}</span>
        </router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item v-if="isEdit">
        <router-link :to="{ name: '角色详情', query: $route.query }">角色详情</router-link>
      </a-breadcrumb-item>
      <a-breadcrumb-item>{{ isEdit ? '编辑角色' : '新建角色' }}</a-breadcrumb-item>
    </a-breadcrumb>
    <div class="main">
      <a-form :colon="false" class="form-content" ref="ruleForm" :model="form" :rules="rules" :label-col="labelCol" :wrapper-col="wrapperCol">
        <div class="form-title">{{ isEdit ? '编辑角色' : '新建角色' }}</div>
        <HeadTitle :title="infoTitle[0]" />
        <div class="base-info">
          <a-form-item class="sub-item role-item" label="角色" :wrapper-col="subWrapperCol" name="roleName">
            <a-input placeholder="请输入" v-model:value="form.roleName" @change="(e) => checkRoleName(e)" />
            <p v-if="conflict">已有同名角色，请修改后再试</p>
          </a-form-item>
          <a-form-item class="sub-item platform-item" label="平台" :wrapper-col="subWrapperCol" required>
            <a-select :disabled="true" v-model:value="form.platformCode" show-search :options="platformOptions" @change="platformChange" />
            <p>新建成功后不可修改，请谨慎设置</p>
          </a-form-item>
          <a-form-item label="角色描述" class="introduction-item" name="roleDesc">
            <a-textarea v-model:value="form.roleDesc" placeholder="请输入" :rows="4" />
            <span class="count-area">{{ `${(form.roleDesc || '').length}/${maxCount}` }}</span>
          </a-form-item>
        </div>
        <div class="role-info">
          <HeadTitle :title="infoTitle[1]" />
          <div style="margin-top: 17px">
            <div class="iconsousuo"><jt-icon type="iconsousuo" /></div>
            <div class="iconsousuo1"><jt-icon type="iconsousuo" /></div>
            <div class="transfer-left-title">
              <span>权限</span>
              <span>({{ dataSource.length - targetKeys.length - countNum }}项)</span>
            </div>
            <div class="transfer-right-title">
              <span>已选权限</span>
              <span>({{ targetKeys.length }}项)</span>
            </div>
            <a-form-item class="sub-item" label=" " :wrapper-col="subWrapperCol" name="myrole">
              <div v-if="transferLoading" class="transfer-loading">
                <a-spin />
              </div>
              <a-transfer class="tree-transfer" :data-source="dataSource" v-model:target-keys="targetKeys" show-search @search="handleSearch" :show-select-all="false" @change="transferChange" :filter-option="filterOption">
                <template #render="item">
                  <a-tooltip>
                    <template #title>
                      {{ item.title }}
                    </template>
                    <span>{{ item.title }}</span>
                  </a-tooltip>
                </template>
              </a-transfer>
            </a-form-item>
          </div>
        </div>
      </a-form>
      <a-button @click="formFinish" type="primary" class="confirm-btn">确定</a-button>
      <a-button @click="goBack" class="cancel-btn">取消</a-button>
    </div>
    <div v-if="editLoading" class="edit-loading">
      <a-spin />
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { message } from 'ant-design-vue';
import _ from 'lodash';

import { Role } from '@/apis';
import { PERMISSION_TYPE } from '@/constants/role';
// import request from '@/request';

import HeadTitle from '@/components/headTitle.vue';

export default defineComponent({
  components: {
    HeadTitle,
  },
  data() {
    return {
      editLoading: false,
      roleId: this.$route.query.id,
      userInfo: {} as any,
      conflict: false,
      isEdit: this.$route.query.flag === '1',
      infoTitle: ['基本信息', '权限信息'],
      labelCol: { span: 4 },
      wrapperCol: { span: 10 },
      subWrapperCol: { span: 14 },
      platformOptions: [],
      platformCode: this.$route.query.platformCode,
      form: {
        roleName: '',
        platformCode: this.$route.query.platformCode,
        roleDesc: '',
      },
      maxCount: 100,
      roleList: [],
      transferLoading: true,
      transferDataSource: [] as any,
      treeData: [],
      targetKeys: [],
      dataSource: [],
      countNum: 0,
      searchValue: '',
      expandedKeys: [],
      autoExpandParent: true,
    };
  },
  created() {
    this.getUserInfo();
    this.getPlatform();
    this.getPowerList();
  },
  computed: {
    rules() {
      return {
        myrole: [
          {
            required: false,
            validator: (rule, value, callback) => {
              if (this.targetKeys.length > 0) {
                return Promise.resolve();
              } else {
                return Promise.reject();
              }
            },
            message: '请至少选择一个权限',
            trigger: ['blur', 'change'],
          },
        ],
        roleName: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value.length <= 0) {
                return Promise.reject('请输入');
              }
              if (value.length > 20) {
                return Promise.reject('20个字符以内');
              }
              if (this.conflict) {
                return Promise.reject('已有同名角色，请修改后再试');
              }
              return Promise.resolve();
            },
            trigger: ['blur', 'change'],
          },
          // { required: true, message: '请输入', trigger: ['blur', 'change'] },
          // { min: 0, max: 20, message: '20个字符以内', trigger: ['blur', 'change'] },
        ],
        roleDesc: [
          { required: true, message: '请输入角色描述', trigger: ['blur', 'change'] },
          { min: 0, max: 100, message: '不超过100个字符', trigger: ['blur', 'change'] },
        ],
      };
    },
  },
  watch: {
    treeData(this: any) {
      return this.handleTreeData(this.treeData, this.targetKeys);
    },
  },
  methods: {
    checkRoleName: _.debounce(async function (this: any, e) {
      if (e.target.value.length <= 20 && e.target.value.length > 0) {
        const data = {
          platformCode: this.platformCode,
          roleName: this.form.roleName,
        };
        const res = await Role.existName({ data });
        if (res.code === 0) {
          this.conflict = res.data;
        }
      }
    }, 500),
    async getPlatform(this: any) {
      const res = await Role.getPlatformList();
      if (res.code === 0) {
        this.platformOptions = res.data.map((item) => {
          return { value: item.code, label: item.name };
        });
      }
    },
    platformChange(val) {
      this.form.platformCode = val;
    },
    // 获取基本信息
    async getUserInfo(this: any) {
      if (!this.isEdit) return;
      // request('/keycloak/web/admin/user/get', { method: 'GET', data: { role: this.roleId } }).then((res: any) => {
      //   if (res.state == 'OK') {
      //     this.userInfo = res.body;
      //   }
      // });
      const data = {
        platformCode: this.platformCode,
        roleId: this.roleId,
      };
      const res = await Role.getRoleBasicInfo({ data });
      if (res.code === 0) {
        this.form.roleName = res.data.name;
        this.form.roleDesc = res.data.desc;
      }
    },
    // 获取权限
    async getPowerList(this: any) {
      const data = {
        platformCode: this.platformCode,
      };
      const res = await Role.getPermissionList({ data });
      // for (let i = 0; i < res.data.length; i++) {
      //   tempArr.push({ key: res.data[i].key, title: res.data[i].name, type: res.data[i].type, desc: res.data[i].desc });
      // }
      this.roleList = res.data.map((x) => {
        x.title = x.name;
        return x;
      });
      this.getRightList();
      this.flatten(this.roleList);
      this.treeData = this.handleTreeData(this.roleList, this.targetKeys.value);
      this.dataSource = this.transferDataSource;
      this.transferLoading = false;
    },
    // 获取已选权限
    async getRightList(this: any) {
      let targetKeys = [] as any[];
      const data = {
        platformCode: this.platformCode,
        roleId: this.roleId,
      };
      if (!this.isEdit) {
        const baseList = this.roleList.filter((x) => x.title === PERMISSION_TYPE.basic);
        for (const i in baseList) {
          const temp = baseList[i];
          targetKeys.push(temp.key);
        }
      } else {
        const res = await Role.getPermissionInfo({ data });
        if (res.code === 0) {
          targetKeys = res.data.map((x) => x.key);
        }
      }
      this.transferChange(targetKeys);
    },
    // 提交表单
    formFinish(this: any) {
      // this.editLoading = true;
      this.$refs.ruleForm
        .validate()
        // 表单校验成功
        .then(async () => {
          const data = { ...this.form };
          let requestFn = Role.create;
          if (this.isEdit) {
            requestFn = Role.update;
            data.roleId = this.roleId;
          }
          const res = await requestFn({ data });
          if (res.code === 0) {
            message.success(`${this.isEdit ? '编辑' : '新建'}角色成功`);
            this.goBack();
          } else {
            this.editLoading = false;
            // 新建/编辑角色失败，请稍后重试
            message.error(res.msg);
          }
        })
        // 表单校验失败
        .catch((err) => {
          this.editLoading = false;
          this.$refs.ruleForm.scrollToField();
          throw new Error(err);
        });
    },
    goBack(this: any) {
      let goPath = { path: '/role-management', query: { ...this.$route.query } };
      if (this.isEdit) {
        goPath = { path: '/role-management/roleControl-detail', query: { ...this.$route.query } };
      }
      this.$router.push(goPath);
    },
    // 穿梭框左边搜索
    handleSearch: _.debounce(function (this: any, dir: string, value: string) {
      if (dir === 'left') {
        const expandedKeys = this.roleList
          .map((item) => {
            if (item.title.indexOf(value) > -1) {
              return this.getParentKey(item.key, this.roleList);
            }
            return null;
          })
          .filter((item, i, self) => item && self.indexOf(item) === i);
        Object.assign(this, {
          expandedKeys,
          searchValue: value,
          autoExpandParent: true,
        });
      }
    }, 500),
    // 穿梭框右边搜索
    filterOption(inputValue, option) {
      let str = '';
      if (option.displayName) {
        str = `${option.displayName}-`;
      }
      let searchStr = `${str} ${option.title}`;
      return searchStr.indexOf(inputValue) > -1;
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    getParentKey(key, tree) {
      let parentKey;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          if (node.children.some((item) => item.key === key)) {
            parentKey = node.key;
          } else if (this.getParentKey(key, node.children)) {
            parentKey = this.getParentKey(key, node.children);
          }
        }
      }
      return parentKey;
    },
    flatten(list = []) {
      list.forEach((item: any) => {
        this.transferDataSource.push(item);
      });
    },
    isChecked(selectedKeys, eventKey) {
      return selectedKeys.indexOf(eventKey) !== -1;
    },
    handleTreeData(data, targetKeys: any[] = []) {
      return data;
    },
    transferChange(this: any, targetKeys) {
      this.targetKeys = targetKeys;
      this.form.permissionKeys = targetKeys;
    },
    onChecked(_, e, checkedKeys, itemSelect) {
      const { eventKey } = e.node;
      itemSelect(eventKey, !this.isChecked(checkedKeys, eventKey));
    },
  },
});
</script>
<style lang="less" scoped>
.user-edit {
  .nav {
    height: 58px;
    line-height: 58px;
    padding-left: 20px;
    background: #ffffff;
    font-weight: 500;
    color: #121f2c;
  }
  .main {
    min-width: 1254px;
    padding: 20px 20px 80px;
    margin: 20px;
    background: #ffffff;
    position: relative;
    .confirm-btn {
      width: 120px;
      margin: 28px 10px 0 150px;
    }
    .cancel-btn {
      width: 88px;
    }
    .form-content {
      position: relative;
      .form-title {
        font-size: 16px;
        font-weight: 500;
        color: #121f2c;
        margin-bottom: 24px;
      }
    }
    /deep/.ant-col-4 {
      min-width: 136px;
      max-width: 148px;
    }
    /deep/.ant-space-align-center {
      align-items: unset;
      vertical-align: top;
    }
  }
  .base-info {
    max-width: 1300px;
    margin: 20px 0 0;
    display: flex;
    flex-wrap: wrap;
    padding-left: 14px;
    position: relative;
    .sub-item {
      // width: 600px;
      width: 800px;

      /deep/.ant-input {
        width: 480px;
        height: 32px;
      }
    }
    .role-item {
      position: relative;
      p {
        font-size: 12px;
        color: #ff454d;
        position: absolute;
        top: 32px;
        left: 0;
      }
    }
    .platform-item {
      position: relative;
      /deep/.ant-select {
        width: 480px;
      }
      p {
        font-size: 12px;
        color: #f5a623;
        position: absolute;
        top: 32px;
        left: 0;
      }
    }
    .constraint-item {
      /deep/.ant-switch {
        vertical-align: top;
      }
      span {
        width: 315px;
        font-size: 12px;
        color: #ff8b00;
        position: absolute;
        top: 7px;
        left: 63px;
      }
    }
    .school-item {
      margin-right: 480px;
      margin-bottom: 0;
      /deep/ .ant-form-item-children {
        display: flex;
      }
      /deep/ .ant-form-item-label {
        line-height: 32px;
      }
    }
    .introduction-item {
      width: 800px;
      position: relative;
      /deep/.ant-form-item-control-wrapper {
        width: 480px;
      }
      /deep/.ant-input {
        width: 480px;
        height: 88px;
        resize: none;
      }
      /deep/.ant-col-10 {
        min-width: 480px;
      }
      .count-area {
        position: absolute;
        bottom: -21px;
        right: 0px;
        color: #606972;
      }
    }
    .img-uploader {
      position: absolute;
      top: 10px;
      right: -140px;
    }
  }
  .tip-item {
    color: #606972;
    padding-left: 150px;
  }
  .tip-item1 {
    width: 100%;
    height: 1px;
    border-bottom: 1px solid #efefef;
    margin: 0 0 40px;
  }
  .role-info {
    position: relative;
    margin-bottom: 20px;
    .transfer-loading {
      width: 247px;
      height: 232px;
      text-align: center;
      padding-top: 100px;
      position: absolute;
      top: 98px;
      left: 0px;
      z-index: 2;
    }
    .transfer-title {
      position: absolute;
      background: #ffffff;
      top: 50px;
      font-size: 12px;
      color: #333333;
      z-index: 1;
      span:nth-child(2) {
        color: #bbbbbb;
        margin-left: 8px;
      }
    }
    .transfer-left-title {
      left: 161px;
      .transfer-title();
    }
    .transfer-right-title {
      left: 448px;
      .transfer-title();
    }
    .iconsousuo {
      background: #ffffff;
      position: absolute;
      top: 98px;
      left: 172px;
      z-index: 2;
    }
    .iconsousuo1 {
      background: #ffffff;
      position: absolute;
      top: 98px;
      left: 460px;
      z-index: 2;
    }
    /deep/.anticon svg {
      color: #bec2c5;
      width: 18px;
      height: 18px;
    }
    /deep/.ant-transfer-list {
      width: 248px;
      height: 331px;
      max-width: 248px;
      max-height: 331px;
      .ant-tree,
      .ant-transfer-list-content {
        font-size: 12px !important;
        color: #555555;
      }
    }
  }
  .edit-loading {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.45);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
/deep/.ant-form-item-explain {
  font-size: 12px;
}
/deep/.ant-breadcrumb-link {
  font-weight: 400;
}
/deep/.ant-form-item-label > label::after {
  margin-right: 16px;
}
.tree-transfer {
  :deep(.ant-transfer-operation) {
    .anticon {
      transform: scale(0.8);
    }
  }
}
</style>
