/* 全局样式定义 */

// table 状态按钮
.dot-common {
    display: flex;
    align-items: center;
    &:before {
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-right: 5px;
    }
    &.dot-success {
      &:before {
        background-color: #1dca94;
      }
    }
    &.dot-queue {
      &:before {
        background-color: #ff931d;
      }
    }
    &.dot-fail {
      &:before {
        background-color: #ff454d;
      }
    }
    &.dot-running {
      &:before {
        background-color: #0082ff;
      }
    }
    &.dot-wait {
      &:before {
        background-color: #c2c5cf;
      }
    }
  }
// table 操作列按钮
.btn-common {
  cursor: pointer;
  color: #0082ff;
    &:hover {
    color: #0082ff;
  }
  &.error {
    cursor: no-drop;
    color: #d8d8d8;
  }
}
/deep/.jt-table-filter{
  .jt-icon{
    color: #bfbfbf;
    &.active{
      color:#337dff;
    }
  }
}

.icon {
  width         : 1em;
  height        : 1em;
  vertical-align: -0.15em;
  fill          : currentColor;
  overflow      : hidden;
}

// #container-wrap{
//   width: 100%;
//   overflow-y: auto;
//   padding: 20px;
// }
// #container-wrapT{
//   width: 100%;
//   overflow-y: auto;
//   padding: 76px 20px 20px 20px;
// }
// #container {
//   padding: 20px;
//   background-color: white;
// }

.markdown-body {
  /* table 样式 */
  table {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
  }
  table td,
  table th {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    padding: 3px 5px;
  }
  table th {
    border-bottom: 2px solid #ccc;
    text-align: center;
  }

  /* blockquote 样式 */
  blockquote {
    display: block;
    border-left: 8px solid #d0e5f2;
    padding: 5px 10px;
    margin: 10px 0;
    line-height: 1.4;
    font-size: 100%;
    background-color: #f1f1f1;
  }

  /* code 样式 */
  code {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    background-color: #f1f1f1;
    border-radius: 3px;
    padding: 3px 5px;
    margin: 0 3px;
  }
  pre code {
    display: block;
  }

  /* ul ol 样式 */
  ul,
  ol {
    margin: 10px 0 10px 20px;
    padding-inline-start: 0px;
  }

  ul li {
    list-style: disc;
  }
  ol li {
    list-style: decimal;
  }
}
.overflow-ellipsis{
  display: inline-block;
  max-width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.ant-table-content .ant-table-thead {
  font-size: 12px;
  color: #121f2c;
  tr th {
    background-color: #f6f9fc;
  }
}
.ant-table-thead{
  // 删除table标题之间分割线
  .ant-table-cell{
    &:before{
      display: none;
    }
  }
}
.ant-table-row {
  font-size: 12px;
}
.ant-table-content .ant-table-tbody .ant-table-row:hover td {
  background: #f5faff;
}
// table的过滤icon位置调整为在文字右侧12px
.ant-table-filter-column{
  justify-content: inherit;
  .ant-table-column-title{
    flex: inherit;
    margin-right: 8px;
  }
}
// 排序按钮紧贴文案
.ant-table .ant-table-column-sorters {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    flex: 0;
  }
}
// 确认弹窗的统一样式
.confirm-modal {
  padding: 8px 8px 0 9px;
  .modal-title {
    font-weight: 500;
    color: #121f2c;
    margin-bottom: 16px;
    .title-icon {
      font-size: 18px;
      color: #0068ff;
      margin-right: 9px;
    }
    .danger {
      color: red;
    }
  }
  .modal-content {
    font-size: 12px;
    color: #606972;
    padding: 0 0 24px 27px;
    p {
      margin-bottom: 12px;
    }
  }
  .modal-footer {
    display: flex;
    justify-content: right;
  }
}
// table中的loading的最小高度
.ant-table-wrapper {
  &:not(.auto-height-table) {
    .ant-spin-nested-loading {
      min-height: 500px;
    }
  }
}
.app-loading-spin {
  .ant-spin.ant-spin-spinning {
    height: 100%;
    max-height: 100%;
  }
}
// table排序按钮的颜色
.ant-table-column-sorter{
  color: #606972;
}