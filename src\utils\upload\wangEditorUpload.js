import E from 'wangeditor';
const { BtnMenu } = E;
export const uploadMenuKey = 'wangeditor_upload_menu';
export default class UploadMenu extends BtnMenu {
  constructor(editor) {
    const $elem = E.$(
      `<div class="w-e-menu" data-title="上传附件">
          <i class="w-e-icon-upload2"></i>
            </div>`
    );
    super($elem, editor);
  }
  // 菜单点击事件
  clickHandler() {
    if (this.editor.uploadBtnClick) {
      this.editor.uploadBtnClick();
    }
  }

  tryChangeActive() {}
}
