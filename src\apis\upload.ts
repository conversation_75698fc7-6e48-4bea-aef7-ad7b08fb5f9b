import request from '@/request';
const CENTER_TYPE = {
  HELP: {
    init: '/common_help/manage/fileupload/multipartinit',
    compose: '/common_help/manage/fileupload/compose',
    info: '/common_help/manage/fileupload/info',
    download: '/common_help/manage/fileupload/url',
  },
  MESSAGE: {
    init: '/messagecenter/web/file/multipartinit',
    compose: '/messagecenter/web/file/compose',
    info: '/messagecenter/web/file/info',
    download: '/messagecenter/web/file/url',
  },
  DYNAMIC:{
    init: '/homepage/web/dynamic/file/multipartinit',
    compose: '/homepage/web/dynamic/file/compose',
    info: '/homepage/web/dynamic/file/info',
    download: '/homepage/web/dynamic/file/url',
  }
};

const uploadForEditor = {
  initUpload: (params: any) => {
    const { name, type, data } = params;
    return request(CENTER_TYPE[name].init, {
      method: type || 'GET',
      data,
    });
  },
  composeFile: (params: any) => {
    const { name, type, data } = params;
    return request(CENTER_TYPE[name].compose, {
      method: type || 'GET',
      data,
    });
  },
  queryMergeResult: (params: any) => {
    const { name, type, data } = params;
    return request(CENTER_TYPE[name].info, {
      method: type || 'GET',
      data,
    });
  },
  getDownLoadUrl: (params: any) => {
    const { name, type, data } = params;
    return request(CENTER_TYPE[name].download, {
      method: type || 'GET',
      data,
    });
  },
};
export default uploadForEditor;
