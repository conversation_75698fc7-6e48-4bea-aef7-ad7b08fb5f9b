<template>
  <div class="user-control-container">
    <sub-header title="站内信审核"></sub-header>
    <Container>
      <container-item>
        <div class="top-bar">
          <a-tabs v-model:activeKey="activeTab" @change="tabChange">
            <a-tab-pane v-for="item in listTab" :key="item" :tab="item" force-render></a-tab-pane>
          </a-tabs>
          <div style="display: flex">
            <a-input placeholder="站内信标题/提交人" @change="handlerSearch" v-model:value="keyword">
              <template #prefix>
                <jt-icon type="iconsousuo" style="font-size: 18px" />
              </template>
            </a-input>
          </div>
        </div>
        <a-table :loading="tableAttr(loading).loading" :columns="columns" :data-source="tableList" @change="tableChange" rowKey="rowKey" :pagination="false">
          <template #emptyText>
            <empty
              v-if="!loading"
              title="站内信"
              :showNoDataText="
                showNoDataText({
                  mailTypeFilterValue: mailTypeFilterValue,
                  isGlobalFilterValue: isGlobalFilterValue,
                  keyword: keyword,
                })
              "
            ></empty>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'mailTitle'">
              <span @click="checkPage(record)" class="mailTitleText" :title="record.mailTitle">{{ record.mailTitle }}</span>
            </template>
            <template v-if="column.key === 'parents'">
              <a-tooltip v-if="record.parentCatalog[0]">
                <template #title v-if="record.parentCatalog[0].indexOf('...') != -1">
                  <span>{{ record.parents[0] }}</span>
                </template>
                <span>{{ record.parentCatalog[0] }}</span>
              </a-tooltip>
              <span v-if="record.parents[1]" style="color: #0082ff"> - </span>
              <a-tooltip v-if="record.parentCatalog[1]">
                <template #title v-if="record.parentCatalog[1].indexOf('...') != -1">
                  <span>{{ record.parents[1] }}</span>
                </template>
                <span>{{ record.parentCatalog[1] }}</span>
              </a-tooltip>
              <span v-if="record.parents.length === 0"> - </span>
            </template>
            <template v-if="column.key === 'isGlobal'">
              <a-tag color="blue" style="margin: 7px 0">{{ +record.isGlobal === 0 ? '指定用户' : '全部用户' }}</a-tag>
            </template>
            <template v-if="column.key === 'operation'">
              <a-space size="middle" class="operation-button">
                <a-button @click="checkPage(record)" :disabled="record.dataType === 'catalog'" type="link">查看</a-button>
                <a-button @click="checkMail(record, '1')" type="link">通过</a-button>
                <a-button @click="checkMail(record, '0')" type="link" danger>驳回</a-button>
              </a-space>
            </template>
            <template v-if="column.key === 'result'">
              <a-space :size="3">
                <check-circle-outlined v-if="record.result == '1'" :style="{ color: '#0082FF' }" />
                <close-circle-outlined v-else :style="{ color: 'red' }" />
                <span :class="{ 'audit-reject': record.result == '0' }">{{ record.result == 1 ? '通过' : '驳回' }}</span>
              </a-space>
            </template>
          </template>
        </a-table>
        <div v-if="tableList.length > 0" class="jt-pagination">
          <a-space size="large">
            <span>共{{ pagination.total }}条</span>
          </a-space>
          <a-pagination @change="changePage" :pageSize="pagination.pageSize" show-quick-jumper :current="pagination.pageNum" :total="pagination.total" />
        </div>
      </container-item>
    </Container>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';
import { message } from 'ant-design-vue';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons-vue';
import subHeader from '@/components/subHeader.vue';
import request from '@/request';
import _ from 'lodash';
import Container from '@/components/container.vue';
import ContainerItem from '@/components/containerItem.vue';
import { tableAttr, showNoDataText } from '@/utils';
import empty from '@/components/empty.vue';
export default defineComponent({
  components: {
    subHeader,
    CheckCircleOutlined,
    CloseCircleOutlined,
    Container,
    ContainerItem,
    empty,
  },
  data() {
    return {
      listTab: ['待审批', '审批记录'],
      activeTab: '待审批',
      keyword: '',
      loading: true,
      tableList: [],
      pagination: {
        pageSize: 10,
        pageNum: 1,
        total: 0,
      },
      accessPlatform: '',
      submitTimeSorter: '',
      mailTypeFilterValue: [],
      isGlobalFilterValue: [],
      allMailTypeFilterList: [],
    };
  },
  computed: {
    columns(this: any) {
      let columns: any[] = [
        {
          title: '站内信标题',
          dataIndex: 'mailTitle',
          key: 'mailTitle',
          scopedSlots: { customRender: 'mailTitle' },
          ellipsis: true,
        },
        {
          title: '发送对象',
          key: 'isGlobal',
          dataIndex: 'isGlobal',
          // filteredValue: [0],
          filteredValue: this.isGlobalFilterValue,
          filters: [
            {
              text: '指定用户',
              value: 0,
            },
            {
              text: '全部用户',
              value: 1,
            },
          ],
        },
        {
          title: '类型',
          key: 'mailType',
          dataIndex: 'mailType',
          filteredValue: this.mailTypeFilterValue,
          filters: this.allMailTypeFilterList,
          customRender: ({ text }) => {
            return this.allMailTypeFilterList.find((x) => x.value === text).text;
          },
        },
        {
          title: '提交审核时间',
          key: 'submitTime',
          dataIndex: 'submitTime',
          // 后期如果需要添加排序功能，就取消下面两行的注释
          // sorter: true,
          // sortOrder: this.submitTimeSorter,
        },
        {
          title: '提交人',
          key: 'submitOperater',
          dataIndex: 'submitOperater',
        },
      ];
      if (this.activeTab === this.listTab[1]) {
        columns.push({ title: '审核时间', key: 'approveTime', dataIndex: 'approveTime' }, { title: '审核结果', key: 'result', scopedSlots: { customRender: 'result' } });
      } else {
        columns.push({ title: '操作', key: 'operation', scopedSlots: { customRender: 'operation' } });
      }

      return columns;
    },
  },
  created() {
    this.getColumns();
  },
  methods: {
    showNoDataText,
    tableAttr,
    handleCreate() {
      this.$router.push({
        path: '/message-management/messageControl-create',
      });
    },
    /**
     * 重置table列表所需各项参数
     */
    resetTableData() {
      this.tableList = [];
      this.keyword = '';
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 10;
      this.submitTimeSorter = '';
      this.mailTypeFilterValue = [];
      this.isGlobalFilterValue = [];
    },
    tabChange(this: any, e) {
      this.activeTab = e;
      this.loading = true;
      this.resetTableData();
      this.getTableList();
    },
    async getColumns(this: any) {
      const getAllMailType = await request('/messagecenter/web/getAllMailType', { method: 'GET' });
      if (getAllMailType.state === 'OK') {
        this.allMailTypeFilterList = [];
        for (const i in getAllMailType.body) {
          const item = getAllMailType.body[i];
          this.allMailTypeFilterList.push({
            value: item.mailTypeId,
            text: item.mailTypeName,
          });
        }
        this.tabChange(this.activeTab);
        this.getTableList();
      }
    },
    getTableList(this: any) {
      this.loading = true;
      request('/messagecenter/web/manage/getApproveMailList', {
        method: 'POST',
        data: {
          approved: this.activeTab === '审批记录',
          keyword: this.keyword,
          mailTypes: this.mailTypeFilterValue,
          order: this.submitTimeSorter ?? '',
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
          receiverTypes: this.isGlobalFilterValue,
        },
      }).then((res: any) => {
        if (res.state === 'OK') {
          this.loading = false;
          this.tableList = res.body.list;
          this.pagination.total = res.body.total;
        }
      });
    },
    changePage(pageNum, pageSize) {
      this.pagination.pageNum = pageNum;
      this.pagination.pageSize = pageSize;
      this.getTableList();
    },
    handlerSearch(this: any, e) {
      this.loading = true;
      this.search(this);
    },
    search: _.debounce(function (this: any) {
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 10;
      this.getTableList();
    }, 500),
    tableChange(pagination, filters, sorter, extra) {
      this.submitTimeSorter = sorter.order;
      for (const i in filters) {
        if (filters[i]) {
          this[i + 'FilterValue'] = filters[i];
        } else {
          this[i + 'FilterValue'] = [];
        }
      }
      this.pagination.pageNum = 1;
      this.pagination.pageSize = 10;
      this.getTableList();
    },
    checkPage(this: any, record) {
      this.$router.push({ path: '/message-check/helpcenterControl-pagePreview', query: { mailId: record.id } });
    },
    /**
     * 审核站内信
     * @param record 当前站内信
     * @param type 1通过 0驳回
     */
    checkMail(record, type) {
      request('/messagecenter/web/manage/approveMail', { method: 'GET', data: { id: record.id, approveResult: type } })
        .then((res: any) => {
          if (res.state === 'OK') {
            message.success(`站内信【${record.mailTitle}】审核成功`);
            this.getTableList();
          } else {
            message.error(`站内信【${record.mailTitle}】审核失败`);
          }
        })
        .catch(() => {
          message.error(`站内信【${record.mailTitle}】审核失败`);
        });
    },
  },
});
</script>
<style lang="less" scoped>
@import '@/assets/styles/index.less';
.mailTitleText {
  cursor: pointer;
  &:hover {
    color: @jt-primary-color;
  }
}
.top-bar {
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /deep/.ant-tabs-nav {
    margin: 0;
  }
  /deep/.ant-tabs-nav::before {
    border: none;
  }
  /deep/.ant-tabs-tab {
    width: 136px;
    height: 32px;
    display: flex;
    justify-content: center;
    margin: 0;
    font-size: 14px;
    color: #606972;
    line-height: 20px;
  }
  /deep/.ant-tabs-tab-active {
    font-weight: 500;
    color: #0082ff;
  }
}

:deep(.ant-table-thead) {
  font-size: 12px;
  color: #121f2c;
  th {
    background-color: #f6f9fc;
  }
}
/deep/.ant-table-row {
  min-height: 53px;
  font-size: 12px;
  td {
    padding: 10px 16px;
  }
}
/deep/.ant-table-thead > tr > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan]):before {
  display: none;
}
/deep/.ant-table-row:hover td {
  background: #f5faff !important;
}
/deep/.ant-table-filter-column {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    width: 48px;
    flex: 0;
  }
}
.table-empty {
  color: #606972;
  font-size: 18px;
  font-weight: bold;
  position: relative;
  top: -120px;
}
.jt-pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
}
.platform {
  display: inline-block;
  width: 90px;
  height: 20px;
  line-height: 16px;
  text-align: center;
  border: 1px solid;
}
.deep-learn {
  color: yellow;
  border-color: yellow;
}
.ai-capacity {
  color: blue;
  border-color: blue;
}
.operation-button {
  button {
    font-size: 12px;
    padding: 0;
  }
}
.audit-reject {
  color: red;
}
</style>
