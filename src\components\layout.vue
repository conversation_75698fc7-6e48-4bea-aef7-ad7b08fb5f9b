<template>
  <div>
    <a-layout class="layout">
      <a-layout-sider class="sider" :collapsedWidth="50" v-model:collapsed="collapsed" :trigger="null" collapsible>
        <a v-if="collapsed" :class="['logo-container-collapsed', !linkUrl && 'no-link']" :href="linkUrl || 'javascript:;'">
          <img style="width: 35px" :src="collapsedLogoUrl" alt="" />
        </a>
        <a v-else :class="['logo-container', !linkUrl && 'no-link']" :href="linkUrl || 'javascript:;'">
          <img style="width: 126px; height: 26px" :src="logoUrl" alt="" v-if="logoUrl" />
        </a>

        <p v-if="!collapsed" class="title">{{ title }}</p>
        <side-menu></side-menu>
      </a-layout-sider>
      <a-layout class="layout-main" :style="{ width: `calc(100% - ${collapsed ? 50 : 200}px)`, left: `${collapsed ? 50 : 200}px`, 'min-width': `${collapsed ? 1360 : 1240}px` }">
        <jt-header :collapsed="collapsed" @toggleCollapsed="toggleCollapsed"></jt-header>
        <a-layout-content>
          <slot></slot>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, ref } from 'vue';
import header from './header.vue';
import sideMenu from '@/components/sideMenu.vue';
import { useStore } from 'vuex';
export default defineComponent({
  components: {
    jtHeader: header,
    sideMenu,
  },
  setup() {
    const collapsed = ref(false);
    const store = useStore();
    const title = computed(() => store.state.layoutConfig.subtab || '');
    const logoUrl = computed(() => store.state.layoutConfig.iconTop);
    const collapsedLogoUrl = computed(() => store.state.layoutConfig.iconTopFold);
    const linkUrl = computed(() => store.state.layoutConfig.link);
    return {
      collapsed,
      linkUrl,
      title,
      logoUrl,
      collapsedLogoUrl,
      toggleCollapsed: () => {
        collapsed.value = !collapsed.value;
      },
    };
  },
});
</script>

<style lang="less" scoped>
.layout {
  height: fit-content;
  min-height: 100vh;
  > :deep(.ant-layout) {
    position: absolute;
    left: 200px;
    .ant-layout-content {
      width: 100%;
      overflow-y: auto;
      height: calc(100vh - 50px);
    }
  }
}
.sider {
  position: absolute;
  background: #153654;
  left: 0;
  height: 100%;
}
.layout-main {
  transition: all 0.3s;
}
.logo-container-collapsed,
.logo-container {
  display: block;
  height: 50px;
  background-color: #0d273e;
  cursor: pointer;
}
.logo-container,
.logo-container-collapsed {
  line-height: 50px;
  text-align: center;
  img {
    vertical-align: middle;
    border-style: none;
  }
  &.no-link {
    cursor: default;
  }
}

.title {
  margin: 0;
  margin-left: 24px;
  width: 176px;
  height: 58px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  line-height: 60px;
}
</style>
