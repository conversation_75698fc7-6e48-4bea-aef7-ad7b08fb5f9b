<template>
  <div id="app">
    <a-config-provider :locale="zhCN" :getPopupContainer="getPopupContainer" :theme="theme">
      <a-spin :spinning="spinning" wrapperClassName="app-loading-spin">
        <template #indicator>
          <img :src="spinImage" id="spin-image" alt="" />
        </template>
        <router-view v-if="$route.path === '/admin'" />
        <layout v-else-if="isTrue && isPreview && isDraftPreview && urlNameSlVal && isDynamicPreview">
          <router-view :key="routeViewRefreshKey" />
        </layout>
        <router-view :key="routeViewRefreshKey" v-else />
      </a-spin>
    </a-config-provider>
  </div>
</template>

<script lang="ts">
import { useStore } from 'vuex';
import { computed, defineComponent, ref } from 'vue';
import layout from '@/components/layout.vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { ConfigProvider } from 'ant-design-vue';

export default defineComponent({
  components: { layout },
  name: 'App',
  setup() {
    const store = useStore();
    const spinning = computed(() => store.state.spinning);
    const routeViewRefreshKey = computed(() => store.state.routeViewRefreshKey);
    store.dispatch('getUserInfo');
    const getPopupContainer = (el, dialogContext) => {
      if (dialogContext) {
        return dialogContext.getDialogWrap();
      } else {
        return document.body;
      }
    };
    const isTrue = ref(true);
    const isPreview = ref(true);
    const isDraftPreview = ref(true);
    const isDynamicPreview = ref(true);
    const urlNameSlVal = ref(true);
    const router = () => {
      const url = window.location.href;
      const urlName = url.split('#')[1];
      const urlNameSl = urlName.slice(0, 6);
      const path = url.split('#')[1];
      const pathAbiti = path.split('?')[0];
      const pathA = path.split('?')[0];
      const draftPath = path.split('?')[0];
      isDraftPreview.value = draftPath == '/capacity-management/draft-preview' ? false : true;
      isTrue.value = pathA == '/capacity-management/preview' ? false : true;
      isPreview.value = pathAbiti == '/capacity-management/documentsPreview' ? false : true;
      isDynamicPreview.value = pathA == '/dynamic-manage/dynamicManage-preview' ? false : true;
      urlNameSlVal.value = urlNameSl == '/home/' ? false : true;
      // 账号管理的页面不需要进行layout信息的请求
      if (path.split('?')[0] !== '/admin') {
        store.dispatch('initLayoutConfig');
      }
    };
    ConfigProvider.config({
      theme: {
        primaryColor: '#0082FF',
        errorColor: '#FF454D',
        warningColor: '#FF454D',
      },
    });
    router();
    return {
      spinImage: require('@/assets/images/loading-dynamic.gif'),
      getPopupContainer,
      router,
      routeViewRefreshKey,
      spinning,
      zhCN,
      isTrue,
      isPreview,
      isDraftPreview,
      isDynamicPreview,
      urlNameSlVal,
    };
  },
});
</script>
<style lang="less">
@import './assets/styles/theme/reset.less';
@import './assets/styles/base.less';
@import './assets/styles/global.less';
</style>
<style scoped>
#app {
  min-width: 1440px;
  color: #555555;
}
#spin-image {
  width: 412px;
  height: 412px;
  top: 80px;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}
</style>
