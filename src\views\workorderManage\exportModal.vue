<template>
  <a-modal :visible="props.visible" title="工单导出" @ok="onOk" :confirmLoading="confirmLoading">
    <a-form ref="formRef" :model="formState" name="basic" :label-col="{ span: 6 }" :wrapper-col="{ span: 16, offset: 1 }" autocomplete="off">
      <a-form-item label="创建起止时间" name="time" :rules="[{ required: true, message: '请选择起止时间' }]">
        <a-config-provider :locale="locale">
          <a-range-picker :locale="locale" style="width: 100%" v-model:value="formState.time" :disabledDate="disabledDate" />
        </a-config-provider>
      </a-form-item>
      <a-form-item label="工单状态" name="status" :rules="[{ required: true, message: '请选择工单状态' }]">
        <a-select mode="multiple" v-model:value="formState.status">
          <a-select-option :key="x.id" :value="x.id" v-for="x in tabsData">{{ x.name }}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item v-if="isQuestionManage" label="问题分类" name="category" :rules="[{ required: true, message: '请选择问题分类' }]">
        <a-select mode="multiple" v-model:value="formState.category">
          <a-select-option :key="x.categoryId" :value="x.categoryId" v-for="x in statusCatogery">{{ x.category }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item v-else label="大模型类型" name="largeModelTypes" :rules="[{ required: true, message: '请选择大模型类型' }]">
        <a-select mode="multiple" v-model:value="formState.largeModelTypes">
          <a-select-option :key="x.largeModelTypeId" :value="x.largeModelType" v-for="x in lmCatogery">{{ x.largeModelType }}</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { defineProps, defineEmits, reactive, ref, toRaw, watch, computed } from 'vue';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
dayjs.locale('zh-cn');

const emits = defineEmits(['ok']);

const formRef = ref();
const props = defineProps({
  visible: { type: Boolean },
  statusCatogery: { type: Array },
  confirmLoading: { type: Boolean },
  lmCatogery: { type: Array },
  isQuestionManage: { type: Boolean },
});
const statusCatogery = computed(() => {
  if (props.statusCatogery?.length === 0) {
    return [];
  }
  return props.statusCatogery?.filter((x) => x.category !== '全部');
});

const tabsData = ref([
  {
    id: 0,
    name: '待处理',
  },
  {
    id: 1,
    name: '处理中',
  },
  {
    id: 2,
    name: '已关闭',
  },
]);
const disabledDate = (current) => {
  return current && current > dayjs().endOf('day');
};

const formState = reactive({
  time: [],
  status: tabsData.value.map((x) => x.id),
  category: [],
  largeModelTypes: [],
});

const onOk = () => {
  formRef.value.validateFields().then((values) => {
    const data = {};
    for (const i in values) {
      data[i] = toRaw(values[i]);
    }
    emits('ok', data);
  });
};

watch(statusCatogery, () => {
  formState.category = statusCatogery.value?.map((x) => x.categoryId);
});

watch(
  () => props.lmCatogery,
  () => {
    formState.largeModelTypes = props.lmCatogery?.map((item) => item.largeModelType);
  }
);
</script>
